export declare const mockTokens: {
    readonly api_key: "ak_LCWGdaM8mv8K4PC/57IICZQXAeWfCgF30DZaFXHoGn9=";
    readonly oauth_token: "oat_8XOIucKvqHVr5tYP123456789abcdefghij";
    readonly m2m_token: "mt_8XOIucKvqHVr5tYP123456789abcdefghij";
};
export declare const mockVerificationResults: {
    api_key: {
        id: string;
        type: string;
        name: string;
        subject: string;
        claims: {
            foo: string;
        };
        scopes: string[];
        revoked: boolean;
        revocationReason: null;
        expired: boolean;
        expiration: null;
        createdBy: null;
        creationReason: null;
        secondsUntilExpiration: null;
        createdAt: number;
        updatedAt: number;
    };
    oauth_token: {
        id: string;
        clientId: string;
        type: string;
        name: string;
        subject: string;
        scopes: string[];
        revoked: boolean;
        revocationReason: null;
        expired: boolean;
        expiration: null;
        createdAt: number;
        updatedAt: number;
    };
    m2m_token: {
        id: string;
        subject: string;
        scopes: string[];
        claims: {
            foo: string;
        };
        revoked: boolean;
        revocationReason: null;
        expired: boolean;
        expiration: null;
        creationReason: null;
        createdAt: number;
        updatedAt: number;
    };
};
export declare const mockMachineAuthResponses: {
    readonly api_key: {
        readonly endpoint: "https://api.clerk.test/api_keys/verify";
        readonly errorMessage: "API key not found";
    };
    readonly oauth_token: {
        readonly endpoint: "https://api.clerk.test/oauth_applications/access_tokens/verify";
        readonly errorMessage: "OAuth token not found";
    };
    readonly m2m_token: {
        readonly endpoint: "https://api.clerk.test/m2m_tokens/verify";
        readonly errorMessage: "Machine token not found";
    };
};
//# sourceMappingURL=machine.d.ts.map