'use client'

import { useUser } from '@clerk/nextjs'
import { useState, useEffect } from 'react'
import { storageUtils } from '@/lib/storage'
import { User, Message, FriendRequest } from '@/lib/types'
import UserSearch from '@/components/UserSearch'
import MessageList from '@/components/MessageList'
import FriendRequestCard from '@/components/FriendRequestCard'
import Navigation from '@/components/Navigation'

export default function Dashboard() {
  const { user } = useUser()
  const [activeTab, setActiveTab] = useState<'messages' | 'search' | 'friends'>('messages')
  const [users, setUsers] = useState<User[]>([])
  const [messages, setMessages] = useState<Message[]>([])
  const [friendRequests, setFriendRequests] = useState<FriendRequest[]>([])

  useEffect(() => {
    if (user) {
      // Load data from APIs
      loadMessages()
      loadFriendRequests()
      loadUsersForMessages()

      // Set up polling to check for new data every 5 seconds
      const interval = setInterval(() => {
        loadMessages()
        loadFriendRequests()
      }, 5000)

      return () => clearInterval(interval)
    }
  }, [user])

  const loadMessages = async () => {
    try {
      const response = await fetch('/api/messages')
      if (response.ok) {
        const data = await response.json()
        setMessages(data.messages)
      }
    } catch (error) {
      console.error('Error loading messages:', error)
    }
  }

  const loadFriendRequests = async () => {
    try {
      const response = await fetch('/api/friend-requests')
      if (response.ok) {
        const data = await response.json()
        setFriendRequests(data.friendRequests)
      }
    } catch (error) {
      console.error('Error loading friend requests:', error)
    }
  }

  const loadUsersForMessages = async () => {
    try {
      const response = await fetch('/api/users')
      if (response.ok) {
        const data = await response.json()
        setUsers(data.users)
      }
    } catch (error) {
      console.error('Error loading users:', error)
    }
  }

  const handleSendMessage = async (receiverId: string, content: string) => {
    if (!user) return

    try {
      const response = await fetch('/api/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ receiverId, content }),
      })

      if (response.ok) {
        // Reload messages to get the updated list
        loadMessages()
      } else {
        console.error('Failed to send message')
      }
    } catch (error) {
      console.error('Error sending message:', error)
    }
  }

  const handleSendFriendRequest = async (receiverId: string) => {
    if (!user) return

    try {
      const response = await fetch('/api/friend-requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ receiverId }),
      })

      if (response.ok) {
        // Reload friend requests to get the updated list
        loadFriendRequests()
      } else {
        const errorData = await response.json()
        console.error('Failed to send friend request:', errorData.error)
      }
    } catch (error) {
      console.error('Error sending friend request:', error)
    }
  }

  const handleFriendRequestResponse = async (requestId: string, status: 'accepted' | 'rejected') => {
    try {
      const response = await fetch('/api/friend-requests', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ requestId, status }),
      })

      if (response.ok) {
        // Reload friend requests to get the updated list
        loadFriendRequests()
      } else {
        console.error('Failed to update friend request')
      }
    } catch (error) {
      console.error('Error updating friend request:', error)
    }
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Loading...</h2>
        </div>
      </div>
    )
  }

  const receivedFriendRequests = friendRequests.filter(
    req => req.receiverId === user.id && req.status === 'pending'
  )

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation 
        activeTab={activeTab} 
        setActiveTab={setActiveTab}
        pendingRequestsCount={receivedFriendRequests.length}
      />
      
      <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow">
          {activeTab === 'messages' && (
            <MessageList 
              currentUserId={user.id}
              users={users}
              messages={messages}
              onSendMessage={handleSendMessage}
            />
          )}
          
          {activeTab === 'search' && (
            <UserSearch 
              currentUserId={user.id}
              users={users}
              friendRequests={friendRequests}
              onSendFriendRequest={handleSendFriendRequest}
              onSendMessage={handleSendMessage}
            />
          )}
          
          {activeTab === 'friends' && (
            <div className="p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Friend Requests</h2>
              {receivedFriendRequests.length === 0 ? (
                <p className="text-gray-500 text-center py-8">No pending friend requests</p>
              ) : (
                <div className="space-y-4">
                  {receivedFriendRequests.map(request => (
                    <FriendRequestCard
                      key={request.id}
                      request={request}
                      users={users}
                      onResponse={handleFriendRequestResponse}
                    />
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
