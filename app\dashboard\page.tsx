'use client'

import { useUser } from '@clerk/nextjs'
import { useState, useEffect } from 'react'
import { storageUtils } from '@/lib/storage'
import { User, Message, FriendRequest } from '@/lib/types'
import UserSearch from '@/components/UserSearch'
import MessageList from '@/components/MessageList'
import FriendRequestCard from '@/components/FriendRequestCard'
import Navigation from '@/components/Navigation'

export default function Dashboard() {
  const { user } = useUser()
  const [activeTab, setActiveTab] = useState<'messages' | 'search' | 'friends'>('messages')
  const [users, setUsers] = useState<User[]>([])
  const [messages, setMessages] = useState<Message[]>([])
  const [friendRequests, setFriendRequests] = useState<FriendRequest[]>([])

  useEffect(() => {
    if (user) {
      // Load data
      setMessages(storageUtils.getMessages())
      setFriendRequests(storageUtils.getFriendRequests())

      // Load users from API for message list
      loadUsersForMessages()
    }
  }, [user])

  const loadUsersForMessages = async () => {
    try {
      const response = await fetch('/api/users')
      if (response.ok) {
        const data = await response.json()
        setUsers(data.users)
      }
    } catch (error) {
      console.error('Error loading users:', error)
    }
  }

  const handleSendMessage = (receiverId: string, content: string) => {
    if (!user) return
    
    const newMessage: Message = {
      id: Date.now().toString(),
      senderId: user.id,
      receiverId,
      content,
      timestamp: Date.now(),
      read: false
    }
    
    storageUtils.addMessage(newMessage)
    setMessages(storageUtils.getMessages())
  }

  const handleSendFriendRequest = (receiverId: string) => {
    if (!user) return
    
    const existingRequest = friendRequests.find(
      req => req.senderId === user.id && req.receiverId === receiverId && req.status === 'pending'
    )
    
    if (existingRequest) return
    
    const newRequest: FriendRequest = {
      id: Date.now().toString(),
      senderId: user.id,
      receiverId,
      status: 'pending',
      timestamp: Date.now()
    }
    
    storageUtils.addFriendRequest(newRequest)
    setFriendRequests(storageUtils.getFriendRequests())
  }

  const handleFriendRequestResponse = (requestId: string, status: 'accepted' | 'rejected') => {
    storageUtils.updateFriendRequest(requestId, status)
    setFriendRequests(storageUtils.getFriendRequests())
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Loading...</h2>
        </div>
      </div>
    )
  }

  const receivedFriendRequests = friendRequests.filter(
    req => req.receiverId === user.id && req.status === 'pending'
  )

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation 
        activeTab={activeTab} 
        setActiveTab={setActiveTab}
        pendingRequestsCount={receivedFriendRequests.length}
      />
      
      <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow">
          {activeTab === 'messages' && (
            <MessageList 
              currentUserId={user.id}
              users={users}
              messages={messages}
              onSendMessage={handleSendMessage}
            />
          )}
          
          {activeTab === 'search' && (
            <UserSearch 
              currentUserId={user.id}
              users={users}
              friendRequests={friendRequests}
              onSendFriendRequest={handleSendFriendRequest}
              onSendMessage={handleSendMessage}
            />
          )}
          
          {activeTab === 'friends' && (
            <div className="p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Friend Requests</h2>
              {receivedFriendRequests.length === 0 ? (
                <p className="text-gray-500 text-center py-8">No pending friend requests</p>
              ) : (
                <div className="space-y-4">
                  {receivedFriendRequests.map(request => (
                    <FriendRequestCard
                      key={request.id}
                      request={request}
                      users={users}
                      onResponse={handleFriendRequestResponse}
                    />
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
