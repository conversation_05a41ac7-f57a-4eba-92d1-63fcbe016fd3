{"version": 3, "sources": ["../../../../src/server/fs/utils.ts"], "sourcesContent": ["/**\n * Attention: Only import this module when the node runtime is used.\n * We are using conditional imports to mitigate bundling issues with Next.js server actions on version prior to 14.1.0.\n */\nimport nodeRuntime from '#safe-node-apis';\n\n// Generic assertion function that acts as a proper type guard\nfunction assertNotNullable<T>(value: T, moduleName: string): asserts value is NonNullable<T> {\n  if (!value) {\n    throw new Error(`Clerk: ${moduleName} is missing. This is an internal error. Please contact Clerk's support.`);\n  }\n}\n\nconst nodeFsOrThrow = (): NonNullable<typeof nodeRuntime.fs> => {\n  assertNotNullable(nodeRuntime.fs, 'fs');\n  return nodeRuntime.fs;\n};\n\nconst nodePathOrThrow = (): NonNullable<typeof nodeRuntime.path> => {\n  assertNotNullable(nodeRuntime.path, 'path');\n  return nodeRuntime.path;\n};\n\nconst nodeCwdOrThrow = (): NonNullable<typeof nodeRuntime.cwd> => {\n  assertNotNullable(nodeRuntime.cwd, 'cwd');\n  return nodeRuntime.cwd;\n};\n\nexport { nodeCwdOrThrow, nodeFsOrThrow, nodePathOrThrow };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,4BAAwB;AAGxB,SAAS,kBAAqB,OAAU,YAAqD;AAC3F,MAAI,CAAC,OAAO;AACV,UAAM,IAAI,MAAM,UAAU,UAAU,yEAAyE;AAAA,EAC/G;AACF;AAEA,MAAM,gBAAgB,MAA0C;AAC9D,oBAAkB,sBAAAA,QAAY,IAAI,IAAI;AACtC,SAAO,sBAAAA,QAAY;AACrB;AAEA,MAAM,kBAAkB,MAA4C;AAClE,oBAAkB,sBAAAA,QAAY,MAAM,MAAM;AAC1C,SAAO,sBAAAA,QAAY;AACrB;AAEA,MAAM,iBAAiB,MAA2C;AAChE,oBAAkB,sBAAAA,QAAY,KAAK,KAAK;AACxC,SAAO,sBAAAA,QAAY;AACrB;", "names": ["nodeRuntime"]}