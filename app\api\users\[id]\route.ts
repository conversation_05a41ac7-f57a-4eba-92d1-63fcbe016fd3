import { clerkClient } from '@clerk/nextjs/server'
import { auth } from '@clerk/nextjs/server'
import { NextResponse } from 'next/server'

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const user = await clerkClient.users.getUser(params.id)

    const userData = {
      id: user.id,
      username: user.username || user.emailAddresses[0]?.emailAddress?.split('@')[0] || 'user',
      firstName: user.firstName || '',
      lastName: user.lastName || '',
      imageUrl: user.imageUrl,
      emailAddress: user.emailAddresses[0]?.emailAddress || ''
    }

    return NextResponse.json({ user: userData })
  } catch (error) {
    console.error('Error fetching user:', error)
    return NextResponse.json({ error: 'User not found' }, { status: 404 })
  }
}
