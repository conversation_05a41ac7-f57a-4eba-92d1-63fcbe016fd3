'use client'

import { useState } from 'react'
import { User, FriendRequest } from '@/lib/types'

interface UserSearchProps {
  currentUserId: string
  users: User[]
  friendRequests: FriendRequest[]
  onSendFriendRequest: (receiverId: string) => void
  onSendMessage: (receiverId: string, content: string) => void
}

export default function UserSearch({
  currentUserId,
  users,
  friendRequests,
  onSendFriendRequest,
  onSendMessage
}: UserSearchProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<User[]>([])
  const [messageContent, setMessageContent] = useState<{ [key: string]: string }>({})
  const [isLoading, setIsLoading] = useState(false)

  const handleSearch = async (query: string) => {
    setSearchQuery(query)
    if (query.trim()) {
      setIsLoading(true)
      try {
        const response = await fetch(`/api/users?q=${encodeURIComponent(query)}`)
        if (response.ok) {
          const data = await response.json()
          setSearchResults(data.users)
        } else {
          console.error('Failed to search users')
          setSearchResults([])
        }
      } catch (error) {
        console.error('Error searching users:', error)
        setSearchResults([])
      } finally {
        setIsLoading(false)
      }
    } else {
      setSearchResults([])
    }
  }

  const getFriendRequestStatus = (userId: string) => {
    const sentRequest = friendRequests.find(
      req => req.senderId === currentUserId && req.receiverId === userId
    )
    const receivedRequest = friendRequests.find(
      req => req.senderId === userId && req.receiverId === currentUserId
    )
    
    if (sentRequest) return sentRequest.status
    if (receivedRequest) return 'received'
    return null
  }

  const handleSendMessage = (receiverId: string) => {
    const content = messageContent[receiverId]
    if (content?.trim()) {
      onSendMessage(receiverId, content.trim())
      setMessageContent(prev => ({ ...prev, [receiverId]: '' }))
    }
  }

  return (
    <div className="p-6">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">Search Users</h2>
      
      <div className="mb-6">
        <input
          type="text"
          placeholder="Search by username, first name, or last name..."
          value={searchQuery}
          onChange={(e) => handleSearch(e.target.value)}
          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      {searchQuery && !isLoading && searchResults.length === 0 && (
        <p className="text-gray-500 text-center py-8">No users found matching "{searchQuery}"</p>
      )}

      {isLoading && (
        <div className="text-center py-8">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p className="text-gray-500 mt-2">Searching users...</p>
        </div>
      )}

      <div className="space-y-4">
        {searchResults.map(user => {
          const friendStatus = getFriendRequestStatus(user.id)
          
          return (
            <div key={user.id} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-start space-x-4">
                <img
                  src={user.imageUrl || `https://ui-avatars.com/api/?name=${user.firstName}+${user.lastName}&background=random`}
                  alt={`${user.firstName} ${user.lastName}`}
                  className="w-12 h-12 rounded-full object-cover"
                />
                
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">
                        {user.firstName} {user.lastName}
                      </h3>
                      <p className="text-sm text-gray-500">@{user.username}</p>
                      {user.emailAddress && (
                        <p className="text-sm text-gray-500">{user.emailAddress}</p>
                      )}
                    </div>
                    
                    <div className="flex space-x-2">
                      {friendStatus === 'pending' && (
                        <span className="px-3 py-1 bg-yellow-100 text-yellow-800 text-sm rounded-full">
                          Request Sent
                        </span>
                      )}
                      {friendStatus === 'accepted' && (
                        <span className="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">
                          Friends
                        </span>
                      )}
                      {friendStatus === 'received' && (
                        <span className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                          Sent You Request
                        </span>
                      )}
                      {!friendStatus && (
                        <button
                          onClick={() => onSendFriendRequest(user.id)}
                          className="px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
                        >
                          Add Friend
                        </button>
                      )}
                    </div>
                  </div>
                  
                  <div className="mt-4">
                    <div className="flex space-x-2">
                      <input
                        type="text"
                        placeholder="Type a message..."
                        value={messageContent[user.id] || ''}
                        onChange={(e) => setMessageContent(prev => ({ 
                          ...prev, 
                          [user.id]: e.target.value 
                        }))}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            handleSendMessage(user.id)
                          }
                        }}
                      />
                      <button
                        onClick={() => handleSendMessage(user.id)}
                        disabled={!messageContent[user.id]?.trim()}
                        className="px-4 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
                      >
                        Send
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
