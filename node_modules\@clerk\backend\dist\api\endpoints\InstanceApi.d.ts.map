{"version": 3, "file": "InstanceApi.d.ts", "sourceRoot": "", "sources": ["../../../src/api/endpoints/InstanceApi.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AACtD,OAAO,KAAK,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AAC9E,OAAO,KAAK,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AAC9E,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAI5C,KAAK,YAAY,GAAG;IAClB;;;;OAIG;IACH,QAAQ,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;IACtC;;OAEG;IACH,IAAI,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;IAClC;;;;OAIG;IACH,2BAA2B,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;IACzD,YAAY,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACzC,cAAc,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IAC3C,iBAAiB,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IAC9C;;;;OAIG;IACH,cAAc,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC;IAC3C;;OAEG;IACH,sBAAsB,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;CACrD,CAAC;AAEF,KAAK,wBAAwB,GAAG;IAC9B,SAAS,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;IACvC,SAAS,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;IACvC,sBAAsB,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;IACpD,2BAA2B,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;IACzD,2BAA2B,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;CAC1D,CAAC;AAEF,KAAK,gCAAgC,GAAG;IACtC,OAAO,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;IACrC,qBAAqB,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IAClD,kBAAkB,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;IAChD,cAAc,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;IAC5C;;;;OAIG;IACH,sBAAsB,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC;IACnD;;OAEG;IACH,aAAa,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IAC1C;;OAEG;IACH,oBAAoB,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;CAClD,CAAC;AAEF,qBAAa,WAAY,SAAQ,WAAW;IAC7B,GAAG;IAOH,MAAM,CAAC,MAAM,EAAE,YAAY;IAQ3B,kBAAkB,CAAC,MAAM,EAAE,wBAAwB;IAQnD,0BAA0B,CAAC,MAAM,EAAE,gCAAgC;CAOjF"}