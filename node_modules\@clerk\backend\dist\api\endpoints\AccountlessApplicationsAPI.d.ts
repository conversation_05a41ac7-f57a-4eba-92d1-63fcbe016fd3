import type { AccountlessApplication } from '../resources/AccountlessApplication';
import { AbstractAPI } from './AbstractApi';
export declare class AccountlessApplicationAPI extends AbstractAPI {
    createAccountlessApplication(params?: {
        requestHeaders?: Headers;
    }): Promise<AccountlessApplication>;
    completeAccountlessApplicationOnboarding(params?: {
        requestHeaders?: Headers;
    }): Promise<AccountlessApplication>;
}
//# sourceMappingURL=AccountlessApplicationsAPI.d.ts.map