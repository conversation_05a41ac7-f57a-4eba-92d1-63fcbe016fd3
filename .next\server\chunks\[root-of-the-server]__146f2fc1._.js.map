{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/lib/userStorage.ts"], "sourcesContent": ["// Simple in-memory storage for demo purposes\n// In production, you'd use a real database like PostgreSQL, MongoDB, etc.\n\ninterface RegisteredUser {\n  id: string\n  username: string\n  firstName: string\n  lastName: string\n  imageUrl?: string\n  emailAddress: string\n}\n\nclass UserStorage {\n  private users: RegisteredUser[] = [\n    // Add some demo users for testing\n    {\n      id: 'demo_alice',\n      username: 'alice_wonder',\n      firstName: 'Alice',\n      lastName: 'Wonder',\n      imageUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',\n      emailAddress: '<EMAIL>'\n    },\n    {\n      id: 'demo_bob',\n      username: 'bob_builder',\n      firstName: 'Bob',\n      lastName: 'Builder',\n      imageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',\n      emailAddress: '<EMAIL>'\n    },\n    {\n      id: 'demo_charlie',\n      username: 'charlie_brown',\n      firstName: '<PERSON>',\n      lastName: '<PERSON>',\n      imageUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',\n      emailAddress: '<EMAIL>'\n    }\n  ]\n\n  addUser(user: RegisteredUser) {\n    const existingIndex = this.users.findIndex(u => u.id === user.id)\n    if (existingIndex !== -1) {\n      this.users[existingIndex] = user\n    } else {\n      this.users.push(user)\n    }\n  }\n\n  getUser(id: string): RegisteredUser | undefined {\n    return this.users.find(u => u.id === id)\n  }\n\n  getAllUsers(): RegisteredUser[] {\n    return this.users\n  }\n\n  searchUsers(query: string, excludeUserId?: string): RegisteredUser[] {\n    return this.users\n      .filter(user => excludeUserId ? user.id !== excludeUserId : true)\n      .filter(user => {\n        if (!query) return true\n        \n        const searchTerm = query.toLowerCase()\n        const username = user.username?.toLowerCase() || ''\n        const firstName = user.firstName?.toLowerCase() || ''\n        const lastName = user.lastName?.toLowerCase() || ''\n        const email = user.emailAddress?.toLowerCase() || ''\n        \n        return username.includes(searchTerm) || \n               firstName.includes(searchTerm) || \n               lastName.includes(searchTerm) ||\n               email.includes(searchTerm)\n      })\n  }\n}\n\n// Export a singleton instance\nexport const userStorage = new UserStorage()\nexport type { RegisteredUser }\n"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,0EAA0E;;;;;AAW1E,MAAM;IACI,QAA0B;QAChC,kCAAkC;QAClC;YACE,IAAI;YACJ,UAAU;YACV,WAAW;YACX,UAAU;YACV,UAAU;YACV,cAAc;QAChB;QACA;YACE,IAAI;YACJ,UAAU;YACV,WAAW;YACX,UAAU;YACV,UAAU;YACV,cAAc;QAChB;QACA;YACE,IAAI;YACJ,UAAU;YACV,WAAW;YACX,UAAU;YACV,UAAU;YACV,cAAc;QAChB;KACD,CAAA;IAED,QAAQ,IAAoB,EAAE;QAC5B,MAAM,gBAAgB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE;QAChE,IAAI,kBAAkB,CAAC,GAAG;YACxB,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG;QAC9B,OAAO;YACL,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;QAClB;IACF;IAEA,QAAQ,EAAU,EAA8B;QAC9C,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACvC;IAEA,cAAgC;QAC9B,OAAO,IAAI,CAAC,KAAK;IACnB;IAEA,YAAY,KAAa,EAAE,aAAsB,EAAoB;QACnE,OAAO,IAAI,CAAC,KAAK,CACd,MAAM,CAAC,CAAA,OAAQ,gBAAgB,KAAK,EAAE,KAAK,gBAAgB,MAC3D,MAAM,CAAC,CAAA;YACN,IAAI,CAAC,OAAO,OAAO;YAEnB,MAAM,aAAa,MAAM,WAAW;YACpC,MAAM,WAAW,KAAK,QAAQ,EAAE,iBAAiB;YACjD,MAAM,YAAY,KAAK,SAAS,EAAE,iBAAiB;YACnD,MAAM,WAAW,KAAK,QAAQ,EAAE,iBAAiB;YACjD,MAAM,QAAQ,KAAK,YAAY,EAAE,iBAAiB;YAElD,OAAO,SAAS,QAAQ,CAAC,eAClB,UAAU,QAAQ,CAAC,eACnB,SAAS,QAAQ,CAAC,eAClB,MAAM,QAAQ,CAAC;QACxB;IACJ;AACF;AAGO,MAAM,cAAc,IAAI", "debugId": null}}, {"offset": {"line": 132, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/app/api/users/route.ts"], "sourcesContent": ["import { auth, currentUser } from '@clerk/nextjs/server'\nimport { NextResponse } from 'next/server'\nimport { userStorage } from '@/lib/userStorage'\n\nexport async function GET(request: Request) {\n  try {\n    const { userId } = await auth()\n\n    if (!userId) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const { searchParams } = new URL(request.url)\n    const query = searchParams.get('q') || ''\n\n    // Search users using the storage service\n    const filteredUsers = userStorage.searchUsers(query, userId)\n\n    return NextResponse.json({ users: filteredUsers })\n  } catch (error) {\n    console.error('Error fetching users:', error)\n    return NextResponse.json({ error: 'Failed to fetch users' }, { status: 500 })\n  }\n}\n\nexport async function POST(request: Request) {\n  try {\n    const { userId } = await auth()\n\n    if (!userId) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const user = await currentUser()\n\n    if (!user) {\n      return NextResponse.json({ error: 'User not found' }, { status: 404 })\n    }\n\n    const userData = {\n      id: user.id,\n      username: user.username || user.emailAddresses[0]?.emailAddress?.split('@')[0] || 'user',\n      firstName: user.firstName || '',\n      lastName: user.lastName || '',\n      imageUrl: user.imageUrl,\n      emailAddress: user.emailAddresses[0]?.emailAddress || ''\n    }\n\n    // Add user to storage\n    userStorage.addUser(userData)\n\n    return NextResponse.json({ user: userData })\n  } catch (error) {\n    console.error('Error registering user:', error)\n    return NextResponse.json({ error: 'Failed to register user' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AACA;AACA;;;;AAEO,eAAe,IAAI,OAAgB;IACxC,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAA,yMAAI;QAE7B,IAAI,CAAC,QAAQ;YACX,OAAO,4JAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,QAAQ,aAAa,GAAG,CAAC,QAAQ;QAEvC,yCAAyC;QACzC,MAAM,gBAAgB,+IAAW,CAAC,WAAW,CAAC,OAAO;QAErD,OAAO,4JAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAc;IAClD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,4JAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF;AAEO,eAAe,KAAK,OAAgB;IACzC,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAA,yMAAI;QAE7B,IAAI,CAAC,QAAQ;YACX,OAAO,4JAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,OAAO,MAAM,IAAA,uNAAW;QAE9B,IAAI,CAAC,MAAM;YACT,OAAO,4JAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAiB,GAAG;gBAAE,QAAQ;YAAI;QACtE;QAEA,MAAM,WAAW;YACf,IAAI,KAAK,EAAE;YACX,UAAU,KAAK,QAAQ,IAAI,KAAK,cAAc,CAAC,EAAE,EAAE,cAAc,MAAM,IAAI,CAAC,EAAE,IAAI;YAClF,WAAW,KAAK,SAAS,IAAI;YAC7B,UAAU,KAAK,QAAQ,IAAI;YAC3B,UAAU,KAAK,QAAQ;YACvB,cAAc,KAAK,cAAc,CAAC,EAAE,EAAE,gBAAgB;QACxD;QAEA,sBAAsB;QACtB,+IAAW,CAAC,OAAO,CAAC;QAEpB,OAAO,4JAAY,CAAC,IAAI,CAAC;YAAE,MAAM;QAAS;IAC5C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,4JAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAA0B,GAAG;YAAE,QAAQ;QAAI;IAC/E;AACF", "debugId": null}}]}