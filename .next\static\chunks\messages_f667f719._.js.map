{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/lib/storage.ts"], "sourcesContent": ["import { User, Message, FriendRequest, Conversation, AppState } from './types';\n\nconst STORAGE_KEYS = {\n  USERS: 'messaging_app_users',\n  MESSAGES: 'messaging_app_messages',\n  FRIEND_REQUESTS: 'messaging_app_friend_requests',\n  CONVERSATIONS: 'messaging_app_conversations',\n};\n\n// Initialize storage with some demo data\nconst initializeStorage = () => {\n  if (typeof window === 'undefined') return;\n  \n  // Initialize users if not exists\n  if (!localStorage.getItem(STORAGE_KEYS.USERS)) {\n    const demoUsers: User[] = [\n      {\n        id: 'demo1',\n        username: 'alice_wonder',\n        firstName: 'Alice',\n        lastName: 'Wonder',\n        imageUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',\n        emailAddress: '<EMAIL>'\n      },\n      {\n        id: 'demo2',\n        username: 'bob_builder',\n        firstName: 'Bob',\n        lastName: 'Builder',\n        imageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',\n        emailAddress: '<EMAIL>'\n      },\n      {\n        id: 'demo3',\n        username: 'charlie_brown',\n        firstName: 'Charlie',\n        lastName: 'Brown',\n        imageUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',\n        emailAddress: '<EMAIL>'\n      }\n    ];\n    localStorage.setItem(STORAGE_KEYS.USERS, JSON.stringify(demoUsers));\n  }\n  \n  // Initialize other storage items\n  if (!localStorage.getItem(STORAGE_KEYS.MESSAGES)) {\n    localStorage.setItem(STORAGE_KEYS.MESSAGES, JSON.stringify([]));\n  }\n  if (!localStorage.getItem(STORAGE_KEYS.FRIEND_REQUESTS)) {\n    localStorage.setItem(STORAGE_KEYS.FRIEND_REQUESTS, JSON.stringify([]));\n  }\n  if (!localStorage.getItem(STORAGE_KEYS.CONVERSATIONS)) {\n    localStorage.setItem(STORAGE_KEYS.CONVERSATIONS, JSON.stringify([]));\n  }\n};\n\nexport const storageUtils = {\n  // Users\n  getUsers: (): User[] => {\n    if (typeof window === 'undefined') return [];\n    initializeStorage();\n    const users = localStorage.getItem(STORAGE_KEYS.USERS);\n    return users ? JSON.parse(users) : [];\n  },\n\n  addUser: (user: User): void => {\n    if (typeof window === 'undefined') return;\n    const users = storageUtils.getUsers();\n    const existingUser = users.find(u => u.id === user.id);\n    if (!existingUser) {\n      users.push(user);\n      localStorage.setItem(STORAGE_KEYS.USERS, JSON.stringify(users));\n    }\n  },\n\n  // Messages\n  getMessages: (): Message[] => {\n    if (typeof window === 'undefined') return [];\n    const messages = localStorage.getItem(STORAGE_KEYS.MESSAGES);\n    return messages ? JSON.parse(messages) : [];\n  },\n\n  addMessage: (message: Message): void => {\n    if (typeof window === 'undefined') return;\n    const messages = storageUtils.getMessages();\n    messages.push(message);\n    localStorage.setItem(STORAGE_KEYS.MESSAGES, JSON.stringify(messages));\n  },\n\n  getConversationMessages: (userId1: string, userId2: string): Message[] => {\n    const messages = storageUtils.getMessages();\n    return messages.filter(\n      msg => \n        (msg.senderId === userId1 && msg.receiverId === userId2) ||\n        (msg.senderId === userId2 && msg.receiverId === userId1)\n    ).sort((a, b) => a.timestamp - b.timestamp);\n  },\n\n  // Friend Requests\n  getFriendRequests: (): FriendRequest[] => {\n    if (typeof window === 'undefined') return [];\n    const requests = localStorage.getItem(STORAGE_KEYS.FRIEND_REQUESTS);\n    return requests ? JSON.parse(requests) : [];\n  },\n\n  addFriendRequest: (request: FriendRequest): void => {\n    if (typeof window === 'undefined') return;\n    const requests = storageUtils.getFriendRequests();\n    requests.push(request);\n    localStorage.setItem(STORAGE_KEYS.FRIEND_REQUESTS, JSON.stringify(requests));\n  },\n\n  updateFriendRequest: (requestId: string, status: 'accepted' | 'rejected'): void => {\n    if (typeof window === 'undefined') return;\n    const requests = storageUtils.getFriendRequests();\n    const requestIndex = requests.findIndex(r => r.id === requestId);\n    if (requestIndex !== -1) {\n      requests[requestIndex].status = status;\n      localStorage.setItem(STORAGE_KEYS.FRIEND_REQUESTS, JSON.stringify(requests));\n    }\n  },\n\n  // Conversations\n  getConversations: (): Conversation[] => {\n    if (typeof window === 'undefined') return [];\n    const conversations = localStorage.getItem(STORAGE_KEYS.CONVERSATIONS);\n    return conversations ? JSON.parse(conversations) : [];\n  },\n\n  updateConversation: (conversation: Conversation): void => {\n    if (typeof window === 'undefined') return;\n    const conversations = storageUtils.getConversations();\n    const existingIndex = conversations.findIndex(c => c.id === conversation.id);\n    if (existingIndex !== -1) {\n      conversations[existingIndex] = conversation;\n    } else {\n      conversations.push(conversation);\n    }\n    localStorage.setItem(STORAGE_KEYS.CONVERSATIONS, JSON.stringify(conversations));\n  },\n\n  // Search users\n  searchUsers: (query: string): User[] => {\n    const users = storageUtils.getUsers();\n    return users.filter(user => \n      user.username.toLowerCase().includes(query.toLowerCase()) ||\n      user.firstName?.toLowerCase().includes(query.toLowerCase()) ||\n      user.lastName?.toLowerCase().includes(query.toLowerCase())\n    );\n  }\n};\n\n// Initialize storage on import\nif (typeof window !== 'undefined') {\n  initializeStorage();\n}\n"], "names": [], "mappings": ";;;;AAEA,MAAM,eAAe;IACnB,OAAO;IACP,UAAU;IACV,iBAAiB;IACjB,eAAe;AACjB;AAEA,yCAAyC;AACzC,MAAM,oBAAoB;IACxB;;IAEA,iCAAiC;IACjC,IAAI,CAAC,aAAa,OAAO,CAAC,aAAa,KAAK,GAAG;QAC7C,MAAM,YAAoB;YACxB;gBACE,IAAI;gBACJ,UAAU;gBACV,WAAW;gBACX,UAAU;gBACV,UAAU;gBACV,cAAc;YAChB;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,WAAW;gBACX,UAAU;gBACV,UAAU;gBACV,cAAc;YAChB;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,WAAW;gBACX,UAAU;gBACV,UAAU;gBACV,cAAc;YAChB;SACD;QACD,aAAa,OAAO,CAAC,aAAa,KAAK,EAAE,KAAK,SAAS,CAAC;IAC1D;IAEA,iCAAiC;IACjC,IAAI,CAAC,aAAa,OAAO,CAAC,aAAa,QAAQ,GAAG;QAChD,aAAa,OAAO,CAAC,aAAa,QAAQ,EAAE,KAAK,SAAS,CAAC,EAAE;IAC/D;IACA,IAAI,CAAC,aAAa,OAAO,CAAC,aAAa,eAAe,GAAG;QACvD,aAAa,OAAO,CAAC,aAAa,eAAe,EAAE,KAAK,SAAS,CAAC,EAAE;IACtE;IACA,IAAI,CAAC,aAAa,OAAO,CAAC,aAAa,aAAa,GAAG;QACrD,aAAa,OAAO,CAAC,aAAa,aAAa,EAAE,KAAK,SAAS,CAAC,EAAE;IACpE;AACF;AAEO,MAAM,eAAe;IAC1B,QAAQ;IACR,UAAU;QACR;;QACA;QACA,MAAM,QAAQ,aAAa,OAAO,CAAC,aAAa,KAAK;QACrD,OAAO,QAAQ,KAAK,KAAK,CAAC,SAAS,EAAE;IACvC;IAEA,SAAS,CAAC;QACR;;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,MAAM,eAAe,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE;QACrD,IAAI,CAAC,cAAc;YACjB,MAAM,IAAI,CAAC;YACX,aAAa,OAAO,CAAC,aAAa,KAAK,EAAE,KAAK,SAAS,CAAC;QAC1D;IACF;IAEA,WAAW;IACX,aAAa;QACX;;QACA,MAAM,WAAW,aAAa,OAAO,CAAC,aAAa,QAAQ;QAC3D,OAAO,WAAW,KAAK,KAAK,CAAC,YAAY,EAAE;IAC7C;IAEA,YAAY,CAAC;QACX;;QACA,MAAM,WAAW,aAAa,WAAW;QACzC,SAAS,IAAI,CAAC;QACd,aAAa,OAAO,CAAC,aAAa,QAAQ,EAAE,KAAK,SAAS,CAAC;IAC7D;IAEA,yBAAyB,CAAC,SAAiB;QACzC,MAAM,WAAW,aAAa,WAAW;QACzC,OAAO,SAAS,MAAM,CACpB,CAAA,MACE,AAAC,IAAI,QAAQ,KAAK,WAAW,IAAI,UAAU,KAAK,WAC/C,IAAI,QAAQ,KAAK,WAAW,IAAI,UAAU,KAAK,SAClD,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;IAC5C;IAEA,kBAAkB;IAClB,mBAAmB;QACjB;;QACA,MAAM,WAAW,aAAa,OAAO,CAAC,aAAa,eAAe;QAClE,OAAO,WAAW,KAAK,KAAK,CAAC,YAAY,EAAE;IAC7C;IAEA,kBAAkB,CAAC;QACjB;;QACA,MAAM,WAAW,aAAa,iBAAiB;QAC/C,SAAS,IAAI,CAAC;QACd,aAAa,OAAO,CAAC,aAAa,eAAe,EAAE,KAAK,SAAS,CAAC;IACpE;IAEA,qBAAqB,CAAC,WAAmB;QACvC;;QACA,MAAM,WAAW,aAAa,iBAAiB;QAC/C,MAAM,eAAe,SAAS,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACtD,IAAI,iBAAiB,CAAC,GAAG;YACvB,QAAQ,CAAC,aAAa,CAAC,MAAM,GAAG;YAChC,aAAa,OAAO,CAAC,aAAa,eAAe,EAAE,KAAK,SAAS,CAAC;QACpE;IACF;IAEA,gBAAgB;IAChB,kBAAkB;QAChB;;QACA,MAAM,gBAAgB,aAAa,OAAO,CAAC,aAAa,aAAa;QACrE,OAAO,gBAAgB,KAAK,KAAK,CAAC,iBAAiB,EAAE;IACvD;IAEA,oBAAoB,CAAC;QACnB;;QACA,MAAM,gBAAgB,aAAa,gBAAgB;QACnD,MAAM,gBAAgB,cAAc,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,aAAa,EAAE;QAC3E,IAAI,kBAAkB,CAAC,GAAG;YACxB,aAAa,CAAC,cAAc,GAAG;QACjC,OAAO;YACL,cAAc,IAAI,CAAC;QACrB;QACA,aAAa,OAAO,CAAC,aAAa,aAAa,EAAE,KAAK,SAAS,CAAC;IAClE;IAEA,eAAe;IACf,aAAa,CAAC;QACZ,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,MAAM,MAAM,CAAC,CAAA;gBAElB,iBACA;mBAFA,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW,SACtD,kBAAA,KAAK,SAAS,cAAd,sCAAA,gBAAgB,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW,UACxD,iBAAA,KAAK,QAAQ,cAAb,qCAAA,eAAe,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW;;IAE3D;AACF;AAEA,+BAA+B;AAC/B,wCAAmC;IACjC;AACF", "debugId": null}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/components/UserSearch.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { User, FriendRequest } from '@/lib/types'\nimport { storageUtils } from '@/lib/storage'\n\ninterface UserSearchProps {\n  currentUserId: string\n  users: User[]\n  friendRequests: FriendRequest[]\n  onSendFriendRequest: (receiverId: string) => void\n  onSendMessage: (receiverId: string, content: string) => void\n}\n\nexport default function UserSearch({ \n  currentUserId, \n  users, \n  friendRequests, \n  onSendFriendRequest, \n  onSendMessage \n}: UserSearchProps) {\n  const [searchQuery, setSearchQuery] = useState('')\n  const [searchResults, setSearchResults] = useState<User[]>([])\n  const [messageContent, setMessageContent] = useState<{ [key: string]: string }>({})\n\n  const handleSearch = (query: string) => {\n    setSearchQuery(query)\n    if (query.trim()) {\n      const results = storageUtils.searchUsers(query).filter(user => user.id !== currentUserId)\n      setSearchResults(results)\n    } else {\n      setSearchResults([])\n    }\n  }\n\n  const getFriendRequestStatus = (userId: string) => {\n    const sentRequest = friendRequests.find(\n      req => req.senderId === currentUserId && req.receiverId === userId\n    )\n    const receivedRequest = friendRequests.find(\n      req => req.senderId === userId && req.receiverId === currentUserId\n    )\n    \n    if (sentRequest) return sentRequest.status\n    if (receivedRequest) return 'received'\n    return null\n  }\n\n  const handleSendMessage = (receiverId: string) => {\n    const content = messageContent[receiverId]\n    if (content?.trim()) {\n      onSendMessage(receiverId, content.trim())\n      setMessageContent(prev => ({ ...prev, [receiverId]: '' }))\n    }\n  }\n\n  return (\n    <div className=\"p-6\">\n      <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Search Users</h2>\n      \n      <div className=\"mb-6\">\n        <input\n          type=\"text\"\n          placeholder=\"Search by username, first name, or last name...\"\n          value={searchQuery}\n          onChange={(e) => handleSearch(e.target.value)}\n          className=\"w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n        />\n      </div>\n\n      {searchQuery && searchResults.length === 0 && (\n        <p className=\"text-gray-500 text-center py-8\">No users found matching \"{searchQuery}\"</p>\n      )}\n\n      <div className=\"space-y-4\">\n        {searchResults.map(user => {\n          const friendStatus = getFriendRequestStatus(user.id)\n          \n          return (\n            <div key={user.id} className=\"border border-gray-200 rounded-lg p-4\">\n              <div className=\"flex items-start space-x-4\">\n                <img\n                  src={user.imageUrl || `https://ui-avatars.com/api/?name=${user.firstName}+${user.lastName}&background=random`}\n                  alt={`${user.firstName} ${user.lastName}`}\n                  className=\"w-12 h-12 rounded-full object-cover\"\n                />\n                \n                <div className=\"flex-1\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <h3 className=\"text-lg font-medium text-gray-900\">\n                        {user.firstName} {user.lastName}\n                      </h3>\n                      <p className=\"text-sm text-gray-500\">@{user.username}</p>\n                      {user.emailAddress && (\n                        <p className=\"text-sm text-gray-500\">{user.emailAddress}</p>\n                      )}\n                    </div>\n                    \n                    <div className=\"flex space-x-2\">\n                      {friendStatus === 'pending' && (\n                        <span className=\"px-3 py-1 bg-yellow-100 text-yellow-800 text-sm rounded-full\">\n                          Request Sent\n                        </span>\n                      )}\n                      {friendStatus === 'accepted' && (\n                        <span className=\"px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full\">\n                          Friends\n                        </span>\n                      )}\n                      {friendStatus === 'received' && (\n                        <span className=\"px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full\">\n                          Sent You Request\n                        </span>\n                      )}\n                      {!friendStatus && (\n                        <button\n                          onClick={() => onSendFriendRequest(user.id)}\n                          className=\"px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700\"\n                        >\n                          Add Friend\n                        </button>\n                      )}\n                    </div>\n                  </div>\n                  \n                  <div className=\"mt-4\">\n                    <div className=\"flex space-x-2\">\n                      <input\n                        type=\"text\"\n                        placeholder=\"Type a message...\"\n                        value={messageContent[user.id] || ''}\n                        onChange={(e) => setMessageContent(prev => ({ \n                          ...prev, \n                          [user.id]: e.target.value \n                        }))}\n                        className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500\"\n                        onKeyPress={(e) => {\n                          if (e.key === 'Enter') {\n                            handleSendMessage(user.id)\n                          }\n                        }}\n                      />\n                      <button\n                        onClick={() => handleSendMessage(user.id)}\n                        disabled={!messageContent[user.id]?.trim()}\n                        className=\"px-4 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed\"\n                      >\n                        Send\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )\n        })}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;AAJA;;;AAce,SAAS,WAAW,KAMjB;QANiB,EACjC,aAAa,EACb,KAAK,EACL,cAAc,EACd,mBAAmB,EACnB,aAAa,EACG,GANiB;;IAOjC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,qLAAQ,EAAC;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,qLAAQ,EAAS,EAAE;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,qLAAQ,EAA4B,CAAC;IAEjF,MAAM,eAAe,CAAC;QACpB,eAAe;QACf,IAAI,MAAM,IAAI,IAAI;YAChB,MAAM,UAAU,6IAAY,CAAC,WAAW,CAAC,OAAO,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAC3E,iBAAiB;QACnB,OAAO;YACL,iBAAiB,EAAE;QACrB;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,MAAM,cAAc,eAAe,IAAI,CACrC,CAAA,MAAO,IAAI,QAAQ,KAAK,iBAAiB,IAAI,UAAU,KAAK;QAE9D,MAAM,kBAAkB,eAAe,IAAI,CACzC,CAAA,MAAO,IAAI,QAAQ,KAAK,UAAU,IAAI,UAAU,KAAK;QAGvD,IAAI,aAAa,OAAO,YAAY,MAAM;QAC1C,IAAI,iBAAiB,OAAO;QAC5B,OAAO;IACT;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,UAAU,cAAc,CAAC,WAAW;QAC1C,IAAI,oBAAA,8BAAA,QAAS,IAAI,IAAI;YACnB,cAAc,YAAY,QAAQ,IAAI;YACtC,kBAAkB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,WAAW,EAAE;gBAAG,CAAC;QAC1D;IACF;IAEA,qBACE,yMAAC;QAAI,WAAU;;0BACb,yMAAC;gBAAG,WAAU;0BAAwC;;;;;;0BAEtD,yMAAC;gBAAI,WAAU;0BACb,cAAA,yMAAC;oBACC,MAAK;oBACL,aAAY;oBACZ,OAAO;oBACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oBAC5C,WAAU;;;;;;;;;;;YAIb,eAAe,cAAc,MAAM,KAAK,mBACvC,yMAAC;gBAAE,WAAU;;oBAAiC;oBAA0B;oBAAY;;;;;;;0BAGtF,yMAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAA;wBAsEQ;oBArEzB,MAAM,eAAe,uBAAuB,KAAK,EAAE;oBAEnD,qBACE,yMAAC;wBAAkB,WAAU;kCAC3B,cAAA,yMAAC;4BAAI,WAAU;;8CACb,yMAAC;oCACC,KAAK,KAAK,QAAQ,IAAI,AAAC,oCAAqD,OAAlB,KAAK,SAAS,EAAC,KAAiB,OAAd,KAAK,QAAQ,EAAC;oCAC1F,KAAK,AAAC,GAAoB,OAAlB,KAAK,SAAS,EAAC,KAAiB,OAAd,KAAK,QAAQ;oCACvC,WAAU;;;;;;8CAGZ,yMAAC;oCAAI,WAAU;;sDACb,yMAAC;4CAAI,WAAU;;8DACb,yMAAC;;sEACC,yMAAC;4DAAG,WAAU;;gEACX,KAAK,SAAS;gEAAC;gEAAE,KAAK,QAAQ;;;;;;;sEAEjC,yMAAC;4DAAE,WAAU;;gEAAwB;gEAAE,KAAK,QAAQ;;;;;;;wDACnD,KAAK,YAAY,kBAChB,yMAAC;4DAAE,WAAU;sEAAyB,KAAK,YAAY;;;;;;;;;;;;8DAI3D,yMAAC;oDAAI,WAAU;;wDACZ,iBAAiB,2BAChB,yMAAC;4DAAK,WAAU;sEAA+D;;;;;;wDAIhF,iBAAiB,4BAChB,yMAAC;4DAAK,WAAU;sEAA6D;;;;;;wDAI9E,iBAAiB,4BAChB,yMAAC;4DAAK,WAAU;sEAA2D;;;;;;wDAI5E,CAAC,8BACA,yMAAC;4DACC,SAAS,IAAM,oBAAoB,KAAK,EAAE;4DAC1C,WAAU;sEACX;;;;;;;;;;;;;;;;;;sDAOP,yMAAC;4CAAI,WAAU;sDACb,cAAA,yMAAC;gDAAI,WAAU;;kEACb,yMAAC;wDACC,MAAK;wDACL,aAAY;wDACZ,OAAO,cAAc,CAAC,KAAK,EAAE,CAAC,IAAI;wDAClC,UAAU,CAAC,IAAM,kBAAkB,CAAA,OAAQ,CAAC;oEAC1C,GAAG,IAAI;oEACP,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;gEAC3B,CAAC;wDACD,WAAU;wDACV,YAAY,CAAC;4DACX,IAAI,EAAE,GAAG,KAAK,SAAS;gEACrB,kBAAkB,KAAK,EAAE;4DAC3B;wDACF;;;;;;kEAEF,yMAAC;wDACC,SAAS,IAAM,kBAAkB,KAAK,EAAE;wDACxC,UAAU,GAAC,0BAAA,cAAc,CAAC,KAAK,EAAE,CAAC,cAAvB,8CAAA,wBAAyB,IAAI;wDACxC,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBApED,KAAK,EAAE;;;;;gBA6ErB;;;;;;;;;;;;AAIR;GAlJwB;KAAA", "debugId": null}}, {"offset": {"line": 442, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/components/ChatWindow.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport { User, Message } from '@/lib/types'\n\ninterface ChatWindowProps {\n  currentUserId: string\n  otherUserId: string\n  otherUser?: User\n  messages: Message[]\n  onSendMessage: (receiverId: string, content: string) => void\n}\n\nexport default function ChatWindow({ \n  currentUserId, \n  otherUserId, \n  otherUser, \n  messages, \n  onSendMessage \n}: ChatWindowProps) {\n  const [newMessage, setNewMessage] = useState('')\n  const messagesEndRef = useRef<HTMLDivElement>(null)\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })\n  }\n\n  useEffect(() => {\n    scrollToBottom()\n  }, [messages])\n\n  const handleSendMessage = () => {\n    if (newMessage.trim()) {\n      onSendMessage(otherUserId, newMessage.trim())\n      setNewMessage('')\n    }\n  }\n\n  const formatTime = (timestamp: number) => {\n    return new Date(timestamp).toLocaleTimeString([], { \n      hour: '2-digit', \n      minute: '2-digit' \n    })\n  }\n\n  const formatDate = (timestamp: number) => {\n    const date = new Date(timestamp)\n    const today = new Date()\n    const yesterday = new Date(today)\n    yesterday.setDate(yesterday.getDate() - 1)\n\n    if (date.toDateString() === today.toDateString()) {\n      return 'Today'\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Yesterday'\n    } else {\n      return date.toLocaleDateString([], { \n        weekday: 'long', \n        year: 'numeric', \n        month: 'long', \n        day: 'numeric' \n      })\n    }\n  }\n\n  const groupMessagesByDate = (messages: Message[]) => {\n    const groups: { [key: string]: Message[] } = {}\n    \n    messages.forEach(message => {\n      const dateKey = new Date(message.timestamp).toDateString()\n      if (!groups[dateKey]) {\n        groups[dateKey] = []\n      }\n      groups[dateKey].push(message)\n    })\n    \n    return Object.entries(groups).sort(([a], [b]) => \n      new Date(a).getTime() - new Date(b).getTime()\n    )\n  }\n\n  if (!otherUser) {\n    return (\n      <div className=\"flex items-center justify-center h-full\">\n        <p className=\"text-gray-500\">User not found</p>\n      </div>\n    )\n  }\n\n  const messageGroups = groupMessagesByDate(messages)\n\n  return (\n    <div className=\"flex flex-col h-full\">\n      {/* Chat Header */}\n      <div className=\"p-4 border-b border-gray-200 bg-white\">\n        <div className=\"flex items-center space-x-3\">\n          <img\n            src={otherUser.imageUrl || `https://ui-avatars.com/api/?name=${otherUser.firstName}+${otherUser.lastName}&background=random`}\n            alt={`${otherUser.firstName} ${otherUser.lastName}`}\n            className=\"w-10 h-10 rounded-full object-cover\"\n          />\n          <div>\n            <h3 className=\"text-lg font-medium text-gray-900\">\n              {otherUser.firstName} {otherUser.lastName}\n            </h3>\n            <p className=\"text-sm text-gray-500\">@{otherUser.username}</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Messages */}\n      <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n        {messageGroups.length === 0 ? (\n          <div className=\"text-center text-gray-500 py-8\">\n            <p>No messages yet</p>\n            <p className=\"text-sm mt-1\">Start the conversation!</p>\n          </div>\n        ) : (\n          messageGroups.map(([dateKey, dayMessages]) => (\n            <div key={dateKey}>\n              {/* Date Separator */}\n              <div className=\"flex items-center justify-center my-4\">\n                <div className=\"bg-gray-100 px-3 py-1 rounded-full text-xs text-gray-600\">\n                  {formatDate(new Date(dateKey).getTime())}\n                </div>\n              </div>\n\n              {/* Messages for this date */}\n              {dayMessages.map((message, index) => {\n                const isCurrentUser = message.senderId === currentUserId\n                const showAvatar = !isCurrentUser && (\n                  index === 0 || \n                  dayMessages[index - 1]?.senderId !== message.senderId\n                )\n\n                return (\n                  <div\n                    key={message.id}\n                    className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'} mb-2`}\n                  >\n                    <div className={`flex items-end space-x-2 max-w-xs lg:max-w-md ${isCurrentUser ? 'flex-row-reverse space-x-reverse' : ''}`}>\n                      {showAvatar && !isCurrentUser && (\n                        <img\n                          src={otherUser.imageUrl || `https://ui-avatars.com/api/?name=${otherUser.firstName}+${otherUser.lastName}&background=random`}\n                          alt={`${otherUser.firstName} ${otherUser.lastName}`}\n                          className=\"w-6 h-6 rounded-full object-cover\"\n                        />\n                      )}\n                      {!showAvatar && !isCurrentUser && (\n                        <div className=\"w-6 h-6\" />\n                      )}\n                      \n                      <div\n                        className={`px-4 py-2 rounded-lg ${\n                          isCurrentUser\n                            ? 'bg-blue-600 text-white'\n                            : 'bg-gray-100 text-gray-900'\n                        }`}\n                      >\n                        <p className=\"text-sm\">{message.content}</p>\n                        <p className={`text-xs mt-1 ${\n                          isCurrentUser ? 'text-blue-100' : 'text-gray-500'\n                        }`}>\n                          {formatTime(message.timestamp)}\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n                )\n              })}\n            </div>\n          ))\n        )}\n        <div ref={messagesEndRef} />\n      </div>\n\n      {/* Message Input */}\n      <div className=\"p-4 border-t border-gray-200 bg-white\">\n        <div className=\"flex space-x-2\">\n          <input\n            type=\"text\"\n            value={newMessage}\n            onChange={(e) => setNewMessage(e.target.value)}\n            placeholder={`Message ${otherUser.firstName}...`}\n            className=\"flex-1 px-4 py-2 border border-gray-300 rounded-full focus:ring-blue-500 focus:border-blue-500\"\n            onKeyPress={(e) => {\n              if (e.key === 'Enter') {\n                handleSendMessage()\n              }\n            }}\n          />\n          <button\n            onClick={handleSendMessage}\n            disabled={!newMessage.trim()}\n            className=\"px-6 py-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed\"\n          >\n            Send\n          </button>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAae,SAAS,WAAW,KAMjB;QANiB,EACjC,aAAa,EACb,WAAW,EACX,SAAS,EACT,QAAQ,EACR,aAAa,EACG,GANiB;;IAOjC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,qLAAQ,EAAC;IAC7C,MAAM,iBAAiB,IAAA,mLAAM,EAAiB;IAE9C,MAAM,iBAAiB;YACrB;SAAA,0BAAA,eAAe,OAAO,cAAtB,8CAAA,wBAAwB,cAAc,CAAC;YAAE,UAAU;QAAS;IAC9D;IAEA,IAAA,sLAAS;gCAAC;YACR;QACF;+BAAG;QAAC;KAAS;IAEb,MAAM,oBAAoB;QACxB,IAAI,WAAW,IAAI,IAAI;YACrB,cAAc,aAAa,WAAW,IAAI;YAC1C,cAAc;QAChB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,WAAW,kBAAkB,CAAC,EAAE,EAAE;YAChD,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,QAAQ,IAAI;QAClB,MAAM,YAAY,IAAI,KAAK;QAC3B,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;QAExC,IAAI,KAAK,YAAY,OAAO,MAAM,YAAY,IAAI;YAChD,OAAO;QACT,OAAO,IAAI,KAAK,YAAY,OAAO,UAAU,YAAY,IAAI;YAC3D,OAAO;QACT,OAAO;YACL,OAAO,KAAK,kBAAkB,CAAC,EAAE,EAAE;gBACjC,SAAS;gBACT,MAAM;gBACN,OAAO;gBACP,KAAK;YACP;QACF;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,SAAuC,CAAC;QAE9C,SAAS,OAAO,CAAC,CAAA;YACf,MAAM,UAAU,IAAI,KAAK,QAAQ,SAAS,EAAE,YAAY;YACxD,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;gBACpB,MAAM,CAAC,QAAQ,GAAG,EAAE;YACtB;YACA,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;QACvB;QAEA,OAAO,OAAO,OAAO,CAAC,QAAQ,IAAI,CAAC;gBAAC,CAAC,EAAE,UAAE,CAAC,EAAE;mBAC1C,IAAI,KAAK,GAAG,OAAO,KAAK,IAAI,KAAK,GAAG,OAAO;;IAE/C;IAEA,IAAI,CAAC,WAAW;QACd,qBACE,yMAAC;YAAI,WAAU;sBACb,cAAA,yMAAC;gBAAE,WAAU;0BAAgB;;;;;;;;;;;IAGnC;IAEA,MAAM,gBAAgB,oBAAoB;IAE1C,qBACE,yMAAC;QAAI,WAAU;;0BAEb,yMAAC;gBAAI,WAAU;0BACb,cAAA,yMAAC;oBAAI,WAAU;;sCACb,yMAAC;4BACC,KAAK,UAAU,QAAQ,IAAI,AAAC,oCAA0D,OAAvB,UAAU,SAAS,EAAC,KAAsB,OAAnB,UAAU,QAAQ,EAAC;4BACzG,KAAK,AAAC,GAAyB,OAAvB,UAAU,SAAS,EAAC,KAAsB,OAAnB,UAAU,QAAQ;4BACjD,WAAU;;;;;;sCAEZ,yMAAC;;8CACC,yMAAC;oCAAG,WAAU;;wCACX,UAAU,SAAS;wCAAC;wCAAE,UAAU,QAAQ;;;;;;;8CAE3C,yMAAC;oCAAE,WAAU;;wCAAwB;wCAAE,UAAU,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;0BAM/D,yMAAC;gBAAI,WAAU;;oBACZ,cAAc,MAAM,KAAK,kBACxB,yMAAC;wBAAI,WAAU;;0CACb,yMAAC;0CAAE;;;;;;0CACH,yMAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;+BAG9B,cAAc,GAAG,CAAC;4BAAC,CAAC,SAAS,YAAY;6CACvC,yMAAC;;8CAEC,yMAAC;oCAAI,WAAU;8CACb,cAAA,yMAAC;wCAAI,WAAU;kDACZ,WAAW,IAAI,KAAK,SAAS,OAAO;;;;;;;;;;;gCAKxC,YAAY,GAAG,CAAC,CAAC,SAAS;wCAIvB;oCAHF,MAAM,gBAAgB,QAAQ,QAAQ,KAAK;oCAC3C,MAAM,aAAa,CAAC,iBAAiB,CACnC,UAAU,KACV,EAAA,gBAAA,WAAW,CAAC,QAAQ,EAAE,cAAtB,oCAAA,cAAwB,QAAQ,MAAK,QAAQ,QAAQ,AACvD;oCAEA,qBACE,yMAAC;wCAEC,WAAW,AAAC,QAAuD,OAAhD,gBAAgB,gBAAgB,iBAAgB;kDAEnE,cAAA,yMAAC;4CAAI,WAAW,AAAC,iDAAwG,OAAxD,gBAAgB,qCAAqC;;gDACnH,cAAc,CAAC,+BACd,yMAAC;oDACC,KAAK,UAAU,QAAQ,IAAI,AAAC,oCAA0D,OAAvB,UAAU,SAAS,EAAC,KAAsB,OAAnB,UAAU,QAAQ,EAAC;oDACzG,KAAK,AAAC,GAAyB,OAAvB,UAAU,SAAS,EAAC,KAAsB,OAAnB,UAAU,QAAQ;oDACjD,WAAU;;;;;;gDAGb,CAAC,cAAc,CAAC,+BACf,yMAAC;oDAAI,WAAU;;;;;;8DAGjB,yMAAC;oDACC,WAAW,AAAC,wBAIX,OAHC,gBACI,2BACA;;sEAGN,yMAAC;4DAAE,WAAU;sEAAW,QAAQ,OAAO;;;;;;sEACvC,yMAAC;4DAAE,WAAW,AAAC,gBAEd,OADC,gBAAgB,kBAAkB;sEAEjC,WAAW,QAAQ,SAAS;;;;;;;;;;;;;;;;;;uCA1B9B,QAAQ,EAAE;;;;;gCAgCrB;;2BAlDQ;;;;;;kCAsDd,yMAAC;wBAAI,KAAK;;;;;;;;;;;;0BAIZ,yMAAC;gBAAI,WAAU;0BACb,cAAA,yMAAC;oBAAI,WAAU;;sCACb,yMAAC;4BACC,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4BAC7C,aAAa,AAAC,WAA8B,OAApB,UAAU,SAAS,EAAC;4BAC5C,WAAU;4BACV,YAAY,CAAC;gCACX,IAAI,EAAE,GAAG,KAAK,SAAS;oCACrB;gCACF;4BACF;;;;;;sCAEF,yMAAC;4BACC,SAAS;4BACT,UAAU,CAAC,WAAW,IAAI;4BAC1B,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX;GA7LwB;KAAA", "debugId": null}}, {"offset": {"line": 778, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/components/MessageList.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { User, Message } from '@/lib/types'\nimport { storageUtils } from '@/lib/storage'\nimport Chat<PERSON>indow from './ChatWindow'\n\ninterface MessageListProps {\n  currentUserId: string\n  users: User[]\n  messages: Message[]\n  onSendMessage: (receiverId: string, content: string) => void\n}\n\nexport default function MessageList({ currentUserId, users, messages, onSendMessage }: MessageListProps) {\n  const [selectedUserId, setSelectedUserId] = useState<string | null>(null)\n  const [conversations, setConversations] = useState<{ userId: string; lastMessage?: Message; unreadCount: number }[]>([])\n\n  useEffect(() => {\n    // Get all users that have conversations with current user\n    const userConversations = new Map<string, { lastMessage?: Message; unreadCount: number }>()\n    \n    messages.forEach(message => {\n      const otherUserId = message.senderId === currentUserId ? message.receiverId : message.senderId\n      \n      if (otherUserId !== currentUserId) {\n        const existing = userConversations.get(otherUserId)\n        const isUnread = message.receiverId === currentUserId && !message.read\n        \n        userConversations.set(otherUserId, {\n          lastMessage: !existing?.lastMessage || message.timestamp > existing.lastMessage.timestamp \n            ? message \n            : existing.lastMessage,\n          unreadCount: (existing?.unreadCount || 0) + (isUnread ? 1 : 0)\n        })\n      }\n    })\n\n    const conversationList = Array.from(userConversations.entries())\n      .map(([userId, data]) => ({ userId, ...data }))\n      .sort((a, b) => (b.lastMessage?.timestamp || 0) - (a.lastMessage?.timestamp || 0))\n\n    setConversations(conversationList)\n  }, [messages, currentUserId])\n\n  const getUserById = (userId: string) => {\n    return users.find(user => user.id === userId)\n  }\n\n  const formatTime = (timestamp: number) => {\n    const date = new Date(timestamp)\n    const now = new Date()\n    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)\n    \n    if (diffInHours < 24) {\n      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })\n    } else if (diffInHours < 168) { // 7 days\n      return date.toLocaleDateString([], { weekday: 'short' })\n    } else {\n      return date.toLocaleDateString([], { month: 'short', day: 'numeric' })\n    }\n  }\n\n  const truncateMessage = (content: string, maxLength: number = 50) => {\n    return content.length > maxLength ? content.substring(0, maxLength) + '...' : content\n  }\n\n  return (\n    <div className=\"flex h-[600px]\">\n      {/* Conversations List */}\n      <div className=\"w-1/3 border-r border-gray-200\">\n        <div className=\"p-4 border-b border-gray-200\">\n          <h3 className=\"text-lg font-medium text-gray-900\">Messages</h3>\n        </div>\n        \n        <div className=\"overflow-y-auto h-full\">\n          {conversations.length === 0 ? (\n            <div className=\"p-4 text-center text-gray-500\">\n              <p>No conversations yet</p>\n              <p className=\"text-sm mt-1\">Search for users to start messaging</p>\n            </div>\n          ) : (\n            conversations.map(({ userId, lastMessage, unreadCount }) => {\n              const user = getUserById(userId)\n              if (!user) return null\n\n              return (\n                <div\n                  key={userId}\n                  onClick={() => setSelectedUserId(userId)}\n                  className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${\n                    selectedUserId === userId ? 'bg-blue-50 border-blue-200' : ''\n                  }`}\n                >\n                  <div className=\"flex items-center space-x-3\">\n                    <img\n                      src={user.imageUrl || `https://ui-avatars.com/api/?name=${user.firstName}+${user.lastName}&background=random`}\n                      alt={`${user.firstName} ${user.lastName}`}\n                      className=\"w-10 h-10 rounded-full object-cover\"\n                    />\n                    \n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"flex items-center justify-between\">\n                        <p className=\"text-sm font-medium text-gray-900 truncate\">\n                          {user.firstName} {user.lastName}\n                        </p>\n                        {lastMessage && (\n                          <p className=\"text-xs text-gray-500\">\n                            {formatTime(lastMessage.timestamp)}\n                          </p>\n                        )}\n                      </div>\n                      \n                      <div className=\"flex items-center justify-between\">\n                        {lastMessage && (\n                          <p className=\"text-sm text-gray-500 truncate\">\n                            {lastMessage.senderId === currentUserId ? 'You: ' : ''}\n                            {truncateMessage(lastMessage.content)}\n                          </p>\n                        )}\n                        {unreadCount > 0 && (\n                          <span className=\"bg-blue-600 text-white text-xs rounded-full px-2 py-1 ml-2\">\n                            {unreadCount}\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              )\n            })\n          )}\n        </div>\n      </div>\n\n      {/* Chat Window */}\n      <div className=\"flex-1\">\n        {selectedUserId ? (\n          <ChatWindow\n            currentUserId={currentUserId}\n            otherUserId={selectedUserId}\n            otherUser={getUserById(selectedUserId)}\n            messages={storageUtils.getConversationMessages(currentUserId, selectedUserId)}\n            onSendMessage={onSendMessage}\n          />\n        ) : (\n          <div className=\"flex items-center justify-center h-full text-gray-500\">\n            <div className=\"text-center\">\n              <svg className=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-2.4-.32l-4.6 1.92 1.92-4.6A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z\" />\n              </svg>\n              <p className=\"mt-2\">Select a conversation to start messaging</p>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AACA;;;AALA;;;;AAce,SAAS,YAAY,KAAmE;QAAnE,EAAE,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAoB,GAAnE;;IAClC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,qLAAQ,EAAgB;IACpE,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,qLAAQ,EAAmE,EAAE;IAEvH,IAAA,sLAAS;iCAAC;YACR,0DAA0D;YAC1D,MAAM,oBAAoB,IAAI;YAE9B,SAAS,OAAO;yCAAC,CAAA;oBACf,MAAM,cAAc,QAAQ,QAAQ,KAAK,gBAAgB,QAAQ,UAAU,GAAG,QAAQ,QAAQ;oBAE9F,IAAI,gBAAgB,eAAe;wBACjC,MAAM,WAAW,kBAAkB,GAAG,CAAC;wBACvC,MAAM,WAAW,QAAQ,UAAU,KAAK,iBAAiB,CAAC,QAAQ,IAAI;wBAEtE,kBAAkB,GAAG,CAAC,aAAa;4BACjC,aAAa,EAAC,qBAAA,+BAAA,SAAU,WAAW,KAAI,QAAQ,SAAS,GAAG,SAAS,WAAW,CAAC,SAAS,GACrF,UACA,SAAS,WAAW;4BACxB,aAAa,CAAC,CAAA,qBAAA,+BAAA,SAAU,WAAW,KAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC;wBAC/D;oBACF;gBACF;;YAEA,MAAM,mBAAmB,MAAM,IAAI,CAAC,kBAAkB,OAAO,IAC1D,GAAG;0DAAC;wBAAC,CAAC,QAAQ,KAAK;2BAAM;wBAAE;wBAAQ,GAAG,IAAI;oBAAC;;yDAC3C,IAAI;0DAAC,CAAC,GAAG;wBAAO,gBAAkC;2BAAnC,CAAC,EAAA,iBAAA,EAAE,WAAW,cAAb,qCAAA,eAAe,SAAS,KAAI,CAAC,IAAI,CAAC,EAAA,iBAAA,EAAE,WAAW,cAAb,qCAAA,eAAe,SAAS,KAAI,CAAC;;;YAElF,iBAAiB;QACnB;gCAAG;QAAC;QAAU;KAAc;IAE5B,MAAM,cAAc,CAAC;QACnB,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IACxC;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,cAAc,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE;QAEtE,IAAI,cAAc,IAAI;YACpB,OAAO,KAAK,kBAAkB,CAAC,EAAE,EAAE;gBAAE,MAAM;gBAAW,QAAQ;YAAU;QAC1E,OAAO,IAAI,cAAc,KAAK;YAC5B,OAAO,KAAK,kBAAkB,CAAC,EAAE,EAAE;gBAAE,SAAS;YAAQ;QACxD,OAAO;YACL,OAAO,KAAK,kBAAkB,CAAC,EAAE,EAAE;gBAAE,OAAO;gBAAS,KAAK;YAAU;QACtE;IACF;IAEA,MAAM,kBAAkB,SAAC;YAAiB,6EAAoB;QAC5D,OAAO,QAAQ,MAAM,GAAG,YAAY,QAAQ,SAAS,CAAC,GAAG,aAAa,QAAQ;IAChF;IAEA,qBACE,yMAAC;QAAI,WAAU;;0BAEb,yMAAC;gBAAI,WAAU;;kCACb,yMAAC;wBAAI,WAAU;kCACb,cAAA,yMAAC;4BAAG,WAAU;sCAAoC;;;;;;;;;;;kCAGpD,yMAAC;wBAAI,WAAU;kCACZ,cAAc,MAAM,KAAK,kBACxB,yMAAC;4BAAI,WAAU;;8CACb,yMAAC;8CAAE;;;;;;8CACH,yMAAC;oCAAE,WAAU;8CAAe;;;;;;;;;;;mCAG9B,cAAc,GAAG,CAAC;gCAAC,EAAE,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE;4BACrD,MAAM,OAAO,YAAY;4BACzB,IAAI,CAAC,MAAM,OAAO;4BAElB,qBACE,yMAAC;gCAEC,SAAS,IAAM,kBAAkB;gCACjC,WAAW,AAAC,gEAEX,OADC,mBAAmB,SAAS,+BAA+B;0CAG7D,cAAA,yMAAC;oCAAI,WAAU;;sDACb,yMAAC;4CACC,KAAK,KAAK,QAAQ,IAAI,AAAC,oCAAqD,OAAlB,KAAK,SAAS,EAAC,KAAiB,OAAd,KAAK,QAAQ,EAAC;4CAC1F,KAAK,AAAC,GAAoB,OAAlB,KAAK,SAAS,EAAC,KAAiB,OAAd,KAAK,QAAQ;4CACvC,WAAU;;;;;;sDAGZ,yMAAC;4CAAI,WAAU;;8DACb,yMAAC;oDAAI,WAAU;;sEACb,yMAAC;4DAAE,WAAU;;gEACV,KAAK,SAAS;gEAAC;gEAAE,KAAK,QAAQ;;;;;;;wDAEhC,6BACC,yMAAC;4DAAE,WAAU;sEACV,WAAW,YAAY,SAAS;;;;;;;;;;;;8DAKvC,yMAAC;oDAAI,WAAU;;wDACZ,6BACC,yMAAC;4DAAE,WAAU;;gEACV,YAAY,QAAQ,KAAK,gBAAgB,UAAU;gEACnD,gBAAgB,YAAY,OAAO;;;;;;;wDAGvC,cAAc,mBACb,yMAAC;4DAAK,WAAU;sEACb;;;;;;;;;;;;;;;;;;;;;;;;+BAlCN;;;;;wBA0CX;;;;;;;;;;;;0BAMN,yMAAC;gBAAI,WAAU;0BACZ,+BACC,yMAAC,mJAAU;oBACT,eAAe;oBACf,aAAa;oBACb,WAAW,YAAY;oBACvB,UAAU,6IAAY,CAAC,uBAAuB,CAAC,eAAe;oBAC9D,eAAe;;;;;yCAGjB,yMAAC;oBAAI,WAAU;8BACb,cAAA,yMAAC;wBAAI,WAAU;;0CACb,yMAAC;gCAAI,WAAU;gCAAkC,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CACtF,cAAA,yMAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;0CAEvE,yMAAC;gCAAE,WAAU;0CAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlC;GAhJwB;KAAA", "debugId": null}}, {"offset": {"line": 1094, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/components/FriendRequestCard.tsx"], "sourcesContent": ["'use client'\n\nimport { User, FriendRequest } from '@/lib/types'\n\ninterface FriendRequestCardProps {\n  request: FriendRequest\n  users: User[]\n  onResponse: (requestId: string, status: 'accepted' | 'rejected') => void\n}\n\nexport default function FriendRequestCard({ request, users, onResponse }: FriendRequestCardProps) {\n  const sender = users.find(user => user.id === request.senderId)\n\n  if (!sender) {\n    return null\n  }\n\n  const formatTime = (timestamp: number) => {\n    const date = new Date(timestamp)\n    const now = new Date()\n    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)\n    \n    if (diffInHours < 1) {\n      return 'Just now'\n    } else if (diffInHours < 24) {\n      return `${Math.floor(diffInHours)} hours ago`\n    } else {\n      const diffInDays = Math.floor(diffInHours / 24)\n      return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`\n    }\n  }\n\n  return (\n    <div className=\"border border-gray-200 rounded-lg p-4 bg-white\">\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          <img\n            src={sender.imageUrl || `https://ui-avatars.com/api/?name=${sender.firstName}+${sender.lastName}&background=random`}\n            alt={`${sender.firstName} ${sender.lastName}`}\n            className=\"w-12 h-12 rounded-full object-cover\"\n          />\n          \n          <div>\n            <h3 className=\"text-lg font-medium text-gray-900\">\n              {sender.firstName} {sender.lastName}\n            </h3>\n            <p className=\"text-sm text-gray-500\">@{sender.username}</p>\n            <p className=\"text-xs text-gray-400\">{formatTime(request.timestamp)}</p>\n          </div>\n        </div>\n        \n        <div className=\"flex space-x-2\">\n          <button\n            onClick={() => onResponse(request.id, 'accepted')}\n            className=\"px-4 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700\"\n          >\n            Accept\n          </button>\n          <button\n            onClick={() => onResponse(request.id, 'rejected')}\n            className=\"px-4 py-2 bg-red-600 text-white text-sm rounded-md hover:bg-red-700\"\n          >\n            Decline\n          </button>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAUe,SAAS,kBAAkB,KAAsD;QAAtD,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAA0B,GAAtD;IACxC,MAAM,SAAS,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,QAAQ,QAAQ;IAE9D,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,cAAc,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE;QAEtE,IAAI,cAAc,GAAG;YACnB,OAAO;QACT,OAAO,IAAI,cAAc,IAAI;YAC3B,OAAO,AAAC,GAA0B,OAAxB,KAAK,KAAK,CAAC,cAAa;QACpC,OAAO;YACL,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;YAC5C,OAAO,AAAC,GAAmB,OAAjB,YAAW,QAAgC,OAA1B,aAAa,IAAI,MAAM,IAAG;QACvD;IACF;IAEA,qBACE,yMAAC;QAAI,WAAU;kBACb,cAAA,yMAAC;YAAI,WAAU;;8BACb,yMAAC;oBAAI,WAAU;;sCACb,yMAAC;4BACC,KAAK,OAAO,QAAQ,IAAI,AAAC,oCAAuD,OAApB,OAAO,SAAS,EAAC,KAAmB,OAAhB,OAAO,QAAQ,EAAC;4BAChG,KAAK,AAAC,GAAsB,OAApB,OAAO,SAAS,EAAC,KAAmB,OAAhB,OAAO,QAAQ;4BAC3C,WAAU;;;;;;sCAGZ,yMAAC;;8CACC,yMAAC;oCAAG,WAAU;;wCACX,OAAO,SAAS;wCAAC;wCAAE,OAAO,QAAQ;;;;;;;8CAErC,yMAAC;oCAAE,WAAU;;wCAAwB;wCAAE,OAAO,QAAQ;;;;;;;8CACtD,yMAAC;oCAAE,WAAU;8CAAyB,WAAW,QAAQ,SAAS;;;;;;;;;;;;;;;;;;8BAItE,yMAAC;oBAAI,WAAU;;sCACb,yMAAC;4BACC,SAAS,IAAM,WAAW,QAAQ,EAAE,EAAE;4BACtC,WAAU;sCACX;;;;;;sCAGD,yMAAC;4BACC,SAAS,IAAM,WAAW,QAAQ,EAAE,EAAE;4BACtC,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX;KA1DwB", "debugId": null}}, {"offset": {"line": 1231, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/components/Navigation.tsx"], "sourcesContent": ["'use client'\n\nimport { UserButton } from '@clerk/nextjs'\n\ninterface NavigationProps {\n  activeTab: 'messages' | 'search' | 'friends'\n  setActiveTab: (tab: 'messages' | 'search' | 'friends') => void\n  pendingRequestsCount: number\n}\n\nexport default function Navigation({ activeTab, setActiveTab, pendingRequestsCount }: NavigationProps) {\n  return (\n    <nav className=\"bg-white shadow-sm border-b\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex items-center space-x-8\">\n            <h1 className=\"text-xl font-bold text-gray-900\">Messages App</h1>\n            \n            <div className=\"flex space-x-4\">\n              <button\n                onClick={() => setActiveTab('messages')}\n                className={`px-3 py-2 rounded-md text-sm font-medium ${\n                  activeTab === 'messages'\n                    ? 'bg-blue-100 text-blue-700'\n                    : 'text-gray-500 hover:text-gray-700'\n                }`}\n              >\n                Messages\n              </button>\n              \n              <button\n                onClick={() => setActiveTab('search')}\n                className={`px-3 py-2 rounded-md text-sm font-medium ${\n                  activeTab === 'search'\n                    ? 'bg-blue-100 text-blue-700'\n                    : 'text-gray-500 hover:text-gray-700'\n                }`}\n              >\n                Search Users\n              </button>\n              \n              <button\n                onClick={() => setActiveTab('friends')}\n                className={`px-3 py-2 rounded-md text-sm font-medium relative ${\n                  activeTab === 'friends'\n                    ? 'bg-blue-100 text-blue-700'\n                    : 'text-gray-500 hover:text-gray-700'\n                }`}\n              >\n                Friend Requests\n                {pendingRequestsCount > 0 && (\n                  <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\n                    {pendingRequestsCount}\n                  </span>\n                )}\n              </button>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center\">\n            <UserButton afterSignOutUrl=\"/\" />\n          </div>\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAUe,SAAS,WAAW,KAAkE;QAAlE,EAAE,SAAS,EAAE,YAAY,EAAE,oBAAoB,EAAmB,GAAlE;IACjC,qBACE,yMAAC;QAAI,WAAU;kBACb,cAAA,yMAAC;YAAI,WAAU;sBACb,cAAA,yMAAC;gBAAI,WAAU;;kCACb,yMAAC;wBAAI,WAAU;;0CACb,yMAAC;gCAAG,WAAU;0CAAkC;;;;;;0CAEhD,yMAAC;gCAAI,WAAU;;kDACb,yMAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,4CAIX,OAHC,cAAc,aACV,8BACA;kDAEP;;;;;;kDAID,yMAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,4CAIX,OAHC,cAAc,WACV,8BACA;kDAEP;;;;;;kDAID,yMAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,qDAIX,OAHC,cAAc,YACV,8BACA;;4CAEP;4CAEE,uBAAuB,mBACtB,yMAAC;gDAAK,WAAU;0DACb;;;;;;;;;;;;;;;;;;;;;;;;kCAOX,yMAAC;wBAAI,WAAU;kCACb,cAAA,yMAAC,oMAAU;4BAAC,iBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxC;KAxDwB", "debugId": null}}, {"offset": {"line": 1353, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/app/dashboard/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useUser } from '@clerk/nextjs'\nimport { useState, useEffect } from 'react'\nimport { storageUtils } from '@/lib/storage'\nimport { User, Message, FriendRequest } from '@/lib/types'\nimport UserSearch from '@/components/UserSearch'\nimport MessageList from '@/components/MessageList'\nimport FriendRequestCard from '@/components/FriendRequestCard'\nimport Navigation from '@/components/Navigation'\n\nexport default function Dashboard() {\n  const { user } = useUser()\n  const [activeTab, setActiveTab] = useState<'messages' | 'search' | 'friends'>('messages')\n  const [users, setUsers] = useState<User[]>([])\n  const [messages, setMessages] = useState<Message[]>([])\n  const [friendRequests, setFriendRequests] = useState<FriendRequest[]>([])\n\n  useEffect(() => {\n    if (user) {\n      // Add current user to storage\n      const currentUser: User = {\n        id: user.id,\n        username: user.username || user.emailAddresses[0]?.emailAddress.split('@')[0] || 'user',\n        firstName: user.firstName || '',\n        lastName: user.lastName || '',\n        imageUrl: user.imageUrl,\n        emailAddress: user.emailAddresses[0]?.emailAddress\n      }\n      storageUtils.addUser(currentUser)\n      \n      // Load data\n      setUsers(storageUtils.getUsers())\n      setMessages(storageUtils.getMessages())\n      setFriendRequests(storageUtils.getFriendRequests())\n    }\n  }, [user])\n\n  const handleSendMessage = (receiverId: string, content: string) => {\n    if (!user) return\n    \n    const newMessage: Message = {\n      id: Date.now().toString(),\n      senderId: user.id,\n      receiverId,\n      content,\n      timestamp: Date.now(),\n      read: false\n    }\n    \n    storageUtils.addMessage(newMessage)\n    setMessages(storageUtils.getMessages())\n  }\n\n  const handleSendFriendRequest = (receiverId: string) => {\n    if (!user) return\n    \n    const existingRequest = friendRequests.find(\n      req => req.senderId === user.id && req.receiverId === receiverId && req.status === 'pending'\n    )\n    \n    if (existingRequest) return\n    \n    const newRequest: FriendRequest = {\n      id: Date.now().toString(),\n      senderId: user.id,\n      receiverId,\n      status: 'pending',\n      timestamp: Date.now()\n    }\n    \n    storageUtils.addFriendRequest(newRequest)\n    setFriendRequests(storageUtils.getFriendRequests())\n  }\n\n  const handleFriendRequestResponse = (requestId: string, status: 'accepted' | 'rejected') => {\n    storageUtils.updateFriendRequest(requestId, status)\n    setFriendRequests(storageUtils.getFriendRequests())\n  }\n\n  if (!user) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Loading...</h2>\n        </div>\n      </div>\n    )\n  }\n\n  const receivedFriendRequests = friendRequests.filter(\n    req => req.receiverId === user.id && req.status === 'pending'\n  )\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navigation \n        activeTab={activeTab} \n        setActiveTab={setActiveTab}\n        pendingRequestsCount={receivedFriendRequests.length}\n      />\n      \n      <main className=\"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8\">\n        <div className=\"bg-white rounded-lg shadow\">\n          {activeTab === 'messages' && (\n            <MessageList \n              currentUserId={user.id}\n              users={users}\n              messages={messages}\n              onSendMessage={handleSendMessage}\n            />\n          )}\n          \n          {activeTab === 'search' && (\n            <UserSearch \n              currentUserId={user.id}\n              users={users}\n              friendRequests={friendRequests}\n              onSendFriendRequest={handleSendFriendRequest}\n              onSendMessage={handleSendMessage}\n            />\n          )}\n          \n          {activeTab === 'friends' && (\n            <div className=\"p-6\">\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Friend Requests</h2>\n              {receivedFriendRequests.length === 0 ? (\n                <p className=\"text-gray-500 text-center py-8\">No pending friend requests</p>\n              ) : (\n                <div className=\"space-y-4\">\n                  {receivedFriendRequests.map(request => (\n                    <FriendRequestCard\n                      key={request.id}\n                      request={request}\n                      users={users}\n                      onResponse={handleFriendRequestResponse}\n                    />\n                  ))}\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;;;AATA;;;;;;;;AAWe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,IAAA,sLAAO;IACxB,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,qLAAQ,EAAoC;IAC9E,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,qLAAQ,EAAS,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,qLAAQ,EAAY,EAAE;IACtD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,qLAAQ,EAAkB,EAAE;IAExE,IAAA,sLAAS;+BAAC;YACR,IAAI,MAAM;oBAIqB,uBAIb;gBAPhB,8BAA8B;gBAC9B,MAAM,cAAoB;oBACxB,IAAI,KAAK,EAAE;oBACX,UAAU,KAAK,QAAQ,MAAI,wBAAA,KAAK,cAAc,CAAC,EAAE,cAAtB,4CAAA,sBAAwB,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,KAAI;oBACjF,WAAW,KAAK,SAAS,IAAI;oBAC7B,UAAU,KAAK,QAAQ,IAAI;oBAC3B,UAAU,KAAK,QAAQ;oBACvB,YAAY,GAAE,yBAAA,KAAK,cAAc,CAAC,EAAE,cAAtB,6CAAA,uBAAwB,YAAY;gBACpD;gBACA,6IAAY,CAAC,OAAO,CAAC;gBAErB,YAAY;gBACZ,SAAS,6IAAY,CAAC,QAAQ;gBAC9B,YAAY,6IAAY,CAAC,WAAW;gBACpC,kBAAkB,6IAAY,CAAC,iBAAiB;YAClD;QACF;8BAAG;QAAC;KAAK;IAET,MAAM,oBAAoB,CAAC,YAAoB;QAC7C,IAAI,CAAC,MAAM;QAEX,MAAM,aAAsB;YAC1B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,UAAU,KAAK,EAAE;YACjB;YACA;YACA,WAAW,KAAK,GAAG;YACnB,MAAM;QACR;QAEA,6IAAY,CAAC,UAAU,CAAC;QACxB,YAAY,6IAAY,CAAC,WAAW;IACtC;IAEA,MAAM,0BAA0B,CAAC;QAC/B,IAAI,CAAC,MAAM;QAEX,MAAM,kBAAkB,eAAe,IAAI,CACzC,CAAA,MAAO,IAAI,QAAQ,KAAK,KAAK,EAAE,IAAI,IAAI,UAAU,KAAK,cAAc,IAAI,MAAM,KAAK;QAGrF,IAAI,iBAAiB;QAErB,MAAM,aAA4B;YAChC,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,UAAU,KAAK,EAAE;YACjB;YACA,QAAQ;YACR,WAAW,KAAK,GAAG;QACrB;QAEA,6IAAY,CAAC,gBAAgB,CAAC;QAC9B,kBAAkB,6IAAY,CAAC,iBAAiB;IAClD;IAEA,MAAM,8BAA8B,CAAC,WAAmB;QACtD,6IAAY,CAAC,mBAAmB,CAAC,WAAW;QAC5C,kBAAkB,6IAAY,CAAC,iBAAiB;IAClD;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,yMAAC;YAAI,WAAU;sBACb,cAAA,yMAAC;gBAAI,WAAU;0BACb,cAAA,yMAAC;oBAAG,WAAU;8BAAwC;;;;;;;;;;;;;;;;IAI9D;IAEA,MAAM,yBAAyB,eAAe,MAAM,CAClD,CAAA,MAAO,IAAI,UAAU,KAAK,KAAK,EAAE,IAAI,IAAI,MAAM,KAAK;IAGtD,qBACE,yMAAC;QAAI,WAAU;;0BACb,yMAAC,mJAAU;gBACT,WAAW;gBACX,cAAc;gBACd,sBAAsB,uBAAuB,MAAM;;;;;;0BAGrD,yMAAC;gBAAK,WAAU;0BACd,cAAA,yMAAC;oBAAI,WAAU;;wBACZ,cAAc,4BACb,yMAAC,oJAAW;4BACV,eAAe,KAAK,EAAE;4BACtB,OAAO;4BACP,UAAU;4BACV,eAAe;;;;;;wBAIlB,cAAc,0BACb,yMAAC,mJAAU;4BACT,eAAe,KAAK,EAAE;4BACtB,OAAO;4BACP,gBAAgB;4BAChB,qBAAqB;4BACrB,eAAe;;;;;;wBAIlB,cAAc,2BACb,yMAAC;4BAAI,WAAU;;8CACb,yMAAC;oCAAG,WAAU;8CAAwC;;;;;;gCACrD,uBAAuB,MAAM,KAAK,kBACjC,yMAAC;oCAAE,WAAU;8CAAiC;;;;;yDAE9C,yMAAC;oCAAI,WAAU;8CACZ,uBAAuB,GAAG,CAAC,CAAA,wBAC1B,yMAAC,0JAAiB;4CAEhB,SAAS;4CACT,OAAO;4CACP,YAAY;2CAHP,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcrC;GAvIwB;;QACL,sLAAO;;;KADF", "debugId": null}}, {"offset": {"line": 1572, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(type, key, props, owner, debugStack, debugTask) {\n      var refProp = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== refProp ? refProp : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        maybeKey,\n        getOwner(),\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (type, config, maybeKey, isStaticChildren) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,KAAK,WAAW,IAAI;YAC7B,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aAAa,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS;QAClE,IAAI,UAAU,MAAM,GAAG;QACvB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,UAAU,UAAU,IAAI,IACzC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,UACA,YACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,gIACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,0BAA0B,SAAU,iBAAiB;YACnD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,MAAM,wBAAwB,CAAC,IAAI,CAC9D,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SAAU,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,gBAAgB;QACjE,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1777, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA;;KAEO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}]}