import type { InstanceSettingsJSON } from './JSON';
export declare class InstanceSettings {
    readonly id?: string | undefined;
    readonly restrictedToAllowlist?: boolean | undefined;
    readonly fromEmailAddress?: string | undefined;
    readonly progressiveSignUp?: boolean | undefined;
    readonly enhancedEmailDeliverability?: boolean | undefined;
    constructor(id?: string | undefined, restrictedToAllowlist?: boolean | undefined, fromEmailAddress?: string | undefined, progressiveSignUp?: boolean | undefined, enhancedEmailDeliverability?: boolean | undefined);
    static fromJSON(data: InstanceSettingsJSON): InstanceSettings;
}
//# sourceMappingURL=InstanceSettings.d.ts.map