import type { SignInTokenJSON } from './JSON';
export declare class SignInToken {
    readonly id: string;
    readonly userId: string;
    readonly token: string;
    readonly status: string;
    readonly url: string;
    readonly createdAt: number;
    readonly updatedAt: number;
    constructor(id: string, userId: string, token: string, status: string, url: string, createdAt: number, updatedAt: number);
    static fromJSO<PERSON>(data: SignInTokenJSON): SignInToken;
}
//# sourceMappingURL=SignInTokens.d.ts.map