{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/app/api/users/route.ts"], "sourcesContent": ["import { auth, currentUser } from '@clerk/nextjs/server'\nimport { NextResponse } from 'next/server'\n\n// Simple in-memory storage for demo purposes\n// In production, you'd use a real database\nlet registeredUsers: any[] = []\n\nexport async function GET(request: Request) {\n  try {\n    const { userId } = await auth()\n\n    if (!userId) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const { searchParams } = new URL(request.url)\n    const query = searchParams.get('q') || ''\n\n    // Filter users based on search query and exclude current user\n    const filteredUsers = registeredUsers\n      .filter(user => user.id !== userId)\n      .filter(user => {\n        if (!query) return true\n\n        const searchTerm = query.toLowerCase()\n        const username = user.username?.toLowerCase() || ''\n        const firstName = user.firstName?.toLowerCase() || ''\n        const lastName = user.lastName?.toLowerCase() || ''\n        const email = user.emailAddress?.toLowerCase() || ''\n\n        return username.includes(searchTerm) ||\n               firstName.includes(searchTerm) ||\n               lastName.includes(searchTerm) ||\n               email.includes(searchTerm)\n      })\n\n    return NextResponse.json({ users: filteredUsers })\n  } catch (error) {\n    console.error('Error fetching users:', error)\n    return NextResponse.json({ error: 'Failed to fetch users' }, { status: 500 })\n  }\n}\n\nexport async function POST(request: Request) {\n  try {\n    const { userId } = await auth()\n\n    if (!userId) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const user = await currentUser()\n\n    if (!user) {\n      return NextResponse.json({ error: 'User not found' }, { status: 404 })\n    }\n\n    const userData = {\n      id: user.id,\n      username: user.username || user.emailAddresses[0]?.emailAddress?.split('@')[0] || 'user',\n      firstName: user.firstName || '',\n      lastName: user.lastName || '',\n      imageUrl: user.imageUrl,\n      emailAddress: user.emailAddresses[0]?.emailAddress || ''\n    }\n\n    // Check if user already exists\n    const existingUserIndex = registeredUsers.findIndex(u => u.id === user.id)\n    if (existingUserIndex !== -1) {\n      // Update existing user\n      registeredUsers[existingUserIndex] = userData\n    } else {\n      // Add new user\n      registeredUsers.push(userData)\n    }\n\n    return NextResponse.json({ user: userData })\n  } catch (error) {\n    console.error('Error registering user:', error)\n    return NextResponse.json({ error: 'Failed to register user' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AACA;;;AAEA,6CAA6C;AAC7C,2CAA2C;AAC3C,IAAI,kBAAyB,EAAE;AAExB,eAAe,IAAI,OAAgB;IACxC,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAA,yMAAI;QAE7B,IAAI,CAAC,QAAQ;YACX,OAAO,4JAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,QAAQ,aAAa,GAAG,CAAC,QAAQ;QAEvC,8DAA8D;QAC9D,MAAM,gBAAgB,gBACnB,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,QAC3B,MAAM,CAAC,CAAA;YACN,IAAI,CAAC,OAAO,OAAO;YAEnB,MAAM,aAAa,MAAM,WAAW;YACpC,MAAM,WAAW,KAAK,QAAQ,EAAE,iBAAiB;YACjD,MAAM,YAAY,KAAK,SAAS,EAAE,iBAAiB;YACnD,MAAM,WAAW,KAAK,QAAQ,EAAE,iBAAiB;YACjD,MAAM,QAAQ,KAAK,YAAY,EAAE,iBAAiB;YAElD,OAAO,SAAS,QAAQ,CAAC,eAClB,UAAU,QAAQ,CAAC,eACnB,SAAS,QAAQ,CAAC,eAClB,MAAM,QAAQ,CAAC;QACxB;QAEF,OAAO,4JAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAc;IAClD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,4JAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF;AAEO,eAAe,KAAK,OAAgB;IACzC,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAA,yMAAI;QAE7B,IAAI,CAAC,QAAQ;YACX,OAAO,4JAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,OAAO,MAAM,IAAA,uNAAW;QAE9B,IAAI,CAAC,MAAM;YACT,OAAO,4JAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAiB,GAAG;gBAAE,QAAQ;YAAI;QACtE;QAEA,MAAM,WAAW;YACf,IAAI,KAAK,EAAE;YACX,UAAU,KAAK,QAAQ,IAAI,KAAK,cAAc,CAAC,EAAE,EAAE,cAAc,MAAM,IAAI,CAAC,EAAE,IAAI;YAClF,WAAW,KAAK,SAAS,IAAI;YAC7B,UAAU,KAAK,QAAQ,IAAI;YAC3B,UAAU,KAAK,QAAQ;YACvB,cAAc,KAAK,cAAc,CAAC,EAAE,EAAE,gBAAgB;QACxD;QAEA,+BAA+B;QAC/B,MAAM,oBAAoB,gBAAgB,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE;QACzE,IAAI,sBAAsB,CAAC,GAAG;YAC5B,uBAAuB;YACvB,eAAe,CAAC,kBAAkB,GAAG;QACvC,OAAO;YACL,eAAe;YACf,gBAAgB,IAAI,CAAC;QACvB;QAEA,OAAO,4JAAY,CAAC,IAAI,CAAC;YAAE,MAAM;QAAS;IAC5C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,4JAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAA0B,GAAG;YAAE,QAAQ;QAAI;IAC/E;AACF", "debugId": null}}]}