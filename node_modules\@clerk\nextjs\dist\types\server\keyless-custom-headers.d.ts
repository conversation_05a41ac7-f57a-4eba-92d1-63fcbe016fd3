interface MetadataHeaders {
    nodeVersion?: string;
    nextVersion?: string;
    npmConfigUserAgent?: string;
    userAgent?: string;
}
/**
 * Collects metadata from the environment and request headers
 */
export declare function collectKeylessMetadata(): Promise<MetadataHeaders>;
/**
 * Converts metadata to HTTP headers
 */
export declare function formatMetadataHeaders(metadata: MetadataHeaders): Headers;
export {};
//# sourceMappingURL=keyless-custom-headers.d.ts.map