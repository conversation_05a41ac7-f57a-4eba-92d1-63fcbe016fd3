{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/app/api/users/%5Bid%5D/route.ts"], "sourcesContent": ["import { auth } from '@clerk/nextjs/server'\nimport { NextResponse } from 'next/server'\n\n// Import the same storage from the main users route\n// In production, this would be a database query\nlet registeredUsers: any[] = []\n\nexport async function GET(\n  request: Request,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const { userId } = await auth()\n\n    if (!userId) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const user = registeredUsers.find(u => u.id === params.id)\n\n    if (!user) {\n      return NextResponse.json({ error: 'User not found' }, { status: 404 })\n    }\n\n    return NextResponse.json({ user })\n  } catch (error) {\n    console.error('Error fetching user:', error)\n    return NextResponse.json({ error: 'User not found' }, { status: 404 })\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,oDAAoD;AACpD,gDAAgD;AAChD,IAAI,kBAAyB,EAAE;AAExB,eAAe,IACpB,OAAgB,EAChB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAA,yMAAI;QAE7B,IAAI,CAAC,QAAQ;YACX,OAAO,4JAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,OAAO,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO,EAAE;QAEzD,IAAI,CAAC,MAAM;YACT,OAAO,4JAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAiB,GAAG;gBAAE,QAAQ;YAAI;QACtE;QAEA,OAAO,4JAAY,CAAC,IAAI,CAAC;YAAE;QAAK;IAClC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,4JAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAiB,GAAG;YAAE,QAAQ;QAAI;IACtE;AACF", "debugId": null}}]}