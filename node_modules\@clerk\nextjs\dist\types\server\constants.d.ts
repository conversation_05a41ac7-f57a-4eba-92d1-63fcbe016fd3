export declare const CLERK_JS_VERSION: string;
export declare const CLERK_JS_URL: string;
export declare const API_VERSION: string;
export declare const SECRET_KEY: string;
export declare const MACHINE_SECRET_KEY: string;
export declare const PUBLISHABLE_KEY: string;
export declare const ENCRYPTION_KEY: string;
export declare const API_URL: string;
export declare const DOMAIN: string;
export declare const PROXY_URL: string;
export declare const IS_SATELLITE: boolean;
export declare const SIGN_IN_URL: string;
export declare const SIGN_UP_URL: string;
export declare const SDK_METADATA: {
    name: string;
    version: string;
    environment: "development" | "production" | "test";
};
export declare const TELEMETRY_DISABLED: boolean;
export declare const TELEMETRY_DEBUG: boolean;
export declare const KEYLESS_DISABLED: boolean;
//# sourceMappingURL=constants.d.ts.map