import { auth } from '@clerk/nextjs/server'
import { NextResponse } from 'next/server'

// In-memory storage for messages (in production, use a real database)
let messages: any[] = []

export async function GET(request: Request) {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const otherUserId = searchParams.get('otherUserId')

    if (otherUserId) {
      // Get conversation between two users
      const conversationMessages = messages.filter(
        msg => 
          (msg.senderId === userId && msg.receiverId === otherUserId) ||
          (msg.senderId === otherUserId && msg.receiverId === userId)
      ).sort((a, b) => a.timestamp - b.timestamp)

      return NextResponse.json({ messages: conversationMessages })
    } else {
      // Get all messages for the current user
      const userMessages = messages.filter(
        msg => msg.senderId === userId || msg.receiverId === userId
      )

      return NextResponse.json({ messages: userMessages })
    }
  } catch (error) {
    console.error('Error fetching messages:', error)
    return NextResponse.json({ error: 'Failed to fetch messages' }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { receiverId, content } = await request.json()

    if (!receiverId || !content) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    const newMessage = {
      id: Date.now().toString(),
      senderId: userId,
      receiverId,
      content,
      timestamp: Date.now(),
      read: false
    }

    messages.push(newMessage)

    return NextResponse.json({ message: newMessage })
  } catch (error) {
    console.error('Error sending message:', error)
    return NextResponse.json({ error: 'Failed to send message' }, { status: 500 })
  }
}
