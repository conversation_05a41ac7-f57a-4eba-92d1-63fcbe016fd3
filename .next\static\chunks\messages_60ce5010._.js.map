{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/lib/storage.ts"], "sourcesContent": ["import { User, Message, FriendRequest, Conversation, AppState } from './types';\n\nconst STORAGE_KEYS = {\n  USERS: 'messaging_app_users',\n  MESSAGES: 'messaging_app_messages',\n  FRIEND_REQUESTS: 'messaging_app_friend_requests',\n  CONVERSATIONS: 'messaging_app_conversations',\n};\n\n// Initialize storage\nconst initializeStorage = () => {\n  if (typeof window === 'undefined') return;\n\n  // Initialize storage items if they don't exist\n  if (!localStorage.getItem(STORAGE_KEYS.USERS)) {\n    localStorage.setItem(STORAGE_KEYS.USERS, JSON.stringify([]));\n  }\n  if (!localStorage.getItem(STORAGE_KEYS.MESSAGES)) {\n    localStorage.setItem(STORAGE_KEYS.MESSAGES, JSON.stringify([]));\n  }\n  if (!localStorage.getItem(STORAGE_KEYS.FRIEND_REQUESTS)) {\n    localStorage.setItem(STORAGE_KEYS.FRIEND_REQUESTS, JSON.stringify([]));\n  }\n  if (!localStorage.getItem(STORAGE_KEYS.CONVERSATIONS)) {\n    localStorage.setItem(STORAGE_KEYS.CONVERSATIONS, JSON.stringify([]));\n  }\n};\n\nexport const storageUtils = {\n  // Users\n  getUsers: (): User[] => {\n    if (typeof window === 'undefined') return [];\n    initializeStorage();\n    const users = localStorage.getItem(STORAGE_KEYS.USERS);\n    return users ? JSON.parse(users) : [];\n  },\n\n  addUser: (user: User): void => {\n    if (typeof window === 'undefined') return;\n    const users = storageUtils.getUsers();\n    const existingUser = users.find(u => u.id === user.id);\n    if (!existingUser) {\n      users.push(user);\n      localStorage.setItem(STORAGE_KEYS.USERS, JSON.stringify(users));\n    }\n  },\n\n  // Messages\n  getMessages: (): Message[] => {\n    if (typeof window === 'undefined') return [];\n    const messages = localStorage.getItem(STORAGE_KEYS.MESSAGES);\n    return messages ? JSON.parse(messages) : [];\n  },\n\n  addMessage: (message: Message): void => {\n    if (typeof window === 'undefined') return;\n    const messages = storageUtils.getMessages();\n    messages.push(message);\n    localStorage.setItem(STORAGE_KEYS.MESSAGES, JSON.stringify(messages));\n  },\n\n  getConversationMessages: (userId1: string, userId2: string): Message[] => {\n    const messages = storageUtils.getMessages();\n    return messages.filter(\n      msg => \n        (msg.senderId === userId1 && msg.receiverId === userId2) ||\n        (msg.senderId === userId2 && msg.receiverId === userId1)\n    ).sort((a, b) => a.timestamp - b.timestamp);\n  },\n\n  // Friend Requests\n  getFriendRequests: (): FriendRequest[] => {\n    if (typeof window === 'undefined') return [];\n    const requests = localStorage.getItem(STORAGE_KEYS.FRIEND_REQUESTS);\n    return requests ? JSON.parse(requests) : [];\n  },\n\n  addFriendRequest: (request: FriendRequest): void => {\n    if (typeof window === 'undefined') return;\n    const requests = storageUtils.getFriendRequests();\n    requests.push(request);\n    localStorage.setItem(STORAGE_KEYS.FRIEND_REQUESTS, JSON.stringify(requests));\n  },\n\n  updateFriendRequest: (requestId: string, status: 'accepted' | 'rejected'): void => {\n    if (typeof window === 'undefined') return;\n    const requests = storageUtils.getFriendRequests();\n    const requestIndex = requests.findIndex(r => r.id === requestId);\n    if (requestIndex !== -1) {\n      requests[requestIndex].status = status;\n      localStorage.setItem(STORAGE_KEYS.FRIEND_REQUESTS, JSON.stringify(requests));\n    }\n  },\n\n  // Conversations\n  getConversations: (): Conversation[] => {\n    if (typeof window === 'undefined') return [];\n    const conversations = localStorage.getItem(STORAGE_KEYS.CONVERSATIONS);\n    return conversations ? JSON.parse(conversations) : [];\n  },\n\n  updateConversation: (conversation: Conversation): void => {\n    if (typeof window === 'undefined') return;\n    const conversations = storageUtils.getConversations();\n    const existingIndex = conversations.findIndex(c => c.id === conversation.id);\n    if (existingIndex !== -1) {\n      conversations[existingIndex] = conversation;\n    } else {\n      conversations.push(conversation);\n    }\n    localStorage.setItem(STORAGE_KEYS.CONVERSATIONS, JSON.stringify(conversations));\n  },\n\n  // Search users\n  searchUsers: (query: string): User[] => {\n    const users = storageUtils.getUsers();\n    return users.filter(user => \n      user.username.toLowerCase().includes(query.toLowerCase()) ||\n      user.firstName?.toLowerCase().includes(query.toLowerCase()) ||\n      user.lastName?.toLowerCase().includes(query.toLowerCase())\n    );\n  }\n};\n\n// Initialize storage on import\nif (typeof window !== 'undefined') {\n  initializeStorage();\n}\n"], "names": [], "mappings": ";;;;AAEA,MAAM,eAAe;IACnB,OAAO;IACP,UAAU;IACV,iBAAiB;IACjB,eAAe;AACjB;AAEA,qBAAqB;AACrB,MAAM,oBAAoB;IACxB;;IAEA,+CAA+C;IAC/C,IAAI,CAAC,aAAa,OAAO,CAAC,aAAa,KAAK,GAAG;QAC7C,aAAa,OAAO,CAAC,aAAa,KAAK,EAAE,KAAK,SAAS,CAAC,EAAE;IAC5D;IACA,IAAI,CAAC,aAAa,OAAO,CAAC,aAAa,QAAQ,GAAG;QAChD,aAAa,OAAO,CAAC,aAAa,QAAQ,EAAE,KAAK,SAAS,CAAC,EAAE;IAC/D;IACA,IAAI,CAAC,aAAa,OAAO,CAAC,aAAa,eAAe,GAAG;QACvD,aAAa,OAAO,CAAC,aAAa,eAAe,EAAE,KAAK,SAAS,CAAC,EAAE;IACtE;IACA,IAAI,CAAC,aAAa,OAAO,CAAC,aAAa,aAAa,GAAG;QACrD,aAAa,OAAO,CAAC,aAAa,aAAa,EAAE,KAAK,SAAS,CAAC,EAAE;IACpE;AACF;AAEO,MAAM,eAAe;IAC1B,QAAQ;IACR,UAAU;QACR;;QACA;QACA,MAAM,QAAQ,aAAa,OAAO,CAAC,aAAa,KAAK;QACrD,OAAO,QAAQ,KAAK,KAAK,CAAC,SAAS,EAAE;IACvC;IAEA,SAAS,CAAC;QACR;;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,MAAM,eAAe,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE;QACrD,IAAI,CAAC,cAAc;YACjB,MAAM,IAAI,CAAC;YACX,aAAa,OAAO,CAAC,aAAa,KAAK,EAAE,KAAK,SAAS,CAAC;QAC1D;IACF;IAEA,WAAW;IACX,aAAa;QACX;;QACA,MAAM,WAAW,aAAa,OAAO,CAAC,aAAa,QAAQ;QAC3D,OAAO,WAAW,KAAK,KAAK,CAAC,YAAY,EAAE;IAC7C;IAEA,YAAY,CAAC;QACX;;QACA,MAAM,WAAW,aAAa,WAAW;QACzC,SAAS,IAAI,CAAC;QACd,aAAa,OAAO,CAAC,aAAa,QAAQ,EAAE,KAAK,SAAS,CAAC;IAC7D;IAEA,yBAAyB,CAAC,SAAiB;QACzC,MAAM,WAAW,aAAa,WAAW;QACzC,OAAO,SAAS,MAAM,CACpB,CAAA,MACE,AAAC,IAAI,QAAQ,KAAK,WAAW,IAAI,UAAU,KAAK,WAC/C,IAAI,QAAQ,KAAK,WAAW,IAAI,UAAU,KAAK,SAClD,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;IAC5C;IAEA,kBAAkB;IAClB,mBAAmB;QACjB;;QACA,MAAM,WAAW,aAAa,OAAO,CAAC,aAAa,eAAe;QAClE,OAAO,WAAW,KAAK,KAAK,CAAC,YAAY,EAAE;IAC7C;IAEA,kBAAkB,CAAC;QACjB;;QACA,MAAM,WAAW,aAAa,iBAAiB;QAC/C,SAAS,IAAI,CAAC;QACd,aAAa,OAAO,CAAC,aAAa,eAAe,EAAE,KAAK,SAAS,CAAC;IACpE;IAEA,qBAAqB,CAAC,WAAmB;QACvC;;QACA,MAAM,WAAW,aAAa,iBAAiB;QAC/C,MAAM,eAAe,SAAS,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACtD,IAAI,iBAAiB,CAAC,GAAG;YACvB,QAAQ,CAAC,aAAa,CAAC,MAAM,GAAG;YAChC,aAAa,OAAO,CAAC,aAAa,eAAe,EAAE,KAAK,SAAS,CAAC;QACpE;IACF;IAEA,gBAAgB;IAChB,kBAAkB;QAChB;;QACA,MAAM,gBAAgB,aAAa,OAAO,CAAC,aAAa,aAAa;QACrE,OAAO,gBAAgB,KAAK,KAAK,CAAC,iBAAiB,EAAE;IACvD;IAEA,oBAAoB,CAAC;QACnB;;QACA,MAAM,gBAAgB,aAAa,gBAAgB;QACnD,MAAM,gBAAgB,cAAc,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,aAAa,EAAE;QAC3E,IAAI,kBAAkB,CAAC,GAAG;YACxB,aAAa,CAAC,cAAc,GAAG;QACjC,OAAO;YACL,cAAc,IAAI,CAAC;QACrB;QACA,aAAa,OAAO,CAAC,aAAa,aAAa,EAAE,KAAK,SAAS,CAAC;IAClE;IAEA,eAAe;IACf,aAAa,CAAC;QACZ,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,MAAM,MAAM,CAAC,CAAA;gBAElB,iBACA;mBAFA,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW,SACtD,kBAAA,KAAK,SAAS,cAAd,sCAAA,gBAAgB,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW,UACxD,iBAAA,KAAK,QAAQ,cAAb,qCAAA,eAAe,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW;;IAE3D;AACF;AAEA,+BAA+B;AAC/B,wCAAmC;IACjC;AACF", "debugId": null}}, {"offset": {"line": 132, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/components/UserSearch.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { User, FriendRequest } from '@/lib/types'\n\ninterface UserSearchProps {\n  currentUserId: string\n  users: User[]\n  friendRequests: FriendRequest[]\n  onSendFriendRequest: (receiverId: string) => void\n  onSendMessage: (receiverId: string, content: string) => void\n}\n\nexport default function UserSearch({\n  currentUserId,\n  users,\n  friendRequests,\n  onSendFriendRequest,\n  onSendMessage\n}: UserSearchProps) {\n  const [searchQuery, setSearchQuery] = useState('')\n  const [searchResults, setSearchResults] = useState<User[]>([])\n  const [messageContent, setMessageContent] = useState<{ [key: string]: string }>({})\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleSearch = async (query: string) => {\n    setSearchQuery(query)\n    if (query.trim()) {\n      setIsLoading(true)\n      try {\n        const response = await fetch(`/api/users?q=${encodeURIComponent(query)}`)\n        if (response.ok) {\n          const data = await response.json()\n          setSearchResults(data.users)\n        } else {\n          console.error('Failed to search users')\n          setSearchResults([])\n        }\n      } catch (error) {\n        console.error('Error searching users:', error)\n        setSearchResults([])\n      } finally {\n        setIsLoading(false)\n      }\n    } else {\n      setSearchResults([])\n    }\n  }\n\n  const getFriendRequestStatus = (userId: string) => {\n    const sentRequest = friendRequests.find(\n      req => req.senderId === currentUserId && req.receiverId === userId\n    )\n    const receivedRequest = friendRequests.find(\n      req => req.senderId === userId && req.receiverId === currentUserId\n    )\n    \n    if (sentRequest) return sentRequest.status\n    if (receivedRequest) return 'received'\n    return null\n  }\n\n  const handleSendMessage = (receiverId: string) => {\n    const content = messageContent[receiverId]\n    if (content?.trim()) {\n      onSendMessage(receiverId, content.trim())\n      setMessageContent(prev => ({ ...prev, [receiverId]: '' }))\n    }\n  }\n\n  return (\n    <div className=\"p-6\">\n      <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Search Users</h2>\n      \n      <div className=\"mb-6\">\n        <input\n          type=\"text\"\n          placeholder=\"Search by username, first name, or last name...\"\n          value={searchQuery}\n          onChange={(e) => handleSearch(e.target.value)}\n          className=\"w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n        />\n      </div>\n\n      {searchQuery && !isLoading && searchResults.length === 0 && (\n        <p className=\"text-gray-500 text-center py-8\">No users found matching \"{searchQuery}\"</p>\n      )}\n\n      {isLoading && (\n        <div className=\"text-center py-8\">\n          <div className=\"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n          <p className=\"text-gray-500 mt-2\">Searching users...</p>\n        </div>\n      )}\n\n      <div className=\"space-y-4\">\n        {searchResults.map(user => {\n          const friendStatus = getFriendRequestStatus(user.id)\n          \n          return (\n            <div key={user.id} className=\"border border-gray-200 rounded-lg p-4\">\n              <div className=\"flex items-start space-x-4\">\n                <img\n                  src={user.imageUrl || `https://ui-avatars.com/api/?name=${user.firstName}+${user.lastName}&background=random`}\n                  alt={`${user.firstName} ${user.lastName}`}\n                  className=\"w-12 h-12 rounded-full object-cover\"\n                />\n                \n                <div className=\"flex-1\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <h3 className=\"text-lg font-medium text-gray-900\">\n                        {user.firstName} {user.lastName}\n                      </h3>\n                      <p className=\"text-sm text-gray-500\">@{user.username}</p>\n                      {user.emailAddress && (\n                        <p className=\"text-sm text-gray-500\">{user.emailAddress}</p>\n                      )}\n                    </div>\n                    \n                    <div className=\"flex space-x-2\">\n                      {friendStatus === 'pending' && (\n                        <span className=\"px-3 py-1 bg-yellow-100 text-yellow-800 text-sm rounded-full\">\n                          Request Sent\n                        </span>\n                      )}\n                      {friendStatus === 'accepted' && (\n                        <span className=\"px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full\">\n                          Friends\n                        </span>\n                      )}\n                      {friendStatus === 'received' && (\n                        <span className=\"px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full\">\n                          Sent You Request\n                        </span>\n                      )}\n                      {!friendStatus && (\n                        <button\n                          onClick={() => onSendFriendRequest(user.id)}\n                          className=\"px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700\"\n                        >\n                          Add Friend\n                        </button>\n                      )}\n                    </div>\n                  </div>\n                  \n                  <div className=\"mt-4\">\n                    <div className=\"flex space-x-2\">\n                      <input\n                        type=\"text\"\n                        placeholder=\"Type a message...\"\n                        value={messageContent[user.id] || ''}\n                        onChange={(e) => setMessageContent(prev => ({ \n                          ...prev, \n                          [user.id]: e.target.value \n                        }))}\n                        className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500\"\n                        onKeyPress={(e) => {\n                          if (e.key === 'Enter') {\n                            handleSendMessage(user.id)\n                          }\n                        }}\n                      />\n                      <button\n                        onClick={() => handleSendMessage(user.id)}\n                        disabled={!messageContent[user.id]?.trim()}\n                        className=\"px-4 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed\"\n                      >\n                        Send\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )\n        })}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAae,SAAS,WAAW,KAMjB;QANiB,EACjC,aAAa,EACb,KAAK,EACL,cAAc,EACd,mBAAmB,EACnB,aAAa,EACG,GANiB;;IAOjC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,qLAAQ,EAAC;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,qLAAQ,EAAS,EAAE;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,qLAAQ,EAA4B,CAAC;IACjF,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,qLAAQ,EAAC;IAE3C,MAAM,eAAe,OAAO;QAC1B,eAAe;QACf,IAAI,MAAM,IAAI,IAAI;YAChB,aAAa;YACb,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,AAAC,gBAAyC,OAA1B,mBAAmB;gBAChE,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,iBAAiB,KAAK,KAAK;gBAC7B,OAAO;oBACL,QAAQ,KAAK,CAAC;oBACd,iBAAiB,EAAE;gBACrB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,iBAAiB,EAAE;YACrB,SAAU;gBACR,aAAa;YACf;QACF,OAAO;YACL,iBAAiB,EAAE;QACrB;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,MAAM,cAAc,eAAe,IAAI,CACrC,CAAA,MAAO,IAAI,QAAQ,KAAK,iBAAiB,IAAI,UAAU,KAAK;QAE9D,MAAM,kBAAkB,eAAe,IAAI,CACzC,CAAA,MAAO,IAAI,QAAQ,KAAK,UAAU,IAAI,UAAU,KAAK;QAGvD,IAAI,aAAa,OAAO,YAAY,MAAM;QAC1C,IAAI,iBAAiB,OAAO;QAC5B,OAAO;IACT;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,UAAU,cAAc,CAAC,WAAW;QAC1C,IAAI,oBAAA,8BAAA,QAAS,IAAI,IAAI;YACnB,cAAc,YAAY,QAAQ,IAAI;YACtC,kBAAkB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,WAAW,EAAE;gBAAG,CAAC;QAC1D;IACF;IAEA,qBACE,yMAAC;QAAI,WAAU;;0BACb,yMAAC;gBAAG,WAAU;0BAAwC;;;;;;0BAEtD,yMAAC;gBAAI,WAAU;0BACb,cAAA,yMAAC;oBACC,MAAK;oBACL,aAAY;oBACZ,OAAO;oBACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oBAC5C,WAAU;;;;;;;;;;;YAIb,eAAe,CAAC,aAAa,cAAc,MAAM,KAAK,mBACrD,yMAAC;gBAAE,WAAU;;oBAAiC;oBAA0B;oBAAY;;;;;;;YAGrF,2BACC,yMAAC;gBAAI,WAAU;;kCACb,yMAAC;wBAAI,WAAU;;;;;;kCACf,yMAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;0BAItC,yMAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAA;wBAsEQ;oBArEzB,MAAM,eAAe,uBAAuB,KAAK,EAAE;oBAEnD,qBACE,yMAAC;wBAAkB,WAAU;kCAC3B,cAAA,yMAAC;4BAAI,WAAU;;8CACb,yMAAC;oCACC,KAAK,KAAK,QAAQ,IAAI,AAAC,oCAAqD,OAAlB,KAAK,SAAS,EAAC,KAAiB,OAAd,KAAK,QAAQ,EAAC;oCAC1F,KAAK,AAAC,GAAoB,OAAlB,KAAK,SAAS,EAAC,KAAiB,OAAd,KAAK,QAAQ;oCACvC,WAAU;;;;;;8CAGZ,yMAAC;oCAAI,WAAU;;sDACb,yMAAC;4CAAI,WAAU;;8DACb,yMAAC;;sEACC,yMAAC;4DAAG,WAAU;;gEACX,KAAK,SAAS;gEAAC;gEAAE,KAAK,QAAQ;;;;;;;sEAEjC,yMAAC;4DAAE,WAAU;;gEAAwB;gEAAE,KAAK,QAAQ;;;;;;;wDACnD,KAAK,YAAY,kBAChB,yMAAC;4DAAE,WAAU;sEAAyB,KAAK,YAAY;;;;;;;;;;;;8DAI3D,yMAAC;oDAAI,WAAU;;wDACZ,iBAAiB,2BAChB,yMAAC;4DAAK,WAAU;sEAA+D;;;;;;wDAIhF,iBAAiB,4BAChB,yMAAC;4DAAK,WAAU;sEAA6D;;;;;;wDAI9E,iBAAiB,4BAChB,yMAAC;4DAAK,WAAU;sEAA2D;;;;;;wDAI5E,CAAC,8BACA,yMAAC;4DACC,SAAS,IAAM,oBAAoB,KAAK,EAAE;4DAC1C,WAAU;sEACX;;;;;;;;;;;;;;;;;;sDAOP,yMAAC;4CAAI,WAAU;sDACb,cAAA,yMAAC;gDAAI,WAAU;;kEACb,yMAAC;wDACC,MAAK;wDACL,aAAY;wDACZ,OAAO,cAAc,CAAC,KAAK,EAAE,CAAC,IAAI;wDAClC,UAAU,CAAC,IAAM,kBAAkB,CAAA,OAAQ,CAAC;oEAC1C,GAAG,IAAI;oEACP,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;gEAC3B,CAAC;wDACD,WAAU;wDACV,YAAY,CAAC;4DACX,IAAI,EAAE,GAAG,KAAK,SAAS;gEACrB,kBAAkB,KAAK,EAAE;4DAC3B;wDACF;;;;;;kEAEF,yMAAC;wDACC,SAAS,IAAM,kBAAkB,KAAK,EAAE;wDACxC,UAAU,GAAC,0BAAA,cAAc,CAAC,KAAK,EAAE,CAAC,cAAvB,8CAAA,wBAAyB,IAAI;wDACxC,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBApED,KAAK,EAAE;;;;;gBA6ErB;;;;;;;;;;;;AAIR;GAxKwB;KAAA", "debugId": null}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/components/ChatWindow.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport { User, Message } from '@/lib/types'\n\ninterface ChatWindowProps {\n  currentUserId: string\n  otherUserId: string\n  otherUser?: User | null\n  messages: Message[]\n  onSendMessage: (receiverId: string, content: string) => void\n  isLoadingUser?: boolean\n}\n\nexport default function ChatWindow({\n  currentUserId,\n  otherUserId,\n  otherUser,\n  messages,\n  onSendMessage,\n  isLoadingUser = false\n}: ChatWindowProps) {\n  const [newMessage, setNewMessage] = useState('')\n  const messagesEndRef = useRef<HTMLDivElement>(null)\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })\n  }\n\n  useEffect(() => {\n    scrollToBottom()\n  }, [messages])\n\n  const handleSendMessage = () => {\n    if (newMessage.trim()) {\n      onSendMessage(otherUserId, newMessage.trim())\n      setNewMessage('')\n    }\n  }\n\n  const formatTime = (timestamp: number) => {\n    return new Date(timestamp).toLocaleTimeString([], { \n      hour: '2-digit', \n      minute: '2-digit' \n    })\n  }\n\n  const formatDate = (timestamp: number) => {\n    const date = new Date(timestamp)\n    const today = new Date()\n    const yesterday = new Date(today)\n    yesterday.setDate(yesterday.getDate() - 1)\n\n    if (date.toDateString() === today.toDateString()) {\n      return 'Today'\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Yesterday'\n    } else {\n      return date.toLocaleDateString([], { \n        weekday: 'long', \n        year: 'numeric', \n        month: 'long', \n        day: 'numeric' \n      })\n    }\n  }\n\n  const groupMessagesByDate = (messages: Message[]) => {\n    const groups: { [key: string]: Message[] } = {}\n    \n    messages.forEach(message => {\n      const dateKey = new Date(message.timestamp).toDateString()\n      if (!groups[dateKey]) {\n        groups[dateKey] = []\n      }\n      groups[dateKey].push(message)\n    })\n    \n    return Object.entries(groups).sort(([a], [b]) => \n      new Date(a).getTime() - new Date(b).getTime()\n    )\n  }\n\n  if (isLoadingUser) {\n    return (\n      <div className=\"flex items-center justify-center h-full\">\n        <div className=\"text-center\">\n          <div className=\"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n          <p className=\"text-gray-500 mt-2\">Loading user...</p>\n        </div>\n      </div>\n    )\n  }\n\n  if (!otherUser) {\n    return (\n      <div className=\"flex items-center justify-center h-full\">\n        <p className=\"text-gray-500\">User not found</p>\n      </div>\n    )\n  }\n\n  const messageGroups = groupMessagesByDate(messages)\n\n  return (\n    <div className=\"flex flex-col h-full\">\n      {/* Chat Header */}\n      <div className=\"p-4 border-b border-gray-200 bg-white\">\n        <div className=\"flex items-center space-x-3\">\n          <img\n            src={otherUser.imageUrl || `https://ui-avatars.com/api/?name=${otherUser.firstName}+${otherUser.lastName}&background=random`}\n            alt={`${otherUser.firstName} ${otherUser.lastName}`}\n            className=\"w-10 h-10 rounded-full object-cover\"\n          />\n          <div>\n            <h3 className=\"text-lg font-medium text-gray-900\">\n              {otherUser.firstName} {otherUser.lastName}\n            </h3>\n            <p className=\"text-sm text-gray-500\">@{otherUser.username}</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Messages */}\n      <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n        {messageGroups.length === 0 ? (\n          <div className=\"text-center text-gray-500 py-8\">\n            <p>No messages yet</p>\n            <p className=\"text-sm mt-1\">Start the conversation!</p>\n          </div>\n        ) : (\n          messageGroups.map(([dateKey, dayMessages]) => (\n            <div key={dateKey}>\n              {/* Date Separator */}\n              <div className=\"flex items-center justify-center my-4\">\n                <div className=\"bg-gray-100 px-3 py-1 rounded-full text-xs text-gray-600\">\n                  {formatDate(new Date(dateKey).getTime())}\n                </div>\n              </div>\n\n              {/* Messages for this date */}\n              {dayMessages.map((message, index) => {\n                const isCurrentUser = message.senderId === currentUserId\n                const showAvatar = !isCurrentUser && (\n                  index === 0 || \n                  dayMessages[index - 1]?.senderId !== message.senderId\n                )\n\n                return (\n                  <div\n                    key={message.id}\n                    className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'} mb-2`}\n                  >\n                    <div className={`flex items-end space-x-2 max-w-xs lg:max-w-md ${isCurrentUser ? 'flex-row-reverse space-x-reverse' : ''}`}>\n                      {showAvatar && !isCurrentUser && (\n                        <img\n                          src={otherUser.imageUrl || `https://ui-avatars.com/api/?name=${otherUser.firstName}+${otherUser.lastName}&background=random`}\n                          alt={`${otherUser.firstName} ${otherUser.lastName}`}\n                          className=\"w-6 h-6 rounded-full object-cover\"\n                        />\n                      )}\n                      {!showAvatar && !isCurrentUser && (\n                        <div className=\"w-6 h-6\" />\n                      )}\n                      \n                      <div\n                        className={`px-4 py-2 rounded-lg ${\n                          isCurrentUser\n                            ? 'bg-blue-600 text-white'\n                            : 'bg-gray-100 text-gray-900'\n                        }`}\n                      >\n                        <p className=\"text-sm\">{message.content}</p>\n                        <p className={`text-xs mt-1 ${\n                          isCurrentUser ? 'text-blue-100' : 'text-gray-500'\n                        }`}>\n                          {formatTime(message.timestamp)}\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n                )\n              })}\n            </div>\n          ))\n        )}\n        <div ref={messagesEndRef} />\n      </div>\n\n      {/* Message Input */}\n      <div className=\"p-4 border-t border-gray-200 bg-white\">\n        <div className=\"flex space-x-2\">\n          <input\n            type=\"text\"\n            value={newMessage}\n            onChange={(e) => setNewMessage(e.target.value)}\n            placeholder={`Message ${otherUser.firstName}...`}\n            className=\"flex-1 px-4 py-2 border border-gray-300 rounded-full focus:ring-blue-500 focus:border-blue-500\"\n            onKeyPress={(e) => {\n              if (e.key === 'Enter') {\n                handleSendMessage()\n              }\n            }}\n          />\n          <button\n            onClick={handleSendMessage}\n            disabled={!newMessage.trim()}\n            className=\"px-6 py-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed\"\n          >\n            Send\n          </button>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAce,SAAS,WAAW,KAOjB;QAPiB,EACjC,aAAa,EACb,WAAW,EACX,SAAS,EACT,QAAQ,EACR,aAAa,EACb,gBAAgB,KAAK,EACL,GAPiB;;IAQjC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,qLAAQ,EAAC;IAC7C,MAAM,iBAAiB,IAAA,mLAAM,EAAiB;IAE9C,MAAM,iBAAiB;YACrB;SAAA,0BAAA,eAAe,OAAO,cAAtB,8CAAA,wBAAwB,cAAc,CAAC;YAAE,UAAU;QAAS;IAC9D;IAEA,IAAA,sLAAS;gCAAC;YACR;QACF;+BAAG;QAAC;KAAS;IAEb,MAAM,oBAAoB;QACxB,IAAI,WAAW,IAAI,IAAI;YACrB,cAAc,aAAa,WAAW,IAAI;YAC1C,cAAc;QAChB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,WAAW,kBAAkB,CAAC,EAAE,EAAE;YAChD,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,QAAQ,IAAI;QAClB,MAAM,YAAY,IAAI,KAAK;QAC3B,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;QAExC,IAAI,KAAK,YAAY,OAAO,MAAM,YAAY,IAAI;YAChD,OAAO;QACT,OAAO,IAAI,KAAK,YAAY,OAAO,UAAU,YAAY,IAAI;YAC3D,OAAO;QACT,OAAO;YACL,OAAO,KAAK,kBAAkB,CAAC,EAAE,EAAE;gBACjC,SAAS;gBACT,MAAM;gBACN,OAAO;gBACP,KAAK;YACP;QACF;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,SAAuC,CAAC;QAE9C,SAAS,OAAO,CAAC,CAAA;YACf,MAAM,UAAU,IAAI,KAAK,QAAQ,SAAS,EAAE,YAAY;YACxD,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;gBACpB,MAAM,CAAC,QAAQ,GAAG,EAAE;YACtB;YACA,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;QACvB;QAEA,OAAO,OAAO,OAAO,CAAC,QAAQ,IAAI,CAAC;gBAAC,CAAC,EAAE,UAAE,CAAC,EAAE;mBAC1C,IAAI,KAAK,GAAG,OAAO,KAAK,IAAI,KAAK,GAAG,OAAO;;IAE/C;IAEA,IAAI,eAAe;QACjB,qBACE,yMAAC;YAAI,WAAU;sBACb,cAAA,yMAAC;gBAAI,WAAU;;kCACb,yMAAC;wBAAI,WAAU;;;;;;kCACf,yMAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,CAAC,WAAW;QACd,qBACE,yMAAC;YAAI,WAAU;sBACb,cAAA,yMAAC;gBAAE,WAAU;0BAAgB;;;;;;;;;;;IAGnC;IAEA,MAAM,gBAAgB,oBAAoB;IAE1C,qBACE,yMAAC;QAAI,WAAU;;0BAEb,yMAAC;gBAAI,WAAU;0BACb,cAAA,yMAAC;oBAAI,WAAU;;sCACb,yMAAC;4BACC,KAAK,UAAU,QAAQ,IAAI,AAAC,oCAA0D,OAAvB,UAAU,SAAS,EAAC,KAAsB,OAAnB,UAAU,QAAQ,EAAC;4BACzG,KAAK,AAAC,GAAyB,OAAvB,UAAU,SAAS,EAAC,KAAsB,OAAnB,UAAU,QAAQ;4BACjD,WAAU;;;;;;sCAEZ,yMAAC;;8CACC,yMAAC;oCAAG,WAAU;;wCACX,UAAU,SAAS;wCAAC;wCAAE,UAAU,QAAQ;;;;;;;8CAE3C,yMAAC;oCAAE,WAAU;;wCAAwB;wCAAE,UAAU,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;0BAM/D,yMAAC;gBAAI,WAAU;;oBACZ,cAAc,MAAM,KAAK,kBACxB,yMAAC;wBAAI,WAAU;;0CACb,yMAAC;0CAAE;;;;;;0CACH,yMAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;+BAG9B,cAAc,GAAG,CAAC;4BAAC,CAAC,SAAS,YAAY;6CACvC,yMAAC;;8CAEC,yMAAC;oCAAI,WAAU;8CACb,cAAA,yMAAC;wCAAI,WAAU;kDACZ,WAAW,IAAI,KAAK,SAAS,OAAO;;;;;;;;;;;gCAKxC,YAAY,GAAG,CAAC,CAAC,SAAS;wCAIvB;oCAHF,MAAM,gBAAgB,QAAQ,QAAQ,KAAK;oCAC3C,MAAM,aAAa,CAAC,iBAAiB,CACnC,UAAU,KACV,EAAA,gBAAA,WAAW,CAAC,QAAQ,EAAE,cAAtB,oCAAA,cAAwB,QAAQ,MAAK,QAAQ,QAAQ,AACvD;oCAEA,qBACE,yMAAC;wCAEC,WAAW,AAAC,QAAuD,OAAhD,gBAAgB,gBAAgB,iBAAgB;kDAEnE,cAAA,yMAAC;4CAAI,WAAW,AAAC,iDAAwG,OAAxD,gBAAgB,qCAAqC;;gDACnH,cAAc,CAAC,+BACd,yMAAC;oDACC,KAAK,UAAU,QAAQ,IAAI,AAAC,oCAA0D,OAAvB,UAAU,SAAS,EAAC,KAAsB,OAAnB,UAAU,QAAQ,EAAC;oDACzG,KAAK,AAAC,GAAyB,OAAvB,UAAU,SAAS,EAAC,KAAsB,OAAnB,UAAU,QAAQ;oDACjD,WAAU;;;;;;gDAGb,CAAC,cAAc,CAAC,+BACf,yMAAC;oDAAI,WAAU;;;;;;8DAGjB,yMAAC;oDACC,WAAW,AAAC,wBAIX,OAHC,gBACI,2BACA;;sEAGN,yMAAC;4DAAE,WAAU;sEAAW,QAAQ,OAAO;;;;;;sEACvC,yMAAC;4DAAE,WAAW,AAAC,gBAEd,OADC,gBAAgB,kBAAkB;sEAEjC,WAAW,QAAQ,SAAS;;;;;;;;;;;;;;;;;;uCA1B9B,QAAQ,EAAE;;;;;gCAgCrB;;2BAlDQ;;;;;;kCAsDd,yMAAC;wBAAI,KAAK;;;;;;;;;;;;0BAIZ,yMAAC;gBAAI,WAAU;0BACb,cAAA,yMAAC;oBAAI,WAAU;;sCACb,yMAAC;4BACC,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4BAC7C,aAAa,AAAC,WAA8B,OAApB,UAAU,SAAS,EAAC;4BAC5C,WAAU;4BACV,YAAY,CAAC;gCACX,IAAI,EAAE,GAAG,KAAK,SAAS;oCACrB;gCACF;4BACF;;;;;;sCAEF,yMAAC;4BACC,SAAS;4BACT,UAAU,CAAC,WAAW,IAAI;4BAC1B,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX;GAzMwB;KAAA", "debugId": null}}, {"offset": {"line": 821, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/components/UserDisplay.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { User } from '@/lib/types'\n\ninterface UserDisplayProps {\n  userId: string\n  children: (user: User | null, isLoading: boolean) => React.ReactNode\n}\n\nexport default function UserDisplay({ userId, children }: UserDisplayProps) {\n  const [user, setUser] = useState<User | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n\n  useEffect(() => {\n    const loadUser = async () => {\n      try {\n        const response = await fetch(`/api/users/${userId}`)\n        if (response.ok) {\n          const data = await response.json()\n          setUser(data.user)\n        }\n      } catch (error) {\n        console.error('Error loading user:', error)\n      } finally {\n        setIsLoading(false)\n      }\n    }\n\n    loadUser()\n  }, [userId])\n\n  return <>{children(user, isLoading)}</>\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAUe,SAAS,YAAY,KAAsC;QAAtC,EAAE,MAAM,EAAE,QAAQ,EAAoB,GAAtC;;IAClC,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,qLAAQ,EAAc;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,qLAAQ,EAAC;IAE3C,IAAA,sLAAS;iCAAC;YACR,MAAM;kDAAW;oBACf,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM,AAAC,cAAoB,OAAP;wBAC3C,IAAI,SAAS,EAAE,EAAE;4BACf,MAAM,OAAO,MAAM,SAAS,IAAI;4BAChC,QAAQ,KAAK,IAAI;wBACnB;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,uBAAuB;oBACvC,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;gCAAG;QAAC;KAAO;IAEX,qBAAO;kBAAG,SAAS,MAAM;;AAC3B;GAvBwB;KAAA", "debugId": null}}, {"offset": {"line": 873, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/components/MessageList.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { User, Message } from '@/lib/types'\nimport { storageUtils } from '@/lib/storage'\nimport Chat<PERSON><PERSON>ow from './ChatWindow'\nimport UserDisplay from './UserDisplay'\n\ninterface MessageListProps {\n  currentUserId: string\n  users: User[]\n  messages: Message[]\n  onSendMessage: (receiverId: string, content: string) => void\n}\n\nexport default function MessageList({ currentUserId, users, messages, onSendMessage }: MessageListProps) {\n  const [selectedUserId, setSelectedUserId] = useState<string | null>(null)\n  const [conversations, setConversations] = useState<{ userId: string; lastMessage?: Message; unreadCount: number }[]>([])\n\n  useEffect(() => {\n    // Get all users that have conversations with current user\n    const userConversations = new Map<string, { lastMessage?: Message; unreadCount: number }>()\n    \n    messages.forEach(message => {\n      const otherUserId = message.senderId === currentUserId ? message.receiverId : message.senderId\n      \n      if (otherUserId !== currentUserId) {\n        const existing = userConversations.get(otherUserId)\n        const isUnread = message.receiverId === currentUserId && !message.read\n        \n        userConversations.set(otherUserId, {\n          lastMessage: !existing?.lastMessage || message.timestamp > existing.lastMessage.timestamp \n            ? message \n            : existing.lastMessage,\n          unreadCount: (existing?.unreadCount || 0) + (isUnread ? 1 : 0)\n        })\n      }\n    })\n\n    const conversationList = Array.from(userConversations.entries())\n      .map(([userId, data]) => ({ userId, ...data }))\n      .sort((a, b) => (b.lastMessage?.timestamp || 0) - (a.lastMessage?.timestamp || 0))\n\n    setConversations(conversationList)\n  }, [messages, currentUserId])\n\n  const getUserById = (userId: string) => {\n    return users.find(user => user.id === userId)\n  }\n\n  const formatTime = (timestamp: number) => {\n    const date = new Date(timestamp)\n    const now = new Date()\n    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)\n    \n    if (diffInHours < 24) {\n      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })\n    } else if (diffInHours < 168) { // 7 days\n      return date.toLocaleDateString([], { weekday: 'short' })\n    } else {\n      return date.toLocaleDateString([], { month: 'short', day: 'numeric' })\n    }\n  }\n\n  const truncateMessage = (content: string, maxLength: number = 50) => {\n    return content.length > maxLength ? content.substring(0, maxLength) + '...' : content\n  }\n\n  return (\n    <div className=\"flex h-[600px]\">\n      {/* Conversations List */}\n      <div className=\"w-1/3 border-r border-gray-200\">\n        <div className=\"p-4 border-b border-gray-200\">\n          <h3 className=\"text-lg font-medium text-gray-900\">Messages</h3>\n        </div>\n        \n        <div className=\"overflow-y-auto h-full\">\n          {conversations.length === 0 ? (\n            <div className=\"p-4 text-center text-gray-500\">\n              <p>No conversations yet</p>\n              <p className=\"text-sm mt-1\">Search for users to start messaging</p>\n            </div>\n          ) : (\n            conversations.map(({ userId, lastMessage, unreadCount }) => {\n              const user = getUserById(userId)\n\n              if (user) {\n                return (\n                  <div\n                    key={userId}\n                    onClick={() => setSelectedUserId(userId)}\n                    className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${\n                      selectedUserId === userId ? 'bg-blue-50 border-blue-200' : ''\n                    }`}\n                  >\n                    <div className=\"flex items-center space-x-3\">\n                      <img\n                        src={user.imageUrl || `https://ui-avatars.com/api/?name=${user.firstName}+${user.lastName}&background=random`}\n                        alt={`${user.firstName} ${user.lastName}`}\n                        className=\"w-10 h-10 rounded-full object-cover\"\n                      />\n\n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"flex items-center justify-between\">\n                          <p className=\"text-sm font-medium text-gray-900 truncate\">\n                            {user.firstName} {user.lastName}\n                          </p>\n                          {lastMessage && (\n                            <p className=\"text-xs text-gray-500\">\n                              {formatTime(lastMessage.timestamp)}\n                            </p>\n                          )}\n                        </div>\n\n                        <div className=\"flex items-center justify-between\">\n                          {lastMessage && (\n                            <p className=\"text-sm text-gray-500 truncate\">\n                              {lastMessage.senderId === currentUserId ? 'You: ' : ''}\n                              {truncateMessage(lastMessage.content)}\n                            </p>\n                          )}\n                          {unreadCount > 0 && (\n                            <span className=\"bg-blue-600 text-white text-xs rounded-full px-2 py-1 ml-2\">\n                              {unreadCount}\n                            </span>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                )\n              }\n\n              // For users not in cache, use UserDisplay\n              return (\n                <UserDisplay key={userId} userId={userId}>\n                  {(fetchedUser, isLoading) => {\n                    if (isLoading) {\n                      return (\n                        <div className=\"p-4 border-b border-gray-100\">\n                          <div className=\"flex items-center space-x-3\">\n                            <div className=\"w-10 h-10 bg-gray-200 rounded-full animate-pulse\"></div>\n                            <div className=\"flex-1\">\n                              <div className=\"h-4 bg-gray-200 rounded animate-pulse mb-2\"></div>\n                              <div className=\"h-3 bg-gray-200 rounded animate-pulse w-1/2\"></div>\n                            </div>\n                          </div>\n                        </div>\n                      )\n                    }\n\n                    if (!fetchedUser) return null\n\n                    return (\n                      <div\n                        onClick={() => setSelectedUserId(userId)}\n                        className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${\n                          selectedUserId === userId ? 'bg-blue-50 border-blue-200' : ''\n                        }`}\n                      >\n                        <div className=\"flex items-center space-x-3\">\n                          <img\n                            src={fetchedUser.imageUrl || `https://ui-avatars.com/api/?name=${fetchedUser.firstName}+${fetchedUser.lastName}&background=random`}\n                            alt={`${fetchedUser.firstName} ${fetchedUser.lastName}`}\n                            className=\"w-10 h-10 rounded-full object-cover\"\n                          />\n\n                          <div className=\"flex-1 min-w-0\">\n                            <div className=\"flex items-center justify-between\">\n                              <p className=\"text-sm font-medium text-gray-900 truncate\">\n                                {fetchedUser.firstName} {fetchedUser.lastName}\n                              </p>\n                              {lastMessage && (\n                                <p className=\"text-xs text-gray-500\">\n                                  {formatTime(lastMessage.timestamp)}\n                                </p>\n                              )}\n                            </div>\n\n                            <div className=\"flex items-center justify-between\">\n                              {lastMessage && (\n                                <p className=\"text-sm text-gray-500 truncate\">\n                                  {lastMessage.senderId === currentUserId ? 'You: ' : ''}\n                                  {truncateMessage(lastMessage.content)}\n                                </p>\n                              )}\n                              {unreadCount > 0 && (\n                                <span className=\"bg-blue-600 text-white text-xs rounded-full px-2 py-1 ml-2\">\n                                  {unreadCount}\n                                </span>\n                              )}\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    )\n                  }}\n                </UserDisplay>\n              )\n            })\n          )}\n        </div>\n      </div>\n\n      {/* Chat Window */}\n      <div className=\"flex-1\">\n        {selectedUserId ? (\n          <UserDisplay userId={selectedUserId}>\n            {(otherUser, isLoading) => (\n              <ChatWindow\n                currentUserId={currentUserId}\n                otherUserId={selectedUserId}\n                otherUser={otherUser}\n                messages={storageUtils.getConversationMessages(currentUserId, selectedUserId)}\n                onSendMessage={onSendMessage}\n                isLoadingUser={isLoading}\n              />\n            )}\n          </UserDisplay>\n        ) : (\n          <div className=\"flex items-center justify-center h-full text-gray-500\">\n            <div className=\"text-center\">\n              <svg className=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-2.4-.32l-4.6 1.92 1.92-4.6A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z\" />\n              </svg>\n              <p className=\"mt-2\">Select a conversation to start messaging</p>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AACA;AACA;;;AANA;;;;;AAee,SAAS,YAAY,KAAmE;QAAnE,EAAE,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAoB,GAAnE;;IAClC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,qLAAQ,EAAgB;IACpE,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,qLAAQ,EAAmE,EAAE;IAEvH,IAAA,sLAAS;iCAAC;YACR,0DAA0D;YAC1D,MAAM,oBAAoB,IAAI;YAE9B,SAAS,OAAO;yCAAC,CAAA;oBACf,MAAM,cAAc,QAAQ,QAAQ,KAAK,gBAAgB,QAAQ,UAAU,GAAG,QAAQ,QAAQ;oBAE9F,IAAI,gBAAgB,eAAe;wBACjC,MAAM,WAAW,kBAAkB,GAAG,CAAC;wBACvC,MAAM,WAAW,QAAQ,UAAU,KAAK,iBAAiB,CAAC,QAAQ,IAAI;wBAEtE,kBAAkB,GAAG,CAAC,aAAa;4BACjC,aAAa,EAAC,qBAAA,+BAAA,SAAU,WAAW,KAAI,QAAQ,SAAS,GAAG,SAAS,WAAW,CAAC,SAAS,GACrF,UACA,SAAS,WAAW;4BACxB,aAAa,CAAC,CAAA,qBAAA,+BAAA,SAAU,WAAW,KAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC;wBAC/D;oBACF;gBACF;;YAEA,MAAM,mBAAmB,MAAM,IAAI,CAAC,kBAAkB,OAAO,IAC1D,GAAG;0DAAC;wBAAC,CAAC,QAAQ,KAAK;2BAAM;wBAAE;wBAAQ,GAAG,IAAI;oBAAC;;yDAC3C,IAAI;0DAAC,CAAC,GAAG;wBAAO,gBAAkC;2BAAnC,CAAC,EAAA,iBAAA,EAAE,WAAW,cAAb,qCAAA,eAAe,SAAS,KAAI,CAAC,IAAI,CAAC,EAAA,iBAAA,EAAE,WAAW,cAAb,qCAAA,eAAe,SAAS,KAAI,CAAC;;;YAElF,iBAAiB;QACnB;gCAAG;QAAC;QAAU;KAAc;IAE5B,MAAM,cAAc,CAAC;QACnB,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IACxC;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,cAAc,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE;QAEtE,IAAI,cAAc,IAAI;YACpB,OAAO,KAAK,kBAAkB,CAAC,EAAE,EAAE;gBAAE,MAAM;gBAAW,QAAQ;YAAU;QAC1E,OAAO,IAAI,cAAc,KAAK;YAC5B,OAAO,KAAK,kBAAkB,CAAC,EAAE,EAAE;gBAAE,SAAS;YAAQ;QACxD,OAAO;YACL,OAAO,KAAK,kBAAkB,CAAC,EAAE,EAAE;gBAAE,OAAO;gBAAS,KAAK;YAAU;QACtE;IACF;IAEA,MAAM,kBAAkB,SAAC;YAAiB,6EAAoB;QAC5D,OAAO,QAAQ,MAAM,GAAG,YAAY,QAAQ,SAAS,CAAC,GAAG,aAAa,QAAQ;IAChF;IAEA,qBACE,yMAAC;QAAI,WAAU;;0BAEb,yMAAC;gBAAI,WAAU;;kCACb,yMAAC;wBAAI,WAAU;kCACb,cAAA,yMAAC;4BAAG,WAAU;sCAAoC;;;;;;;;;;;kCAGpD,yMAAC;wBAAI,WAAU;kCACZ,cAAc,MAAM,KAAK,kBACxB,yMAAC;4BAAI,WAAU;;8CACb,yMAAC;8CAAE;;;;;;8CACH,yMAAC;oCAAE,WAAU;8CAAe;;;;;;;;;;;mCAG9B,cAAc,GAAG,CAAC;gCAAC,EAAE,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE;4BACrD,MAAM,OAAO,YAAY;4BAEzB,IAAI,MAAM;gCACR,qBACE,yMAAC;oCAEC,SAAS,IAAM,kBAAkB;oCACjC,WAAW,AAAC,gEAEX,OADC,mBAAmB,SAAS,+BAA+B;8CAG7D,cAAA,yMAAC;wCAAI,WAAU;;0DACb,yMAAC;gDACC,KAAK,KAAK,QAAQ,IAAI,AAAC,oCAAqD,OAAlB,KAAK,SAAS,EAAC,KAAiB,OAAd,KAAK,QAAQ,EAAC;gDAC1F,KAAK,AAAC,GAAoB,OAAlB,KAAK,SAAS,EAAC,KAAiB,OAAd,KAAK,QAAQ;gDACvC,WAAU;;;;;;0DAGZ,yMAAC;gDAAI,WAAU;;kEACb,yMAAC;wDAAI,WAAU;;0EACb,yMAAC;gEAAE,WAAU;;oEACV,KAAK,SAAS;oEAAC;oEAAE,KAAK,QAAQ;;;;;;;4DAEhC,6BACC,yMAAC;gEAAE,WAAU;0EACV,WAAW,YAAY,SAAS;;;;;;;;;;;;kEAKvC,yMAAC;wDAAI,WAAU;;4DACZ,6BACC,yMAAC;gEAAE,WAAU;;oEACV,YAAY,QAAQ,KAAK,gBAAgB,UAAU;oEACnD,gBAAgB,YAAY,OAAO;;;;;;;4DAGvC,cAAc,mBACb,yMAAC;gEAAK,WAAU;0EACb;;;;;;;;;;;;;;;;;;;;;;;;mCAlCN;;;;;4BA0CX;4BAEA,0CAA0C;4BAC1C,qBACE,yMAAC,oJAAW;gCAAc,QAAQ;0CAC/B,CAAC,aAAa;oCACb,IAAI,WAAW;wCACb,qBACE,yMAAC;4CAAI,WAAU;sDACb,cAAA,yMAAC;gDAAI,WAAU;;kEACb,yMAAC;wDAAI,WAAU;;;;;;kEACf,yMAAC;wDAAI,WAAU;;0EACb,yMAAC;gEAAI,WAAU;;;;;;0EACf,yMAAC;gEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;oCAKzB;oCAEA,IAAI,CAAC,aAAa,OAAO;oCAEzB,qBACE,yMAAC;wCACC,SAAS,IAAM,kBAAkB;wCACjC,WAAW,AAAC,gEAEX,OADC,mBAAmB,SAAS,+BAA+B;kDAG7D,cAAA,yMAAC;4CAAI,WAAU;;8DACb,yMAAC;oDACC,KAAK,YAAY,QAAQ,IAAI,AAAC,oCAA4D,OAAzB,YAAY,SAAS,EAAC,KAAwB,OAArB,YAAY,QAAQ,EAAC;oDAC/G,KAAK,AAAC,GAA2B,OAAzB,YAAY,SAAS,EAAC,KAAwB,OAArB,YAAY,QAAQ;oDACrD,WAAU;;;;;;8DAGZ,yMAAC;oDAAI,WAAU;;sEACb,yMAAC;4DAAI,WAAU;;8EACb,yMAAC;oEAAE,WAAU;;wEACV,YAAY,SAAS;wEAAC;wEAAE,YAAY,QAAQ;;;;;;;gEAE9C,6BACC,yMAAC;oEAAE,WAAU;8EACV,WAAW,YAAY,SAAS;;;;;;;;;;;;sEAKvC,yMAAC;4DAAI,WAAU;;gEACZ,6BACC,yMAAC;oEAAE,WAAU;;wEACV,YAAY,QAAQ,KAAK,gBAAgB,UAAU;wEACnD,gBAAgB,YAAY,OAAO;;;;;;;gEAGvC,cAAc,mBACb,yMAAC;oEAAK,WAAU;8EACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAQjB;+BA7DgB;;;;;wBAgEtB;;;;;;;;;;;;0BAMN,yMAAC;gBAAI,WAAU;0BACZ,+BACC,yMAAC,oJAAW;oBAAC,QAAQ;8BAClB,CAAC,WAAW,0BACX,yMAAC,mJAAU;4BACT,eAAe;4BACf,aAAa;4BACb,WAAW;4BACX,UAAU,6IAAY,CAAC,uBAAuB,CAAC,eAAe;4BAC9D,eAAe;4BACf,eAAe;;;;;;;;;;yCAKrB,yMAAC;oBAAI,WAAU;8BACb,cAAA,yMAAC;wBAAI,WAAU;;0CACb,yMAAC;gCAAI,WAAU;gCAAkC,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CACtF,cAAA,yMAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;0CAEvE,yMAAC;gCAAE,WAAU;0CAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlC;GAzNwB;KAAA", "debugId": null}}, {"offset": {"line": 1351, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/components/FriendRequestCard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { User, FriendRequest } from '@/lib/types'\n\ninterface FriendRequestCardProps {\n  request: FriendRequest\n  users: User[]\n  onResponse: (requestId: string, status: 'accepted' | 'rejected') => void\n}\n\nexport default function FriendRequestCard({ request, users, onResponse }: FriendRequestCardProps) {\n  const [sender, setSender] = useState<User | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n\n  useEffect(() => {\n    const loadSender = async () => {\n      try {\n        const response = await fetch(`/api/users/${request.senderId}`)\n        if (response.ok) {\n          const data = await response.json()\n          setSender(data.user)\n        }\n      } catch (error) {\n        console.error('Error loading sender:', error)\n      } finally {\n        setIsLoading(false)\n      }\n    }\n\n    loadSender()\n  }, [request.senderId])\n\n  if (isLoading) {\n    return (\n      <div className=\"border border-gray-200 rounded-lg p-4 bg-white\">\n        <div className=\"flex items-center space-x-4\">\n          <div className=\"w-12 h-12 bg-gray-200 rounded-full animate-pulse\"></div>\n          <div className=\"flex-1\">\n            <div className=\"h-4 bg-gray-200 rounded animate-pulse mb-2\"></div>\n            <div className=\"h-3 bg-gray-200 rounded animate-pulse w-1/2\"></div>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  if (!sender) {\n    return null\n  }\n\n  const formatTime = (timestamp: number) => {\n    const date = new Date(timestamp)\n    const now = new Date()\n    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)\n    \n    if (diffInHours < 1) {\n      return 'Just now'\n    } else if (diffInHours < 24) {\n      return `${Math.floor(diffInHours)} hours ago`\n    } else {\n      const diffInDays = Math.floor(diffInHours / 24)\n      return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`\n    }\n  }\n\n  return (\n    <div className=\"border border-gray-200 rounded-lg p-4 bg-white\">\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          <img\n            src={sender.imageUrl || `https://ui-avatars.com/api/?name=${sender.firstName}+${sender.lastName}&background=random`}\n            alt={`${sender.firstName} ${sender.lastName}`}\n            className=\"w-12 h-12 rounded-full object-cover\"\n          />\n          \n          <div>\n            <h3 className=\"text-lg font-medium text-gray-900\">\n              {sender.firstName} {sender.lastName}\n            </h3>\n            <p className=\"text-sm text-gray-500\">@{sender.username}</p>\n            <p className=\"text-xs text-gray-400\">{formatTime(request.timestamp)}</p>\n          </div>\n        </div>\n        \n        <div className=\"flex space-x-2\">\n          <button\n            onClick={() => onResponse(request.id, 'accepted')}\n            className=\"px-4 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700\"\n          >\n            Accept\n          </button>\n          <button\n            onClick={() => onResponse(request.id, 'rejected')}\n            className=\"px-4 py-2 bg-red-600 text-white text-sm rounded-md hover:bg-red-700\"\n          >\n            Decline\n          </button>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAWe,SAAS,kBAAkB,KAAsD;QAAtD,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAA0B,GAAtD;;IACxC,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,qLAAQ,EAAc;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,qLAAQ,EAAC;IAE3C,IAAA,sLAAS;uCAAC;YACR,MAAM;0DAAa;oBACjB,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM,AAAC,cAA8B,OAAjB,QAAQ,QAAQ;wBAC3D,IAAI,SAAS,EAAE,EAAE;4BACf,MAAM,OAAO,MAAM,SAAS,IAAI;4BAChC,UAAU,KAAK,IAAI;wBACrB;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,yBAAyB;oBACzC,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;sCAAG;QAAC,QAAQ,QAAQ;KAAC;IAErB,IAAI,WAAW;QACb,qBACE,yMAAC;YAAI,WAAU;sBACb,cAAA,yMAAC;gBAAI,WAAU;;kCACb,yMAAC;wBAAI,WAAU;;;;;;kCACf,yMAAC;wBAAI,WAAU;;0CACb,yMAAC;gCAAI,WAAU;;;;;;0CACf,yMAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAKzB;IAEA,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,cAAc,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE;QAEtE,IAAI,cAAc,GAAG;YACnB,OAAO;QACT,OAAO,IAAI,cAAc,IAAI;YAC3B,OAAO,AAAC,GAA0B,OAAxB,KAAK,KAAK,CAAC,cAAa;QACpC,OAAO;YACL,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;YAC5C,OAAO,AAAC,GAAmB,OAAjB,YAAW,QAAgC,OAA1B,aAAa,IAAI,MAAM,IAAG;QACvD;IACF;IAEA,qBACE,yMAAC;QAAI,WAAU;kBACb,cAAA,yMAAC;YAAI,WAAU;;8BACb,yMAAC;oBAAI,WAAU;;sCACb,yMAAC;4BACC,KAAK,OAAO,QAAQ,IAAI,AAAC,oCAAuD,OAApB,OAAO,SAAS,EAAC,KAAmB,OAAhB,OAAO,QAAQ,EAAC;4BAChG,KAAK,AAAC,GAAsB,OAApB,OAAO,SAAS,EAAC,KAAmB,OAAhB,OAAO,QAAQ;4BAC3C,WAAU;;;;;;sCAGZ,yMAAC;;8CACC,yMAAC;oCAAG,WAAU;;wCACX,OAAO,SAAS;wCAAC;wCAAE,OAAO,QAAQ;;;;;;;8CAErC,yMAAC;oCAAE,WAAU;;wCAAwB;wCAAE,OAAO,QAAQ;;;;;;;8CACtD,yMAAC;oCAAE,WAAU;8CAAyB,WAAW,QAAQ,SAAS;;;;;;;;;;;;;;;;;;8BAItE,yMAAC;oBAAI,WAAU;;sCACb,yMAAC;4BACC,SAAS,IAAM,WAAW,QAAQ,EAAE,EAAE;4BACtC,WAAU;sCACX;;;;;;sCAGD,yMAAC;4BACC,SAAS,IAAM,WAAW,QAAQ,EAAE,EAAE;4BACtC,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX;GA3FwB;KAAA", "debugId": null}}, {"offset": {"line": 1564, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/components/Navigation.tsx"], "sourcesContent": ["'use client'\n\nimport { UserButton } from '@clerk/nextjs'\n\ninterface NavigationProps {\n  activeTab: 'messages' | 'search' | 'friends'\n  setActiveTab: (tab: 'messages' | 'search' | 'friends') => void\n  pendingRequestsCount: number\n}\n\nexport default function Navigation({ activeTab, setActiveTab, pendingRequestsCount }: NavigationProps) {\n  return (\n    <nav className=\"bg-white shadow-sm border-b\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex items-center space-x-8\">\n            <h1 className=\"text-xl font-bold text-gray-900\">Messages App</h1>\n            \n            <div className=\"flex space-x-4\">\n              <button\n                onClick={() => setActiveTab('messages')}\n                className={`px-3 py-2 rounded-md text-sm font-medium ${\n                  activeTab === 'messages'\n                    ? 'bg-blue-100 text-blue-700'\n                    : 'text-gray-500 hover:text-gray-700'\n                }`}\n              >\n                Messages\n              </button>\n              \n              <button\n                onClick={() => setActiveTab('search')}\n                className={`px-3 py-2 rounded-md text-sm font-medium ${\n                  activeTab === 'search'\n                    ? 'bg-blue-100 text-blue-700'\n                    : 'text-gray-500 hover:text-gray-700'\n                }`}\n              >\n                Search Users\n              </button>\n              \n              <button\n                onClick={() => setActiveTab('friends')}\n                className={`px-3 py-2 rounded-md text-sm font-medium relative ${\n                  activeTab === 'friends'\n                    ? 'bg-blue-100 text-blue-700'\n                    : 'text-gray-500 hover:text-gray-700'\n                }`}\n              >\n                Friend Requests\n                {pendingRequestsCount > 0 && (\n                  <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\n                    {pendingRequestsCount}\n                  </span>\n                )}\n              </button>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center\">\n            <UserButton afterSignOutUrl=\"/\" />\n          </div>\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAUe,SAAS,WAAW,KAAkE;QAAlE,EAAE,SAAS,EAAE,YAAY,EAAE,oBAAoB,EAAmB,GAAlE;IACjC,qBACE,yMAAC;QAAI,WAAU;kBACb,cAAA,yMAAC;YAAI,WAAU;sBACb,cAAA,yMAAC;gBAAI,WAAU;;kCACb,yMAAC;wBAAI,WAAU;;0CACb,yMAAC;gCAAG,WAAU;0CAAkC;;;;;;0CAEhD,yMAAC;gCAAI,WAAU;;kDACb,yMAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,4CAIX,OAHC,cAAc,aACV,8BACA;kDAEP;;;;;;kDAID,yMAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,4CAIX,OAHC,cAAc,WACV,8BACA;kDAEP;;;;;;kDAID,yMAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,qDAIX,OAHC,cAAc,YACV,8BACA;;4CAEP;4CAEE,uBAAuB,mBACtB,yMAAC;gDAAK,WAAU;0DACb;;;;;;;;;;;;;;;;;;;;;;;;kCAOX,yMAAC;wBAAI,WAAU;kCACb,cAAA,yMAAC,oMAAU;4BAAC,iBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxC;KAxDwB", "debugId": null}}, {"offset": {"line": 1686, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/app/dashboard/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useUser } from '@clerk/nextjs'\nimport { useState, useEffect } from 'react'\nimport { storageUtils } from '@/lib/storage'\nimport { User, Message, FriendRequest } from '@/lib/types'\nimport UserSearch from '@/components/UserSearch'\nimport MessageList from '@/components/MessageList'\nimport FriendRequestCard from '@/components/FriendRequestCard'\nimport Navigation from '@/components/Navigation'\n\nexport default function Dashboard() {\n  const { user } = useUser()\n  const [activeTab, setActiveTab] = useState<'messages' | 'search' | 'friends'>('messages')\n  const [users, setUsers] = useState<User[]>([])\n  const [messages, setMessages] = useState<Message[]>([])\n  const [friendRequests, setFriendRequests] = useState<FriendRequest[]>([])\n\n  useEffect(() => {\n    if (user) {\n      // Register current user with the API\n      registerCurrentUser()\n\n      // Load data\n      setMessages(storageUtils.getMessages())\n      setFriendRequests(storageUtils.getFriendRequests())\n\n      // Load users from API for message list\n      loadUsersForMessages()\n    }\n  }, [user])\n\n  const registerCurrentUser = async () => {\n    try {\n      await fetch('/api/users', {\n        method: 'POST',\n      })\n    } catch (error) {\n      console.error('Error registering user:', error)\n    }\n  }\n\n  const loadUsersForMessages = async () => {\n    try {\n      const response = await fetch('/api/users')\n      if (response.ok) {\n        const data = await response.json()\n        setUsers(data.users)\n      }\n    } catch (error) {\n      console.error('Error loading users:', error)\n    }\n  }\n\n  const handleSendMessage = (receiverId: string, content: string) => {\n    if (!user) return\n    \n    const newMessage: Message = {\n      id: Date.now().toString(),\n      senderId: user.id,\n      receiverId,\n      content,\n      timestamp: Date.now(),\n      read: false\n    }\n    \n    storageUtils.addMessage(newMessage)\n    setMessages(storageUtils.getMessages())\n  }\n\n  const handleSendFriendRequest = (receiverId: string) => {\n    if (!user) return\n    \n    const existingRequest = friendRequests.find(\n      req => req.senderId === user.id && req.receiverId === receiverId && req.status === 'pending'\n    )\n    \n    if (existingRequest) return\n    \n    const newRequest: FriendRequest = {\n      id: Date.now().toString(),\n      senderId: user.id,\n      receiverId,\n      status: 'pending',\n      timestamp: Date.now()\n    }\n    \n    storageUtils.addFriendRequest(newRequest)\n    setFriendRequests(storageUtils.getFriendRequests())\n  }\n\n  const handleFriendRequestResponse = (requestId: string, status: 'accepted' | 'rejected') => {\n    storageUtils.updateFriendRequest(requestId, status)\n    setFriendRequests(storageUtils.getFriendRequests())\n  }\n\n  if (!user) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Loading...</h2>\n        </div>\n      </div>\n    )\n  }\n\n  const receivedFriendRequests = friendRequests.filter(\n    req => req.receiverId === user.id && req.status === 'pending'\n  )\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navigation \n        activeTab={activeTab} \n        setActiveTab={setActiveTab}\n        pendingRequestsCount={receivedFriendRequests.length}\n      />\n      \n      <main className=\"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8\">\n        <div className=\"bg-white rounded-lg shadow\">\n          {activeTab === 'messages' && (\n            <MessageList \n              currentUserId={user.id}\n              users={users}\n              messages={messages}\n              onSendMessage={handleSendMessage}\n            />\n          )}\n          \n          {activeTab === 'search' && (\n            <UserSearch \n              currentUserId={user.id}\n              users={users}\n              friendRequests={friendRequests}\n              onSendFriendRequest={handleSendFriendRequest}\n              onSendMessage={handleSendMessage}\n            />\n          )}\n          \n          {activeTab === 'friends' && (\n            <div className=\"p-6\">\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Friend Requests</h2>\n              {receivedFriendRequests.length === 0 ? (\n                <p className=\"text-gray-500 text-center py-8\">No pending friend requests</p>\n              ) : (\n                <div className=\"space-y-4\">\n                  {receivedFriendRequests.map(request => (\n                    <FriendRequestCard\n                      key={request.id}\n                      request={request}\n                      users={users}\n                      onResponse={handleFriendRequestResponse}\n                    />\n                  ))}\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;;;AATA;;;;;;;;AAWe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,IAAA,sLAAO;IACxB,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,qLAAQ,EAAoC;IAC9E,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,qLAAQ,EAAS,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,qLAAQ,EAAY,EAAE;IACtD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,qLAAQ,EAAkB,EAAE;IAExE,IAAA,sLAAS;+BAAC;YACR,IAAI,MAAM;gBACR,qCAAqC;gBACrC;gBAEA,YAAY;gBACZ,YAAY,6IAAY,CAAC,WAAW;gBACpC,kBAAkB,6IAAY,CAAC,iBAAiB;gBAEhD,uCAAuC;gBACvC;YACF;QACF;8BAAG;QAAC;KAAK;IAET,MAAM,sBAAsB;QAC1B,IAAI;YACF,MAAM,MAAM,cAAc;gBACxB,QAAQ;YACV;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS,KAAK,KAAK;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,oBAAoB,CAAC,YAAoB;QAC7C,IAAI,CAAC,MAAM;QAEX,MAAM,aAAsB;YAC1B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,UAAU,KAAK,EAAE;YACjB;YACA;YACA,WAAW,KAAK,GAAG;YACnB,MAAM;QACR;QAEA,6IAAY,CAAC,UAAU,CAAC;QACxB,YAAY,6IAAY,CAAC,WAAW;IACtC;IAEA,MAAM,0BAA0B,CAAC;QAC/B,IAAI,CAAC,MAAM;QAEX,MAAM,kBAAkB,eAAe,IAAI,CACzC,CAAA,MAAO,IAAI,QAAQ,KAAK,KAAK,EAAE,IAAI,IAAI,UAAU,KAAK,cAAc,IAAI,MAAM,KAAK;QAGrF,IAAI,iBAAiB;QAErB,MAAM,aAA4B;YAChC,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,UAAU,KAAK,EAAE;YACjB;YACA,QAAQ;YACR,WAAW,KAAK,GAAG;QACrB;QAEA,6IAAY,CAAC,gBAAgB,CAAC;QAC9B,kBAAkB,6IAAY,CAAC,iBAAiB;IAClD;IAEA,MAAM,8BAA8B,CAAC,WAAmB;QACtD,6IAAY,CAAC,mBAAmB,CAAC,WAAW;QAC5C,kBAAkB,6IAAY,CAAC,iBAAiB;IAClD;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,yMAAC;YAAI,WAAU;sBACb,cAAA,yMAAC;gBAAI,WAAU;0BACb,cAAA,yMAAC;oBAAG,WAAU;8BAAwC;;;;;;;;;;;;;;;;IAI9D;IAEA,MAAM,yBAAyB,eAAe,MAAM,CAClD,CAAA,MAAO,IAAI,UAAU,KAAK,KAAK,EAAE,IAAI,IAAI,MAAM,KAAK;IAGtD,qBACE,yMAAC;QAAI,WAAU;;0BACb,yMAAC,mJAAU;gBACT,WAAW;gBACX,cAAc;gBACd,sBAAsB,uBAAuB,MAAM;;;;;;0BAGrD,yMAAC;gBAAK,WAAU;0BACd,cAAA,yMAAC;oBAAI,WAAU;;wBACZ,cAAc,4BACb,yMAAC,oJAAW;4BACV,eAAe,KAAK,EAAE;4BACtB,OAAO;4BACP,UAAU;4BACV,eAAe;;;;;;wBAIlB,cAAc,0BACb,yMAAC,mJAAU;4BACT,eAAe,KAAK,EAAE;4BACtB,OAAO;4BACP,gBAAgB;4BAChB,qBAAqB;4BACrB,eAAe;;;;;;wBAIlB,cAAc,2BACb,yMAAC;4BAAI,WAAU;;8CACb,yMAAC;oCAAG,WAAU;8CAAwC;;;;;;gCACrD,uBAAuB,MAAM,KAAK,kBACjC,yMAAC;oCAAE,WAAU;8CAAiC;;;;;yDAE9C,yMAAC;oCAAI,WAAU;8CACZ,uBAAuB,GAAG,CAAC,CAAA,wBAC1B,yMAAC,0JAAiB;4CAEhB,SAAS;4CACT,OAAO;4CACP,YAAY;2CAHP,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcrC;GAvJwB;;QACL,sLAAO;;;KADF", "debugId": null}}]}