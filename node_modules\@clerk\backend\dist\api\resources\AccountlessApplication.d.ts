import type { AccountlessApplicationJSON } from './JSON';
export declare class AccountlessApplication {
    readonly publishableKey: string;
    readonly secretKey: string;
    readonly claimUrl: string;
    readonly apiKeysUrl: string;
    constructor(publishableKey: string, secretKey: string, claimUrl: string, apiKeysUrl: string);
    static fromJSON(data: AccountlessApplicationJSON): AccountlessApplication;
}
//# sourceMappingURL=AccountlessApplication.d.ts.map