var R=require("../../../../chunks/[turbopack]_runtime.js")("server/app/api/users/[id]/route.js")
R.c("server/chunks/65b60_01fc35fc._.js")
R.c("server/chunks/65b60_next_0c16b03c._.js")
R.c("server/chunks/65b60_@clerk_backend_dist_b9933677._.js")
R.c("server/chunks/65b60_@clerk_nextjs_dist_esm_e1f77bcd._.js")
R.c("server/chunks/65b60_ab055b3a._.js")
R.c("server/chunks/[root-of-the-server]__38c8004c._.js")
R.m("[project]/messages/.next-internal/server/app/api/users/[id]/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/messages/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/messages/app/api/users/[id]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/messages/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/messages/app/api/users/[id]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
