import React from 'react';
import type { NextClerkProviderProps } from '../../types';
export declare const KeylessCreatorOrReader: (props: NextClerkProviderProps) => string | number | bigint | boolean | React.ReactElement<any, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | Promise<React.AwaitedReactNode> | null | undefined;
//# sourceMappingURL=keyless-creator-reader.d.ts.map