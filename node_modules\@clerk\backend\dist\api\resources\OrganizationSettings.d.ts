import type { DomainsEnrollmentModes } from './Enums';
import type { OrganizationSettingsJSON } from './JSON';
export declare class OrganizationSettings {
    readonly enabled: boolean;
    readonly maxAllowedMemberships: number;
    readonly maxAllowedRoles: number;
    readonly maxAllowedPermissions: number;
    readonly creatorRole: string;
    readonly adminDeleteEnabled: boolean;
    readonly domainsEnabled: boolean;
    readonly domainsEnrollmentModes: Array<DomainsEnrollmentModes>;
    readonly domainsDefaultRole: string;
    constructor(enabled: boolean, maxAllowedMemberships: number, maxAllowedRoles: number, maxAllowedPermissions: number, creatorRole: string, adminDeleteEnabled: boolean, domainsEnabled: boolean, domainsEnrollmentModes: Array<DomainsEnrollmentModes>, domainsDefaultRole: string);
    static fromJSON(data: OrganizationSettingsJSON): OrganizationSettings;
}
//# sourceMappingURL=OrganizationSettings.d.ts.map