import type { InstanceRestrictionsJSON } from './JSON';
export declare class InstanceRestrictions {
    readonly allowlist: boolean;
    readonly blocklist: boolean;
    readonly blockEmailSubaddresses: boolean;
    readonly blockDisposableEmailDomains: boolean;
    readonly ignoreDotsForGmailAddresses: boolean;
    constructor(allowlist: boolean, blocklist: boolean, blockEmailSubaddresses: boolean, blockDisposableEmailDomains: boolean, ignoreDotsForGmailAddresses: boolean);
    static fromJSON(data: InstanceRestrictionsJSON): InstanceRestrictions;
}
//# sourceMappingURL=InstanceRestrictions.d.ts.map