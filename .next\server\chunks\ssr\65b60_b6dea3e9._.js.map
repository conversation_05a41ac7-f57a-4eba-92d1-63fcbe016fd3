{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    function useSyncExternalStore$2(subscribe, getSnapshot) {\n      didWarnOld18Alpha ||\n        void 0 === React.startTransition ||\n        ((didWarnOld18Alpha = !0),\n        console.error(\n          \"You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release.\"\n        ));\n      var value = getSnapshot();\n      if (!didWarnUncachedGetSnapshot) {\n        var cachedValue = getSnapshot();\n        objectIs(value, cachedValue) ||\n          (console.error(\n            \"The result of getSnapshot should be cached to avoid an infinite loop\"\n          ),\n          (didWarnUncachedGetSnapshot = !0));\n      }\n      cachedValue = useState({\n        inst: { value: value, getSnapshot: getSnapshot }\n      });\n      var inst = cachedValue[0].inst,\n        forceUpdate = cachedValue[1];\n      useLayoutEffect(\n        function () {\n          inst.value = value;\n          inst.getSnapshot = getSnapshot;\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n        },\n        [subscribe, value, getSnapshot]\n      );\n      useEffect(\n        function () {\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          return subscribe(function () {\n            checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          });\n        },\n        [subscribe]\n      );\n      useDebugValue(value);\n      return value;\n    }\n    function checkIfSnapshotChanged(inst) {\n      var latestGetSnapshot = inst.getSnapshot;\n      inst = inst.value;\n      try {\n        var nextValue = latestGetSnapshot();\n        return !objectIs(inst, nextValue);\n      } catch (error) {\n        return !0;\n      }\n    }\n    function useSyncExternalStore$1(subscribe, getSnapshot) {\n      return getSnapshot();\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useState = React.useState,\n      useEffect = React.useEffect,\n      useLayoutEffect = React.useLayoutEffect,\n      useDebugValue = React.useDebugValue,\n      didWarnOld18Alpha = !1,\n      didWarnUncachedGetSnapshot = !1,\n      shim =\n        \"undefined\" === typeof window ||\n        \"undefined\" === typeof window.document ||\n        \"undefined\" === typeof window.document.createElement\n          ? useSyncExternalStore$1\n          : useSyncExternalStore$2;\n    exports.useSyncExternalStore =\n      void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : shim;\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGD,oEACE,AAAC;IACC,SAAS,GAAG,CAAC,EAAE,CAAC;QACd,OAAO,AAAC,MAAM,KAAK,CAAC,MAAM,KAAK,IAAI,MAAM,IAAI,CAAC,KAAO,MAAM,KAAK,MAAM;IACxE;IACA,SAAS,uBAAuB,SAAS,EAAE,WAAW;QACpD,qBACE,KAAK,MAAM,MAAM,eAAe,IAChC,CAAC,AAAC,oBAAoB,CAAC,GACvB,QAAQ,KAAK,CACX,iMACD;QACH,IAAI,QAAQ;QACZ,IAAI,CAAC,4BAA4B;YAC/B,IAAI,cAAc;YAClB,SAAS,OAAO,gBACd,CAAC,QAAQ,KAAK,CACZ,yEAED,6BAA6B,CAAC,CAAE;QACrC;QACA,cAAc,SAAS;YACrB,MAAM;gBAAE,OAAO;gBAAO,aAAa;YAAY;QACjD;QACA,IAAI,OAAO,WAAW,CAAC,EAAE,CAAC,IAAI,EAC5B,cAAc,WAAW,CAAC,EAAE;QAC9B,gBACE;YACE,KAAK,KAAK,GAAG;YACb,KAAK,WAAW,GAAG;YACnB,uBAAuB,SAAS,YAAY;gBAAE,MAAM;YAAK;QAC3D,GACA;YAAC;YAAW;YAAO;SAAY;QAEjC,UACE;YACE,uBAAuB,SAAS,YAAY;gBAAE,MAAM;YAAK;YACzD,OAAO,UAAU;gBACf,uBAAuB,SAAS,YAAY;oBAAE,MAAM;gBAAK;YAC3D;QACF,GACA;YAAC;SAAU;QAEb,cAAc;QACd,OAAO;IACT;IACA,SAAS,uBAAuB,IAAI;QAClC,IAAI,oBAAoB,KAAK,WAAW;QACxC,OAAO,KAAK,KAAK;QACjB,IAAI;YACF,IAAI,YAAY;YAChB,OAAO,CAAC,SAAS,MAAM;QACzB,EAAE,OAAO,OAAO;YACd,OAAO,CAAC;QACV;IACF;IACA,SAAS,uBAAuB,SAAS,EAAE,WAAW;QACpD,OAAO;IACT;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,2BAA2B,IACnE,+BAA+B,2BAA2B,CAAC;IAC7D,IAAI,yJACF,WAAW,eAAe,OAAO,OAAO,EAAE,GAAG,OAAO,EAAE,GAAG,IACzD,WAAW,MAAM,QAAQ,EACzB,YAAY,MAAM,SAAS,EAC3B,kBAAkB,MAAM,eAAe,EACvC,gBAAgB,MAAM,aAAa,EACnC,oBAAoB,CAAC,GACrB,6BAA6B,CAAC,GAC9B,OACE,uCAGI,yBACA;IACR,QAAQ,oBAAoB,GAC1B,KAAK,MAAM,MAAM,oBAAoB,GAAG,MAAM,oBAAoB,GAAG;IACvE,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,0BAA0B,IAClE,+BAA+B,0BAA0B,CAAC;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/use-sync-external-store/shim/index.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n"], "names": [], "mappings": "AAEA;;KAEO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/dequal/lite/index.mjs"], "sourcesContent": ["var has = Object.prototype.hasOwnProperty;\n\nexport function dequal(foo, bar) {\n\tvar ctor, len;\n\tif (foo === bar) return true;\n\n\tif (foo && bar && (ctor=foo.constructor) === bar.constructor) {\n\t\tif (ctor === Date) return foo.getTime() === bar.getTime();\n\t\tif (ctor === RegExp) return foo.toString() === bar.toString();\n\n\t\tif (ctor === Array) {\n\t\t\tif ((len=foo.length) === bar.length) {\n\t\t\t\twhile (len-- && dequal(foo[len], bar[len]));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (!ctor || typeof foo === 'object') {\n\t\t\tlen = 0;\n\t\t\tfor (ctor in foo) {\n\t\t\t\tif (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;\n\t\t\t\tif (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;\n\t\t\t}\n\t\t\treturn Object.keys(bar).length === len;\n\t\t}\n\t}\n\n\treturn foo !== foo && bar !== bar;\n}\n"], "names": [], "mappings": ";;;;AAAA,IAAI,MAAM,OAAO,SAAS,CAAC,cAAc;AAElC,SAAS,OAAO,GAAG,EAAE,GAAG;IAC9B,IAAI,MAAM;IACV,IAAI,QAAQ,KAAK,OAAO;IAExB,IAAI,OAAO,OAAO,CAAC,OAAK,IAAI,WAAW,MAAM,IAAI,WAAW,EAAE;QAC7D,IAAI,SAAS,MAAM,OAAO,IAAI,OAAO,OAAO,IAAI,OAAO;QACvD,IAAI,SAAS,QAAQ,OAAO,IAAI,QAAQ,OAAO,IAAI,QAAQ;QAE3D,IAAI,SAAS,OAAO;YACnB,IAAI,CAAC,MAAI,IAAI,MAAM,MAAM,IAAI,MAAM,EAAE;gBACpC,MAAO,SAAS,OAAO,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI;YAC1C;YACA,OAAO,QAAQ,CAAC;QACjB;QAEA,IAAI,CAAC,QAAQ,OAAO,QAAQ,UAAU;YACrC,MAAM;YACN,IAAK,QAAQ,IAAK;gBACjB,IAAI,IAAI,IAAI,CAAC,KAAK,SAAS,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,KAAK,OAAO,OAAO;gBACjE,IAAI,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,GAAG,OAAO;YAC7D;YACA,OAAO,OAAO,IAAI,CAAC,KAAK,MAAM,KAAK;QACpC;IACD;IAEA,OAAO,QAAQ,OAAO,QAAQ;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/dequal/dist/index.mjs"], "sourcesContent": ["var has = Object.prototype.hasOwnProperty;\n\nfunction find(iter, tar, key) {\n\tfor (key of iter.keys()) {\n\t\tif (dequal(key, tar)) return key;\n\t}\n}\n\nexport function dequal(foo, bar) {\n\tvar ctor, len, tmp;\n\tif (foo === bar) return true;\n\n\tif (foo && bar && (ctor=foo.constructor) === bar.constructor) {\n\t\tif (ctor === Date) return foo.getTime() === bar.getTime();\n\t\tif (ctor === RegExp) return foo.toString() === bar.toString();\n\n\t\tif (ctor === Array) {\n\t\t\tif ((len=foo.length) === bar.length) {\n\t\t\t\twhile (len-- && dequal(foo[len], bar[len]));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ctor === Set) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len;\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!bar.has(tmp)) return false;\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === Map) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len[0];\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!dequal(len[1], bar.get(tmp))) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === ArrayBuffer) {\n\t\t\tfoo = new Uint8Array(foo);\n\t\t\tbar = new Uint8Array(bar);\n\t\t} else if (ctor === DataView) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo.getInt8(len) === bar.getInt8(len));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ArrayBuffer.isView(foo)) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo[len] === bar[len]);\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (!ctor || typeof foo === 'object') {\n\t\t\tlen = 0;\n\t\t\tfor (ctor in foo) {\n\t\t\t\tif (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;\n\t\t\t\tif (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;\n\t\t\t}\n\t\t\treturn Object.keys(bar).length === len;\n\t\t}\n\t}\n\n\treturn foo !== foo && bar !== bar;\n}\n"], "names": [], "mappings": ";;;;AAAA,IAAI,MAAM,OAAO,SAAS,CAAC,cAAc;AAEzC,SAAS,KAAK,IAAI,EAAE,GAAG,EAAE,GAAG;IAC3B,KAAK,OAAO,KAAK,IAAI,GAAI;QACxB,IAAI,OAAO,KAAK,MAAM,OAAO;IAC9B;AACD;AAEO,SAAS,OAAO,GAAG,EAAE,GAAG;IAC9B,IAAI,MAAM,KAAK;IACf,IAAI,QAAQ,KAAK,OAAO;IAExB,IAAI,OAAO,OAAO,CAAC,OAAK,IAAI,WAAW,MAAM,IAAI,WAAW,EAAE;QAC7D,IAAI,SAAS,MAAM,OAAO,IAAI,OAAO,OAAO,IAAI,OAAO;QACvD,IAAI,SAAS,QAAQ,OAAO,IAAI,QAAQ,OAAO,IAAI,QAAQ;QAE3D,IAAI,SAAS,OAAO;YACnB,IAAI,CAAC,MAAI,IAAI,MAAM,MAAM,IAAI,MAAM,EAAE;gBACpC,MAAO,SAAS,OAAO,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI;YAC1C;YACA,OAAO,QAAQ,CAAC;QACjB;QAEA,IAAI,SAAS,KAAK;YACjB,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;gBAC1B,OAAO;YACR;YACA,KAAK,OAAO,IAAK;gBAChB,MAAM;gBACN,IAAI,OAAO,OAAO,QAAQ,UAAU;oBACnC,MAAM,KAAK,KAAK;oBAChB,IAAI,CAAC,KAAK,OAAO;gBAClB;gBACA,IAAI,CAAC,IAAI,GAAG,CAAC,MAAM,OAAO;YAC3B;YACA,OAAO;QACR;QAEA,IAAI,SAAS,KAAK;YACjB,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;gBAC1B,OAAO;YACR;YACA,KAAK,OAAO,IAAK;gBAChB,MAAM,GAAG,CAAC,EAAE;gBACZ,IAAI,OAAO,OAAO,QAAQ,UAAU;oBACnC,MAAM,KAAK,KAAK;oBAChB,IAAI,CAAC,KAAK,OAAO;gBAClB;gBACA,IAAI,CAAC,OAAO,GAAG,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,OAAO;oBAClC,OAAO;gBACR;YACD;YACA,OAAO;QACR;QAEA,IAAI,SAAS,aAAa;YACzB,MAAM,IAAI,WAAW;YACrB,MAAM,IAAI,WAAW;QACtB,OAAO,IAAI,SAAS,UAAU;YAC7B,IAAI,CAAC,MAAI,IAAI,UAAU,MAAM,IAAI,UAAU,EAAE;gBAC5C,MAAO,SAAS,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC;YAClD;YACA,OAAO,QAAQ,CAAC;QACjB;QAEA,IAAI,YAAY,MAAM,CAAC,MAAM;YAC5B,IAAI,CAAC,MAAI,IAAI,UAAU,MAAM,IAAI,UAAU,EAAE;gBAC5C,MAAO,SAAS,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI;YACtC;YACA,OAAO,QAAQ,CAAC;QACjB;QAEA,IAAI,CAAC,QAAQ,OAAO,QAAQ,UAAU;YACrC,MAAM;YACN,IAAK,QAAQ,IAAK;gBACjB,IAAI,IAAI,IAAI,CAAC,KAAK,SAAS,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,KAAK,OAAO,OAAO;gBACjE,IAAI,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,GAAG,OAAO;YAC7D;YACA,OAAO,OAAO,IAAI,CAAC,KAAK,MAAM,KAAK;QACpC;IACD;IAEA,OAAO,QAAQ,OAAO,QAAQ;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/client-boundary/controlComponents.ts"], "sourcesContent": ["'use client';\n\nexport {\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>oa<PERSON>,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  ClerkF<PERSON>,\n  SignedOut,\n  SignedIn,\n  Protect,\n  RedirectToSignIn,\n  RedirectToSignUp,\n  RedirectToTasks,\n  RedirectToUserProfile,\n  AuthenticateWithRedirectCallback,\n  RedirectToCreateOrganization,\n  RedirectToOrganizationProfile,\n} from '@clerk/clerk-react';\n\nexport { MultisessionAppSupport } from '@clerk/clerk-react/internal';\n"], "names": [], "mappings": ";;AAEA;AAiBA,SAAS,8BAA8B", "debugId": null}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/client-boundary/hooks/usePagesRouter.tsx"], "sourcesContent": ["import { useRouter } from 'next/compat/router';\n\nexport const usePagesRouter = () => {\n  // The compat version of useRouter returns null instead of throwing an error\n  // when used inside app router instead of pages router\n  // we use it to detect if the component is used inside pages or app router\n  // so we can use the correct algorithm to get the path\n  return { pagesRouter: useRouter() };\n};\n"], "names": [], "mappings": ";;;;AAAA,SAAS,iBAAiB;;;AAEnB,MAAM,iBAAiB,MAAM;IAKlC,OAAO;QAAE,iBAAa,iKAAA,CAAU;IAAE;AACpC", "debugId": null}}, {"offset": {"line": 271, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/client-boundary/hooks/useEnforceCatchAllRoute.tsx"], "sourcesContent": ["import { isProductionEnvironment } from '@clerk/shared/utils';\nimport type { RoutingStrategy } from '@clerk/types';\nimport React from 'react';\n\nimport { useSession } from '../hooks';\nimport { usePagesRouter } from './usePagesRouter';\n\n/**\n * This ugly hook  enforces that the Clerk components are mounted in a catch-all route\n * For pages router, we can parse the pathname we get from the useRouter hook\n * For app router, there is no reliable way to do the same check right now, so we\n * fire a request to a path under window.location.href and we check whether the path\n * exists or not\n */\nexport const useEnforceCatchAllRoute = (\n  component: string,\n  path: string,\n  routing?: RoutingStrategy,\n  requireSessionBeforeCheck = true,\n) => {\n  const ref = React.useRef(0);\n  const { pagesRouter } = usePagesRouter();\n  const { session, isLoaded } = useSession();\n\n  // This check does not break the rules of hooks\n  // as the condition will remain the same for the whole app lifecycle\n  if (isProductionEnvironment()) {\n    return;\n  }\n\n  React.useEffect(() => {\n    if (!isLoaded || (routing && routing !== 'path')) {\n      return;\n    }\n\n    // For components that require an active session, like UserProfile\n    // we should not enforce the catch-all route if there is no session\n    // because these components are usually protected by the middleware\n    // and if the check runs before the session is available, it will fail\n    // even if the route is a catch-all route, as the check request will result\n    // in a 404 because of auth.protect();\n    if (requireSessionBeforeCheck && !session) {\n      return;\n    }\n\n    const ac = new AbortController();\n    const error = () => {\n      const correctPath = pagesRouter ? `${path}/[[...index]].tsx` : `${path}/[[...rest]]/page.tsx`;\n      throw new Error(\n        `\nClerk: The <${component}/> component is not configured correctly. The most likely reasons for this error are:\n\n1. The \"${path}\" route is not a catch-all route.\nIt is recommended to convert this route to a catch-all route, eg: \"${correctPath}\". Alternatively, you can update the <${component}/> component to use hash-based routing by setting the \"routing\" prop to \"hash\".\n\n2. The <${component}/> component is mounted in a catch-all route, but all routes under \"${path}\" are protected by the middleware.\nTo resolve this, ensure that the middleware does not protect the catch-all route or any of its children. If you are using the \"createRouteMatcher\" helper, consider adding \"(.*)\" to the end of the route pattern, eg: \"${path}(.*)\". For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#create-route-matcher\n`,\n      );\n    };\n\n    if (pagesRouter) {\n      if (!pagesRouter.pathname.match(/\\[\\[\\.\\.\\..+]]/)) {\n        error();\n      }\n    } else {\n      const check = async () => {\n        // make sure to run this as soon as possible\n        // but don't run again when strict mode is enabled\n        ref.current++;\n        if (ref.current > 1) {\n          return;\n        }\n        let res;\n        try {\n          const url = `${window.location.origin}${\n            window.location.pathname\n          }/${component}_clerk_catchall_check_${Date.now()}`;\n          res = await fetch(url, { signal: ac.signal });\n        } catch {\n          // no op\n        }\n        if (res?.status === 404) {\n          error();\n        }\n      };\n      void check();\n    }\n\n    return () => {\n      // make sure to run this as soon as possible\n      // but don't run again when strict mode is enabled\n      if (ref.current > 1) {\n        ac.abort();\n      }\n    };\n  }, [isLoaded]);\n};\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,+BAA+B;AAExC,OAAO,WAAW;AAElB,SAAS,kBAAkB;AAC3B,SAAS,sBAAsB;;;;;;AASxB,MAAM,0BAA0B,CACrC,WACA,MACA,SACA,4BAA4B,IAAA,KACzB;IACH,MAAM,MAAM,4NAAA,CAAM,MAAA,CAAO,CAAC;IAC1B,MAAM,EAAE,WAAA,CAAY,CAAA,OAAI,+NAAA,CAAe;IACvC,MAAM,EAAE,OAAA,EAAS,QAAA,CAAS,CAAA,OAAI,sLAAA,CAAW;IAIzC,QAAI,sMAAA,CAAwB,IAAG;QAC7B;IACF;IAEA,4NAAA,CAAM,SAAA,CAAU,MAAM;QACpB,IAAI,CAAC,YAAa,WAAW,YAAY,QAAS;YAChD;QACF;QAQA,IAAI,6BAA6B,CAAC,SAAS;YACzC;QACF;QAEA,MAAM,KAAK,IAAI,gBAAgB;QAC/B,MAAM,QAAQ,MAAM;YAClB,MAAM,cAAc,cAAc,GAAG,IAAI,CAAA,iBAAA,CAAA,GAAsB,GAAG,IAAI,CAAA,qBAAA,CAAA;YACtE,MAAM,IAAI,MACR,CAAA;YAAA,EACM,SAAS,CAAA;;QAAA,EAEb,IAAI,CAAA;mEAAA,EACuD,WAAW,CAAA,sCAAA,EAAyC,SAAS,CAAA;;QAAA,EAExH,SAAS,CAAA,oEAAA,EAAuE,IAAI,CAAA;wNAAA,EAC4H,IAAI,CAAA;AAAA,CAAA;QAG1N;QAEA,IAAI,aAAa;YACf,IAAI,CAAC,YAAY,QAAA,CAAS,KAAA,CAAM,gBAAgB,GAAG;gBACjD,MAAM;YACR;QACF,OAAO;YACL,MAAM,QAAQ,YAAY;gBAGxB,IAAI,OAAA;gBACJ,IAAI,IAAI,OAAA,GAAU,GAAG;oBACnB;gBACF;gBACA,IAAI;gBACJ,IAAI;oBACF,MAAM,MAAM,GAAG,OAAO,QAAA,CAAS,MAAM,GACnC,OAAO,QAAA,CAAS,QAClB,CAAA,CAAA,EAAI,SAAS,CAAA,sBAAA,EAAyB,KAAK,GAAA,CAAI,CAAC,EAAA;oBAChD,MAAM,MAAM,MAAM,KAAK;wBAAE,QAAQ,GAAG,MAAA;oBAAO,CAAC;gBAC9C,EAAA,OAAQ,CAER;gBACA,IAAA,CAAI,OAAA,OAAA,KAAA,IAAA,IAAK,MAAA,MAAW,KAAK;oBACvB,MAAM;gBACR;YACF;YACA,KAAK,MAAM;QACb;QAEA,OAAO,MAAM;YAGX,IAAI,IAAI,OAAA,GAAU,GAAG;gBACnB,GAAG,KAAA,CAAM;YACX;QACF;IACF,GAAG;QAAC,QAAQ;KAAC;AACf", "debugId": null}}, {"offset": {"line": 350, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/client-boundary/hooks/usePathnameWithoutCatchAll.tsx"], "sourcesContent": ["import React from 'react';\n\nimport { usePagesRouter } from './usePagesRouter';\n\nexport const usePathnameWithoutCatchAll = () => {\n  const pathRef = React.useRef<string>();\n\n  const { pagesRouter } = usePagesRouter();\n\n  if (pagesRouter) {\n    if (pathRef.current) {\n      return pathRef.current;\n    } else {\n      // in pages router things are simpler as the pathname includes the catch all route\n      // which starts with [[... and we can just remove it\n      pathRef.current = pagesRouter.pathname.replace(/\\/\\[\\[\\.\\.\\..*/, '');\n      return pathRef.current;\n    }\n  }\n\n  // require is used to avoid importing next/navigation when the pages router is used,\n  // as it will throw an error. We cannot use dynamic import as it is async\n  // and we need the hook to be sync\n  // eslint-disable-next-line @typescript-eslint/no-require-imports\n  const usePathname = require('next/navigation').usePathname;\n  // eslint-disable-next-line @typescript-eslint/no-require-imports\n  const useParams = require('next/navigation').useParams;\n\n  // Get the pathname that includes any named or catch all params\n  // eg:\n  // the filesystem route /user/[id]/profile/[[...rest]]/page.tsx\n  // could give us the following pathname /user/123/profile/security\n  // if the user navigates to the security section of the user profile\n  const pathname = usePathname() || '';\n  const pathParts = pathname.split('/').filter(Boolean);\n  // the useParams hook returns an object with all named and catch all params\n  // for named params, the key in the returned object always contains a single value\n  // for catch all params, the key in the returned object contains an array of values\n  // we find the catch all params by checking if the value is an array\n  // and then we remove one path part for each catch all param\n  const catchAllParams = Object.values(useParams() || {})\n    .filter(v => Array.isArray(v))\n    .flat(Infinity);\n  // so we end up with the pathname where the components are mounted at\n  // eg /user/123/profile/security will return /user/123/profile as the path\n  if (pathRef.current) {\n    return pathRef.current;\n  } else {\n    pathRef.current = `/${pathParts.slice(0, pathParts.length - catchAllParams.length).join('/')}`;\n    return pathRef.current;\n  }\n};\n"], "names": [], "mappings": ";;;;AAAA,OAAO,WAAW;AAElB,SAAS,sBAAsB;;;;AAExB,MAAM,6BAA6B,MAAM;IAC9C,MAAM,UAAU,4NAAA,CAAM,MAAA,CAAe;IAErC,MAAM,EAAE,WAAA,CAAY,CAAA,OAAI,+NAAA,CAAe;IAEvC,IAAI,aAAa;QACf,IAAI,QAAQ,OAAA,EAAS;YACnB,OAAO,QAAQ,OAAA;QACjB,OAAO;YAGL,QAAQ,OAAA,GAAU,YAAY,QAAA,CAAS,OAAA,CAAQ,kBAAkB,EAAE;YACnE,OAAO,QAAQ,OAAA;QACjB;IACF;IAMA,MAAM,cAAc,QAAQ,iBAAiB,4EAAE,WAAA;IAE/C,MAAM,YAAY,QAAQ,iBAAiB,4EAAE,SAAA;IAO7C,MAAM,WAAW,YAAY,KAAK;IAClC,MAAM,YAAY,SAAS,KAAA,CAAM,GAAG,EAAE,MAAA,CAAO,OAAO;IAMpD,MAAM,iBAAiB,OAAO,MAAA,CAAO,UAAU,KAAK,CAAC,CAAC,EACnD,MAAA,CAAO,CAAA,IAAK,MAAM,OAAA,CAAQ,CAAC,CAAC,EAC5B,IAAA,CAAK,QAAQ;IAGhB,IAAI,QAAQ,OAAA,EAAS;QACnB,OAAO,QAAQ,OAAA;IACjB,OAAO;QACL,QAAQ,OAAA,GAAU,CAAA,CAAA,EAAI,UAAU,KAAA,CAAM,GAAG,UAAU,MAAA,GAAS,eAAe,MAAM,EAAE,IAAA,CAAK,GAAG,CAAC,EAAA;QAC5F,OAAO,QAAQ,OAAA;IACjB;AACF", "debugId": null}}, {"offset": {"line": 388, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/client-boundary/hooks/useEnforceRoutingProps.tsx"], "sourcesContent": ["import { useRoutingProps } from '@clerk/clerk-react/internal';\nimport type { RoutingOptions } from '@clerk/types';\n\nimport { useEnforceCatchAllRoute } from './useEnforceCatchAllRoute';\nimport { usePathnameWithoutCatchAll } from './usePathnameWithoutCatchAll';\n\nexport function useEnforceCorrectRoutingProps<T extends RoutingOptions>(\n  componentName: string,\n  props: T,\n  requireSessionBeforeCheck = true,\n): T {\n  const path = usePathnameWithoutCatchAll();\n  const routingProps = useRoutingProps(componentName, props, { path });\n  useEnforceCatchAllRoute(componentName, path, routingProps.routing, requireSessionBeforeCheck);\n  return routingProps;\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,uBAAuB;AAGhC,SAAS,+BAA+B;AACxC,SAAS,kCAAkC;;;;;AAEpC,SAAS,8BACd,aAAA,EACA,KAAA,EACA,4BAA4B,IAAA,EACzB;IACH,MAAM,WAAO,uPAAA,CAA2B;IACxC,MAAM,mBAAe,6MAAA,EAAgB,eAAe,OAAO;QAAE;IAAK,CAAC;IACnE,IAAA,iPAAA,EAAwB,eAAe,MAAM,aAAa,OAAA,EAAS,yBAAyB;IAC5F,OAAO;AACT", "debugId": null}}, {"offset": {"line": 413, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/client-boundary/uiComponents.tsx"], "sourcesContent": ["'use client';\n\nimport {\n  OrganizationProfile as BaseOrganizationProfile,\n  SignIn as BaseSignIn,\n  SignUp as BaseSignUp,\n  UserProfile as BaseUserProfile,\n} from '@clerk/clerk-react';\nimport type { ComponentProps } from 'react';\nimport React from 'react';\n\nimport { useEnforceCorrectRoutingProps } from './hooks/useEnforceRoutingProps';\n\nexport {\n  APIKeys,\n  CreateOrganization,\n  GoogleOneTap,\n  OrganizationList,\n  OrganizationSwitcher,\n  PricingTable,\n  SignInButton,\n  SignInWithMetamaskButton,\n  SignOutButton,\n  SignUpButton,\n  TaskChooseOrganization,\n  UserButton,\n  Waitlist,\n} from '@clerk/clerk-react';\n\n// The assignment of UserProfile with BaseUserProfile props is used\n// to support the CustomPage functionality (eg UserProfile.Page)\n// Also the `typeof BaseUserProfile` is used to resolve the following error:\n// \"The inferred type of 'UserProfile' cannot be named without a reference to ...\"\nexport const UserProfile: typeof BaseUserProfile = Object.assign(\n  (props: ComponentProps<typeof BaseUserProfile>) => {\n    return <BaseUserProfile {...useEnforceCorrectRoutingProps('UserProfile', props)} />;\n  },\n  { ...BaseUserProfile },\n);\n\n// The assignment of OrganizationProfile with BaseOrganizationProfile props is used\n// to support the CustomPage functionality (eg OrganizationProfile.Page)\n// Also the `typeof BaseOrganizationProfile` is used to resolved the following error:\n// \"The inferred type of 'OrganizationProfile' cannot be named without a reference to ...\"\nexport const OrganizationProfile: typeof BaseOrganizationProfile = Object.assign(\n  (props: ComponentProps<typeof BaseOrganizationProfile>) => {\n    return <BaseOrganizationProfile {...useEnforceCorrectRoutingProps('OrganizationProfile', props)} />;\n  },\n  { ...BaseOrganizationProfile },\n);\n\nexport const SignIn = (props: ComponentProps<typeof BaseSignIn>) => {\n  return <BaseSignIn {...useEnforceCorrectRoutingProps('SignIn', props, false)} />;\n};\n\nexport const SignUp = (props: ComponentProps<typeof BaseSignUp>) => {\n  return <BaseSignUp {...useEnforceCorrectRoutingProps('SignUp', props, false)} />;\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;AAOA,OAAO,WAAW;AAElB,SAAS,qCAAqC;;;;;;;AAsBvC,MAAM,cAAsC,OAAO,MAAA,CACxD,CAAC,UAAkD;IACjD,OAAO,aAAA,GAAA,4NAAA,CAAA,aAAA,CAAC,kMAAA,EAAA;QAAiB,OAAG,sPAAA,EAA8B,eAAe,KAAK,CAAA;IAAA,CAAG;AACnF,GACA;IAAE,GAAG,kMAAA;AAAgB;AAOhB,MAAM,sBAAsD,OAAO,MAAA,CACxE,CAAC,UAA0D;IACzD,OAAO,aAAA,GAAA,4NAAA,CAAA,aAAA,CAAC,0MAAA,EAAA;QAAyB,OAAG,sPAAA,EAA8B,uBAAuB,KAAK,CAAA;IAAA,CAAG;AACnG,GACA;IAAE,GAAG,0MAAA;AAAwB;AAGxB,MAAM,SAAS,CAAC,UAA6C;IAClE,OAAO,aAAA,GAAA,4NAAA,CAAA,aAAA,CAAC,6LAAA,EAAA;QAAY,OAAG,sPAAA,EAA8B,UAAU,OAAO,KAAK,CAAA;IAAA,CAAG;AAChF;AAEO,MAAM,SAAS,CAAC,UAA6C;IAClE,OAAO,aAAA,GAAA,4NAAA,CAAA,aAAA,CAAC,6LAAA,EAAA;QAAY,OAAG,sPAAA,EAA8B,UAAU,OAAO,KAAK,CAAA;IAAA,CAAG;AAChF", "debugId": null}}, {"offset": {"line": 506, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/client-boundary/PromisifiedAuthProvider.tsx"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@clerk/clerk-react';\nimport { useDerivedAuth } from '@clerk/clerk-react/internal';\nimport type { InitialState } from '@clerk/types';\nimport { useRouter } from 'next/compat/router';\nimport React from 'react';\n\nconst PromisifiedAuthContext = React.createContext<Promise<InitialState> | InitialState | null>(null);\n\nexport function PromisifiedAuthProvider({\n  authPromise,\n  children,\n}: {\n  authPromise: Promise<InitialState> | InitialState;\n  children: React.ReactNode;\n}) {\n  return <PromisifiedAuthContext.Provider value={authPromise}>{children}</PromisifiedAuthContext.Provider>;\n}\n\n/**\n * Returns the current auth state, the user and session ids and the `getToken`\n * that can be used to retrieve the given template or the default Clerk token.\n *\n * Until Clerk loads, `isLoaded` will be set to `false`.\n * Once Clerk loads, `isLoaded` will be set to `true`, and you can\n * safely access the `userId` and `sessionId` variables.\n *\n * For projects using NextJs or Remix, you can have immediate access to this data during SSR\n * simply by using the `ClerkProvider`.\n *\n * @example\n * import { useAuth } from '@clerk/nextjs'\n *\n * function Hello() {\n *   const { isSignedIn, sessionId, userId } = useAuth();\n *   if(isSignedIn) {\n *     return null;\n *   }\n *   console.log(sessionId, userId)\n *   return <div>...</div>\n * }\n *\n * @example\n * This page will be fully rendered during SSR.\n *\n * ```tsx\n * import { useAuth } from '@clerk/nextjs'\n *\n * export HelloPage = () => {\n *   const { isSignedIn, sessionId, userId } = useAuth();\n *   console.log(isSignedIn, sessionId, userId)\n *   return <div>...</div>\n * }\n * ```\n */\nexport function usePromisifiedAuth(options: Parameters<typeof useAuth>[0] = {}) {\n  const isPagesRouter = useRouter();\n  const valueFromContext = React.useContext(PromisifiedAuthContext);\n\n  let resolvedData = valueFromContext;\n  if (valueFromContext && 'then' in valueFromContext) {\n    resolvedData = React.use(valueFromContext);\n  }\n\n  // At this point we should have a usable auth object\n  if (typeof window === 'undefined') {\n    // Pages router should always use useAuth as it is able to grab initial auth state from context during SSR.\n    if (isPagesRouter) {\n      return useAuth(options);\n    }\n\n    // We don't need to deal with Clerk being loaded here\n    return useDerivedAuth({ ...resolvedData, ...options });\n  } else {\n    return useAuth({ ...resolvedData, ...options });\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAEA,SAAS,eAAe;;AACxB,SAAS,sBAAsB;AAE/B,SAAS,iBAAiB;AAC1B,OAAO,WAAW;;;;;;;AAElB,MAAM,yBAAyB,4NAAA,CAAM,aAAA,CAA2D,IAAI;AAE7F,SAAS,wBAAwB,EACtC,WAAA,EACA,QAAA,EACF,EAGG;IACD,OAAO,aAAA,GAAA,4NAAA,CAAA,aAAA,CAAC,uBAAuB,QAAA,EAAvB;QAAgC,OAAO;IAAA,GAAc,QAAS;AACxE;AAsCO,SAAS,mBAAmB,UAAyC,CAAC,CAAA,EAAG;IAC9E,MAAM,oBAAgB,iKAAA,CAAU;IAChC,MAAM,mBAAmB,4NAAA,CAAM,UAAA,CAAW,sBAAsB;IAEhE,IAAI,eAAe;IACnB,IAAI,oBAAoB,UAAU,kBAAkB;QAClD,eAAe,4NAAA,CAAM,GAAA,CAAI,gBAAgB;IAC3C;IAGA,IAAI,OAAO,WAAW,kBAAa;QAEjC,IAAI,eAAe;YACjB,WAAO,8MAAA,EAAQ,OAAO;QACxB;QAGA,WAAO,qNAAA,EAAe;YAAE,GAAG,YAAA;YAAc,GAAG,OAAA;QAAQ,CAAC;IACvD,OAAO;;AAGT", "debugId": null}}, {"offset": {"line": 553, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/client-boundary/hooks.ts"], "sourcesContent": ["'use client';\n\nexport {\n  useClerk,\n  useEmailLink,\n  useOrganization,\n  useOrganizationList,\n  useSession,\n  useSessionList,\n  useSignIn,\n  useSignUp,\n  useUser,\n  useReverification,\n} from '@clerk/clerk-react';\n\nexport {\n  isClerkAPIResponseError,\n  isClerkRuntimeError,\n  isEmailLinkError,\n  isKnownError,\n  isMetamaskError,\n  isReverificationCancelledError,\n  EmailLinkErrorCode,\n  EmailLinkErrorCodeStatus,\n} from '@clerk/clerk-react/errors';\n\nexport { usePromisifiedAuth as useAuth } from './PromisifiedAuthProvider';\n"], "names": [], "mappings": ";;;AAEA;;AAaA;AAWA,SAA+B,0BAAe", "debugId": null}}, {"offset": {"line": 620, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/client-boundary/hooks/useSafeLayoutEffect.tsx"], "sourcesContent": ["import React from 'react';\n\n// TODO: Import from shared once [JS-118] is done\nexport const useSafeLayoutEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\n"], "names": [], "mappings": ";;;;AAAA,OAAO,WAAW;;;AAGX,MAAM,sBAAsB,OAAO,WAAW,oBAAc,MAAM,oBAAkB,4NAAA,CAAM,SAAA", "debugId": null}}, {"offset": {"line": 634, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/client-boundary/NextOptionsContext.tsx"], "sourcesContent": ["import React from 'react';\n\nimport type { NextClerkProviderProps } from '../types';\n\ntype ClerkNextContextValue = Partial<Omit<NextClerkProviderProps, 'children'>>;\n\nconst ClerkNextOptionsCtx = React.createContext<{ value: ClerkNextContextValue } | undefined>(undefined);\nClerkNextOptionsCtx.displayName = 'ClerkNextOptionsCtx';\n\nconst useClerkNextOptions = () => {\n  const ctx = React.useContext(ClerkNextOptionsCtx) as { value: ClerkNextContextValue };\n  return ctx?.value;\n};\n\nconst ClerkNextOptionsProvider = (\n  props: React.PropsWithChildren<{ options: ClerkNextContextValue }>,\n): React.JSX.Element => {\n  const { children, options } = props;\n  return <ClerkNextOptionsCtx.Provider value={{ value: options }}>{children}</ClerkNextOptionsCtx.Provider>;\n};\n\nexport { ClerkNextOptionsProvider, useClerkNextOptions };\n"], "names": [], "mappings": ";;;;;;AAAA,OAAO,WAAW;;;AAMlB,MAAM,sBAAsB,4NAAA,CAAM,aAAA,CAA4D,KAAA,CAAS;AACvG,oBAAoB,WAAA,GAAc;AAElC,MAAM,sBAAsB,MAAM;IAChC,MAAM,MAAM,4NAAA,CAAM,UAAA,CAAW,mBAAmB;IAChD,OAAO,OAAA,OAAA,KAAA,IAAA,IAAK,KAAA;AACd;AAEA,MAAM,2BAA2B,CAC/B,UACsB;IACtB,MAAM,EAAE,QAAA,EAAU,OAAA,CAAQ,CAAA,GAAI;IAC9B,OAAO,aAAA,GAAA,4NAAA,CAAA,aAAA,CAAC,oBAAoB,QAAA,EAApB;QAA6B,OAAO;YAAE,OAAO;QAAQ;IAAA,GAAI,QAAS;AAC5E", "debugId": null}}, {"offset": {"line": 663, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/utils/clerk-js-script.tsx"], "sourcesContent": ["import { useClerk } from '@clerk/clerk-react';\nimport { buildClerkJsScriptAttributes, clerkJsScriptUrl } from '@clerk/clerk-react/internal';\nimport NextScript from 'next/script';\nimport React from 'react';\n\nimport { useClerkNextOptions } from '../client-boundary/NextOptionsContext';\n\ntype ClerkJSScriptProps = {\n  router: 'app' | 'pages';\n};\n\nfunction ClerkJSScript(props: ClerkJSScriptProps) {\n  const { publishable<PERSON>ey, clerkJSU<PERSON>, clerkJ<PERSON><PERSON><PERSON>, clerkJSV<PERSON>t, nonce } = useClerkNextOptions();\n  const { domain, proxyUrl } = useClerk();\n\n  /**\n   * If no publishable key, avoid appending an invalid script in the DOM.\n   */\n  if (!publishableKey) {\n    return null;\n  }\n\n  const options = {\n    domain,\n    proxyUrl,\n    publishable<PERSON><PERSON>,\n    clerkJ<PERSON>rl,\n    clerkJSVersion,\n    clerkJSV<PERSON>t,\n    nonce,\n  };\n  const scriptUrl = clerkJsScriptUrl(options);\n\n  /**\n   * Notes:\n   * `next/script` in 13.x.x when used with App Router will fail to pass any of our `data-*` attributes, resulting in errors\n   * Nextjs App Router will automatically move inline scripts inside `<head/>`\n   * Using the `nextjs/script` for App Router with the `beforeInteractive` strategy will throw an error because our custom script will be mounted outside the `html` tag.\n   */\n  const Script = props.router === 'app' ? 'script' : NextScript;\n\n  return (\n    <Script\n      src={scriptUrl}\n      data-clerk-js-script\n      async\n      // `nextjs/script` will add defer by default and does not get removed when we async is true\n      defer={props.router === 'pages' ? false : undefined}\n      crossOrigin='anonymous'\n      strategy={props.router === 'pages' ? 'beforeInteractive' : undefined}\n      {...buildClerkJsScriptAttributes(options)}\n    />\n  );\n}\n\nexport { ClerkJSScript };\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,gBAAgB;;AACzB,SAAS,8BAA8B,wBAAwB;AAC/D,OAAO,gBAAgB;AACvB,OAAO,WAAW;AAElB,SAAS,2BAA2B;;;;;;;AAMpC,SAAS,cAAc,KAAA,EAA2B;IAChD,MAAM,EAAE,cAAA,EAAgB,UAAA,EAAY,cAAA,EAAgB,cAAA,EAAgB,KAAA,CAAM,CAAA,OAAI,+NAAA,CAAoB;IAClG,MAAM,EAAE,MAAA,EAAQ,QAAA,CAAS,CAAA,OAAI,oLAAA,CAAS;IAKtC,IAAI,CAAC,gBAAgB;QACnB,OAAO;IACT;IAEA,MAAM,UAAU;QACd;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IACA,MAAM,gBAAY,+LAAA,EAAiB,OAAO;IAQ1C,MAAM,SAAS,MAAM,MAAA,KAAW,QAAQ,WAAW,qJAAA;IAEnD,OACE,aAAA,GAAA,4NAAA,CAAA,aAAA,CAAC,QAAA;QACC,KAAK;QACL,wBAAoB;QACpB,OAAK;QAEL,OAAO,MAAM,MAAA,KAAW,UAAU,QAAQ,KAAA;QAC1C,aAAY;QACZ,UAAU,MAAM,MAAA,KAAW,UAAU,sBAAsB,KAAA;QAC1D,OAAG,2MAAA,EAA6B,OAAO,CAAA;IAAA;AAG9C", "debugId": null}}, {"offset": {"line": 713, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/server/constants.ts"], "sourcesContent": ["import { apiUrlFromPublishableKey } from '@clerk/shared/apiUrlFromPublishableKey';\nimport { isTruthy } from '@clerk/shared/underscore';\n\nexport const CLERK_JS_VERSION = process.env.NEXT_PUBLIC_CLERK_JS_VERSION || '';\nexport const CLERK_JS_URL = process.env.NEXT_PUBLIC_CLERK_JS_URL || '';\nexport const API_VERSION = process.env.CLERK_API_VERSION || 'v1';\nexport const SECRET_KEY = process.env.CLERK_SECRET_KEY || '';\nexport const MACHINE_SECRET_KEY = process.env.CLERK_MACHINE_SECRET_KEY || '';\nexport const PUBLISHABLE_KEY = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY || '';\nexport const ENCRYPTION_KEY = process.env.CLERK_ENCRYPTION_KEY || '';\nexport const API_URL = process.env.CLERK_API_URL || apiUrlFromPublishableKey(PUBLISHABLE_KEY);\nexport const DOMAIN = process.env.NEXT_PUBLIC_CLERK_DOMAIN || '';\nexport const PROXY_URL = process.env.NEXT_PUBLIC_CLERK_PROXY_URL || '';\nexport const IS_SATELLITE = isTruthy(process.env.NEXT_PUBLIC_CLERK_IS_SATELLITE) || false;\nexport const SIGN_IN_URL = process.env.NEXT_PUBLIC_CLERK_SIGN_IN_URL || '';\nexport const SIGN_UP_URL = process.env.NEXT_PUBLIC_CLERK_SIGN_UP_URL || '';\nexport const SDK_METADATA = {\n  name: PACKAGE_NAME,\n  version: PACKAGE_VERSION,\n  environment: process.env.NODE_ENV,\n};\n\nexport const TELEMETRY_DISABLED = isTruthy(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DISABLED);\nexport const TELEMETRY_DEBUG = isTruthy(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DEBUG);\n\nexport const KEYLESS_DISABLED = isTruthy(process.env.NEXT_PUBLIC_CLERK_KEYLESS_DISABLED) || false;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAAS,gCAAgC;;AACzC,SAAS,gBAAgB;;;;AAElB,MAAM,mBAAmB,QAAQ,GAAA,CAAI,4BAAA,IAAgC;AACrE,MAAM,eAAe,QAAQ,GAAA,CAAI,wBAAA,IAA4B;AAC7D,MAAM,cAAc,QAAQ,GAAA,CAAI,iBAAA,IAAqB;AACrD,MAAM,aAAa,QAAQ,GAAA,CAAI,gBAAA,IAAoB;AACnD,MAAM,qBAAqB,QAAQ,GAAA,CAAI,wBAAA,IAA4B;AACnE,MAAM,kBAAkB,QAAQ,IAAI,sFAAqC;AACzE,MAAM,iBAAiB,QAAQ,GAAA,CAAI,oBAAA,IAAwB;AAC3D,MAAM,UAAU,QAAQ,GAAA,CAAI,aAAA,QAAiB,uMAAA,EAAyB,eAAe;AACrF,MAAM,SAAS,QAAQ,GAAA,CAAI,wBAAA,IAA4B;AACvD,MAAM,YAAY,QAAQ,GAAA,CAAI,2BAAA,IAA+B;AAC7D,MAAM,mBAAe,uLAAA,EAAS,QAAQ,GAAA,CAAI,8BAA8B,KAAK;AAC7E,MAAM,cAAc,QAAQ,GAAA,CAAI,6BAAA,IAAiC;AACjE,MAAM,cAAc,QAAQ,GAAA,CAAI,6BAAA,IAAiC;AACjE,MAAM,eAAe;IAC1B,MAAM;IACN,SAAS;IACT,WAAA,EAAa,QAAQ,IAAI;AAC3B;AAEO,MAAM,yBAAqB,uLAAA,EAAS,QAAQ,GAAA,CAAI,oCAAoC;AACpF,MAAM,sBAAkB,uLAAA,EAAS,QAAQ,GAAA,CAAI,iCAAiC;AAE9E,MAAM,uBAAmB,uLAAA,EAAS,QAAQ,GAAA,CAAI,kCAAkC,KAAK", "debugId": null}}, {"offset": {"line": 783, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/utils/sdk-versions.ts"], "sourcesContent": ["import nextPkg from 'next/package.json';\n\nconst isNext13 = nextPkg.version.startsWith('13.');\n\n/**\n * Those versions are affected by a bundling issue that will break the application if `node:fs` is used inside a server function.\n * The affected versions are >=next@13.5.4 and <=next@14.0.4\n */\nconst isNextWithUnstableServerActions = isNext13 || nextPkg.version.startsWith('14.0');\n\nexport { isNext13, isNextWithUnstableServerActions };\n"], "names": [], "mappings": ";;;;;;AAAA,OAAO,aAAa;;;AAEpB,MAAM,WAAW,8HAAA,CAAQ,OAAA,CAAQ,UAAA,CAAW,KAAK;AAMjD,MAAM,kCAAkC,YAAY,8HAAA,CAAQ,OAAA,CAAQ,UAAA,CAAW,MAAM", "debugId": null}}, {"offset": {"line": 800, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/utils/feature-flags.ts"], "sourcesContent": ["import { isDevelopmentEnvironment } from '@clerk/shared/utils';\n\nimport { KEYLESS_DISABLED } from '../server/constants';\nimport { isNextWithUnstableServerActions } from './sdk-versions';\n\nconst canUseKeyless =\n  !isNextWithUnstableServerActions &&\n  // Next.js will inline the value of 'development' or 'production' on the client bundle, so this is client-safe.\n  isDevelopmentEnvironment() &&\n  !KEYLESS_DISABLED;\n\nexport { canUseKeyless };\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,gCAAgC;AAEzC,SAAS,wBAAwB;AACjC,SAAS,uCAAuC;;;;;AAEhD,MAAM,gBACJ,CAAC,2NAAA,IAAA,+GAAA;IAED,uMAAA,CAAyB,MACzB,CAAC,uMAAA", "debugId": null}}, {"offset": {"line": 820, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/utils/mergeNextClerkPropsWithEnv.ts"], "sourcesContent": ["import { isTruthy } from '@clerk/shared/underscore';\n\nimport { SDK_METADATA } from '../server/constants';\nimport type { NextClerkProviderProps } from '../types';\n\n// @ts-ignore - https://github.com/microsoft/TypeScript/issues/47663\nexport const mergeNextClerkPropsWithEnv = (props: Omit<NextClerkProviderProps, 'children'>): any => {\n  return {\n    ...props,\n    publishableKey: props.publishableKey || process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY || '',\n    clerkJSUrl: props.clerkJSUrl || process.env.NEXT_PUBLIC_CLERK_JS_URL,\n    clerkJSVersion: props.clerkJSVersion || process.env.NEXT_PUBLIC_CLERK_JS_VERSION,\n    proxyUrl: props.proxyUrl || process.env.NEXT_PUBLIC_CLERK_PROXY_URL || '',\n    domain: props.domain || process.env.NEXT_PUBLIC_CLERK_DOMAIN || '',\n    isSatellite: props.isSatellite || isTruthy(process.env.NEXT_PUBLIC_CLERK_IS_SATELLITE),\n    signInUrl: props.signInUrl || process.env.NEXT_PUBLIC_CLERK_SIGN_IN_URL || '',\n    signUpUrl: props.signUpUrl || process.env.NEXT_PUBLIC_CLERK_SIGN_UP_URL || '',\n    signInForceRedirectUrl:\n      props.signInForceRedirectUrl || process.env.NEXT_PUBLIC_CLERK_SIGN_IN_FORCE_REDIRECT_URL || '',\n    signUpForceRedirectUrl:\n      props.signUpForceRedirectUrl || process.env.NEXT_PUBLIC_CLERK_SIGN_UP_FORCE_REDIRECT_URL || '',\n    signInFallbackRedirectUrl:\n      props.signInFallbackRedirectUrl || process.env.NEXT_PUBLIC_CLERK_SIGN_IN_FALLBACK_REDIRECT_URL || '',\n    signUpFallbackRedirectUrl:\n      props.signUpFallbackRedirectUrl || process.env.NEXT_PUBLIC_CLERK_SIGN_UP_FALLBACK_REDIRECT_URL || '',\n    afterSignInUrl: props.afterSignInUrl || process.env.NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL || '',\n    afterSignUpUrl: props.afterSignUpUrl || process.env.NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL || '',\n    newSubscriptionRedirectUrl:\n      props.newSubscriptionRedirectUrl || process.env.NEXT_PUBLIC_CLERK_CHECKOUT_CONTINUE_URL || '',\n    telemetry: props.telemetry ?? {\n      disabled: isTruthy(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DISABLED),\n      debug: isTruthy(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DEBUG),\n    },\n    sdkMetadata: SDK_METADATA,\n  };\n};\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,gBAAgB;AAEzB,SAAS,oBAAoB;;;;AAItB,MAAM,6BAA6B,CAAC,UAAyD;IANpG,IAAA;IAOE,OAAO;QACL,GAAG,KAAA;QACH,gBAAgB,MAAM,cAAA,IAAkB,QAAQ,IAAI,sFAAqC;QACzF,YAAY,MAAM,UAAA,IAAc,QAAQ,GAAA,CAAI,wBAAA;QAC5C,gBAAgB,MAAM,cAAA,IAAkB,QAAQ,GAAA,CAAI,4BAAA;QACpD,UAAU,MAAM,QAAA,IAAY,QAAQ,GAAA,CAAI,2BAAA,IAA+B;QACvE,QAAQ,MAAM,MAAA,IAAU,QAAQ,GAAA,CAAI,wBAAA,IAA4B;QAChE,aAAa,MAAM,WAAA,QAAe,uLAAA,EAAS,QAAQ,GAAA,CAAI,8BAA8B;QACrF,WAAW,MAAM,SAAA,IAAa,QAAQ,GAAA,CAAI,6BAAA,IAAiC;QAC3E,WAAW,MAAM,SAAA,IAAa,QAAQ,GAAA,CAAI,6BAAA,IAAiC;QAC3E,wBACE,MAAM,sBAAA,IAA0B,QAAQ,GAAA,CAAI,4CAAA,IAAgD;QAC9F,wBACE,MAAM,sBAAA,IAA0B,QAAQ,GAAA,CAAI,4CAAA,IAAgD;QAC9F,2BACE,MAAM,yBAAA,IAA6B,QAAQ,GAAA,CAAI,+CAAA,IAAmD;QACpG,2BACE,MAAM,yBAAA,IAA6B,QAAQ,GAAA,CAAI,+CAAA,IAAmD;QACpG,gBAAgB,MAAM,cAAA,IAAkB,QAAQ,GAAA,CAAI,mCAAA,IAAuC;QAC3F,gBAAgB,MAAM,cAAA,IAAkB,QAAQ,GAAA,CAAI,mCAAA,IAAuC;QAC3F,4BACE,MAAM,0BAAA,IAA8B,QAAQ,GAAA,CAAI,uCAAA,IAA2C;QAC7F,WAAA,CAAW,KAAA,MAAM,SAAA,KAAN,OAAA,KAAmB;YAC5B,cAAU,uLAAA,EAAS,QAAQ,GAAA,CAAI,oCAAoC;YACnE,WAAO,uLAAA,EAAS,QAAQ,GAAA,CAAI,iCAAiC;QAC/D;QACA,aAAa,mMAAA;IACf;AACF", "debugId": null}}, {"offset": {"line": 862, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/utils/router-telemetry.ts"], "sourcesContent": ["import { eventFrameworkMetadata } from '@clerk/shared/telemetry';\n\nimport { useClerk } from '../client-boundary/hooks';\nimport { usePagesRouter } from '../client-boundary/hooks/usePagesRouter';\n\nconst RouterTelemetry = () => {\n  const clerk = useClerk();\n  const { pagesRouter } = usePagesRouter();\n\n  /**\n   * Caching and throttling is handled internally it's safe to execute on every navigation.\n   */\n  clerk.telemetry?.record(\n    eventFrameworkMetadata({\n      router: pagesRouter ? 'pages' : 'app',\n      ...(globalThis?.next?.version ? { nextjsVersion: globalThis.next.version } : {}),\n    }),\n  );\n\n  return null;\n};\n\nexport { RouterTelemetry };\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,8BAA8B;AAEvC,SAAS,gBAAgB;AACzB,SAAS,sBAAsB;;;;;AAE/B,MAAM,kBAAkB,MAAM;IAL9B,IAAA,IAAA;IAME,MAAM,YAAQ,oLAAA,CAAS;IACvB,MAAM,EAAE,WAAA,CAAY,CAAA,OAAI,+NAAA,CAAe;IAKvC,CAAA,KAAA,MAAM,SAAA,KAAN,OAAA,KAAA,IAAA,GAAiB,MAAA,KACf,qMAAA,EAAuB;QACrB,QAAQ,cAAc,UAAU;QAChC,GAAA,CAAA,CAAI,KAAA,cAAA,OAAA,KAAA,IAAA,WAAY,IAAA,KAAZ,OAAA,KAAA,IAAA,GAAkB,OAAA,IAAU;YAAE,eAAe,WAAW,IAAA,CAAK,OAAA;QAAQ,IAAI,CAAC,CAAA;IAChF,CAAC;IAGH,OAAO;AACT", "debugId": null}}, {"offset": {"line": 892, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/dist/esm/app-router/keyless-actions.js"], "sourcesContent": ["\"use server\";\nimport { cookies, headers } from \"next/headers\";\nimport { redirect, RedirectType } from \"next/navigation\";\nimport { errorThrower } from \"../server/errorThrower\";\nimport { detectClerkMiddleware } from \"../server/headers-utils\";\nimport { getKeylessCookieName, getKeylessCookieValue } from \"../server/keyless\";\nimport { canUseKeyless } from \"../utils/feature-flags\";\nconst keylessCookieConfig = {\n  secure: false,\n  httpOnly: false,\n  sameSite: \"lax\"\n};\nasync function syncKeylessConfigAction(args) {\n  const { claimUrl, publishableKey, secretKey, returnUrl } = args;\n  const cookieStore = await cookies();\n  const request = new Request(\"https://placeholder.com\", { headers: await headers() });\n  const keyless = await getKeylessCookieValue((name) => {\n    var _a;\n    return (_a = cookieStore.get(name)) == null ? void 0 : _a.value;\n  });\n  const pksMatch = (keyless == null ? void 0 : keyless.publishableKey) === publishableKey;\n  const sksMatch = (keyless == null ? void 0 : keyless.secretKey) === secretKey;\n  if (pksMatch && sksMatch) {\n    return;\n  }\n  cookieStore.set(\n    await getKeylessCookieName(),\n    JSON.stringify({ claimUrl, publishableKey, secretKey }),\n    keylessCookieConfig\n  );\n  if (detectClerkMiddleware(request)) {\n    redirect(`/clerk-sync-keyless?returnUrl=${returnUrl}`, RedirectType.replace);\n    return;\n  }\n  return;\n}\nasync function createOrReadKeylessAction() {\n  if (!canUseKeyless) {\n    return null;\n  }\n  const result = await import(\"../server/keyless-node.js\").then((m) => m.createOrReadKeyless()).catch(() => null);\n  if (!result) {\n    errorThrower.throwMissingPublishableKeyError();\n    return null;\n  }\n  const { clerkDevelopmentCache, createKeylessModeMessage } = await import(\"../server/keyless-log-cache.js\");\n  clerkDevelopmentCache == null ? void 0 : clerkDevelopmentCache.log({\n    cacheKey: result.publishableKey,\n    msg: createKeylessModeMessage(result)\n  });\n  const { claimUrl, publishableKey, secretKey, apiKeysUrl } = result;\n  void (await cookies()).set(\n    await getKeylessCookieName(),\n    JSON.stringify({ claimUrl, publishableKey, secretKey }),\n    keylessCookieConfig\n  );\n  return {\n    claimUrl,\n    publishableKey,\n    apiKeysUrl\n  };\n}\nasync function deleteKeylessAction() {\n  if (!canUseKeyless) {\n    return;\n  }\n  await import(\"../server/keyless-node.js\").then((m) => m.removeKeyless()).catch(() => {\n  });\n  return;\n}\nasync function detectKeylessEnvDriftAction() {\n  if (!canUseKeyless) {\n    return;\n  }\n  try {\n    const { detectKeylessEnvDrift } = await import(\"../server/keyless-telemetry.js\");\n    await detectKeylessEnvDrift();\n  } catch {\n  }\n}\nexport {\n  createOrReadKeylessAction,\n  deleteKeylessAction,\n  detectKeylessEnvDriftAction,\n  syncKeylessConfigAction\n};\n"], "names": [], "mappings": ";;;;;;;IAmFE,8BAAA,WAAA,GAAA,IAAA,2PAAA,EAAA,8CAAA,gPAAA,EAAA,KAAA,GAAA,sPAAA,EAAA", "debugId": null}}, {"offset": {"line": 904, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/dist/esm/app-router/server-actions.js"], "sourcesContent": ["\"use server\";\nimport { cookies } from \"next/headers\";\nasync function invalidateCacheAction() {\n  void (await cookies()).delete(`__clerk_invalidate_cache_cookie_${Date.now()}`);\n}\nexport {\n  invalidateCacheAction\n};\n"], "names": [], "mappings": ";;;;;;;IAME,wBAAA,WAAA,GAAA,IAAA,2PAAA,EAAA,8CAAA,gPAAA,EAAA,KAAA,GAAA,sPAAA,EAAA", "debugId": null}}, {"offset": {"line": 916, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/utils/removeBasePath.ts"], "sourcesContent": ["/**\n * Removes the Next.js basePath from the provided destination if set.\n * @param to Destination route to navigate to\n * @returns Destination without basePath, if set\n */\nexport function removeBasePath(to: string): string {\n  let destination = to;\n  const basePath = process.env.__NEXT_ROUTER_BASEPATH;\n  if (basePath && destination.startsWith(basePath)) {\n    destination = destination.slice(basePath.length);\n  }\n\n  return destination;\n}\n"], "names": [], "mappings": ";;;;;AAKO,SAAS,eAAe,EAAA,EAAoB;IACjD,IAAI,cAAc;IAClB,MAAM,WAAW,QAAQ,IAAI;IAC7B,IAAI,YAAY,YAAY,UAAA,CAAW,QAAQ,GAAG;;IAIlD,OAAO;AACT", "debugId": null}}, {"offset": {"line": 934, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/app-router/client/useInternalNavFun.ts"], "sourcesContent": ["import type { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime';\nimport { usePathname } from 'next/navigation';\nimport { useCallback, useEffect, useTransition } from 'react';\n\nimport { removeBasePath } from '../../utils/removeBasePath';\n\nconst getClerkNavigationObject = (name: string) => {\n  window.__clerk_internal_navigations ??= {};\n  // @ts-ignore\n  window.__clerk_internal_navigations[name] ??= {};\n  return window.__clerk_internal_navigations[name];\n};\n\nexport const useInternalNavFun = (props: {\n  windowNav: typeof window.history.pushState | typeof window.history.replaceState | undefined;\n  routerNav: AppRouterInstance['push'] | AppRouterInstance['replace'];\n  name: string;\n}): NavigationFunction => {\n  const { windowNav, routerNav, name } = props;\n  const pathname = usePathname();\n  const [isPending, startTransition] = useTransition();\n\n  if (windowNav) {\n    getClerkNavigationObject(name).fun = (to, opts) => {\n      return new Promise<void>(res => {\n        // We need to use window to store the reference to the buffer,\n        // as ClerkProvider might be unmounted and remounted during navigations\n        // If we use a ref, it will be reset when ClerkProvider is unmounted\n        getClerkNavigationObject(name).promisesBuffer ??= [];\n        getClerkNavigationObject(name).promisesBuffer?.push(res);\n        startTransition(() => {\n          // If the navigation is internal, we should use the history API to navigate\n          // as this is the way to perform a shallow navigation in Next.js App Router\n          // without unmounting/remounting the page or fetching data from the server.\n          if (opts?.__internal_metadata?.navigationType === 'internal') {\n            // In 14.1.0, useSearchParams becomes reactive to shallow updates,\n            // but only if passing `null` as the history state.\n            // Older versions need to maintain the history state for push/replace to work,\n            // without affecting how the Next router works.\n            const state = ((window as any).next?.version ?? '') < '14.1.0' ? history.state : null;\n            windowNav(state, '', to);\n          } else {\n            // If the navigation is external (usually when navigating away from the component but still within the app),\n            // we should use the Next.js router to navigate as it will handle updating the URL and also\n            // fetching the new page if necessary.\n            routerNav(removeBasePath(to));\n          }\n        });\n      });\n    };\n  }\n\n  const flushPromises = () => {\n    getClerkNavigationObject(name).promisesBuffer?.forEach(resolve => resolve());\n    getClerkNavigationObject(name).promisesBuffer = [];\n  };\n\n  // Flush any pending promises on mount/unmount\n  useEffect(() => {\n    flushPromises();\n    return flushPromises;\n  }, []);\n\n  // Handle flushing the promise buffer when a navigation happens\n  useEffect(() => {\n    if (!isPending) {\n      flushPromises();\n    }\n  }, [pathname, isPending]);\n\n  return useCallback<NavigationFunction>((to, metadata) => {\n    return getClerkNavigationObject(name).fun(to, metadata);\n    // We are not expecting name to change\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n};\n"], "names": ["_a", "_b", "_c"], "mappings": ";;;;AACA,SAAS,mBAAmB;AAC5B,SAAS,aAAa,WAAW,qBAAqB;AAEtD,SAAS,sBAAsB;;;;;AAE/B,MAAM,2BAA2B,CAAC,SAAiB;IANnD,IAAA,IAAA,IAAA;IAOE,CAAA,KAAA,OAAO,4BAAA,KAAP,OAAA,KAAA,OAAO,4BAAA,GAAiC,CAAC;IAEzC,CAAA,KAAA,CAAA,KAAA,OAAO,4BAAA,CAAA,CAAP,KAAA,KAAA,OAAA,KAAA,EAAA,CAAA,KAAA,GAA8C,CAAC;IAC/C,OAAO,OAAO,4BAAA,CAA6B,IAAI,CAAA;AACjD;AAEO,MAAM,oBAAoB,CAAC,UAIR;IACxB,MAAM,EAAE,SAAA,EAAW,SAAA,EAAW,IAAA,CAAK,CAAA,GAAI;IACvC,MAAM,eAAW,6JAAA,CAAY;IAC7B,MAAM,CAAC,WAAW,eAAe,CAAA,OAAI,kOAAA,CAAc;IAEnD,IAAI,WAAW;QACb,yBAAyB,IAAI,EAAE,GAAA,GAAM,CAAC,IAAI,SAAS;YACjD,OAAO,IAAI,QAAc,CAAA,QAAO;gBAxBtC,IAAA,IAAA,IAAA;gBA4BQ,CAAA,KAAA,CAAA,KAAA,yBAAyB,IAAI,CAAA,EAAE,cAAA,KAA/B,OAAA,KAAA,GAA+B,cAAA,GAAmB,CAAC,CAAA;gBACnD,CAAA,KAAA,yBAAyB,IAAI,EAAE,cAAA,KAA/B,OAAA,KAAA,IAAA,GAA+C,IAAA,CAAK;gBACpD,gBAAgB,MAAM;oBA9B9B,IAAAA,KAAAC,KAAAC;oBAkCU,IAAA,CAAA,CAAIF,MAAA,QAAA,OAAA,KAAA,IAAA,KAAM,mBAAA,KAAN,OAAA,KAAA,IAAAA,IAA2B,cAAA,MAAmB,YAAY;wBAK5D,MAAM,QAAA,CAAA,CAAUE,MAAAA,CAAAD,MAAA,OAAe,IAAA,KAAf,OAAA,KAAA,IAAAA,IAAqB,OAAA,KAArB,OAAAC,MAAgC,EAAA,IAAM,WAAW,QAAQ,KAAA,GAAQ;wBACjF,UAAU,OAAO,IAAI,EAAE;oBACzB,OAAO;wBAIL,cAAU,yMAAA,EAAe,EAAE,CAAC;oBAC9B;gBACF,CAAC;YACH,CAAC;QACH;IACF;IAEA,MAAM,gBAAgB,MAAM;QApD9B,IAAA;QAqDI,CAAA,KAAA,yBAAyB,IAAI,EAAE,cAAA,KAA/B,OAAA,KAAA,IAAA,GAA+C,OAAA,CAAQ,CAAA,UAAW,QAAQ;QAC1E,yBAAyB,IAAI,EAAE,cAAA,GAAiB,CAAC,CAAA;IACnD;IAGA,IAAA,8NAAA,EAAU,MAAM;QACd,cAAc;QACd,OAAO;IACT,GAAG,CAAC,CAAC;IAGL,IAAA,8NAAA,EAAU,MAAM;QACd,IAAI,CAAC,WAAW;YACd,cAAc;QAChB;IACF,GAAG;QAAC;QAAU,SAAS;KAAC;IAExB,WAAO,gOAAA,EAAgC,CAAC,IAAI,aAAa;QACvD,OAAO,yBAAyB,IAAI,EAAE,GAAA,CAAI,IAAI,QAAQ;IAGxD,GAAG,CAAC,CAAC;AACP", "debugId": null}}, {"offset": {"line": 1000, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/app-router/client/useAwaitablePush.ts"], "sourcesContent": ["'use client';\n\nimport { useRouter } from 'next/navigation';\n\nimport { useInternalNavFun } from './useInternalNavFun';\n\n/**\n * Creates an \"awaitable\" navigation function that will do its best effort to wait for Next.js to finish its route transition.\n * This is accomplished by wrapping the call to `router.push` in `startTransition()`, which should rely on <PERSON>act to coordinate the pending state. We key off of\n * `isPending` to flush the stored promises and ensure the navigates \"resolve\".\n */\nexport const useAwaitablePush = () => {\n  const router = useRouter();\n\n  return useInternalNavFun({\n    windowNav: typeof window !== 'undefined' ? window.history.pushState.bind(window.history) : undefined,\n    routerNav: router.push.bind(router),\n    name: 'push',\n  });\n};\n"], "names": [], "mappings": ";;;;AAEA,SAAS,iBAAiB;AAE1B,SAAS,yBAAyB;;;;;AAO3B,MAAM,mBAAmB,MAAM;IACpC,MAAM,aAAS,2JAAA,CAAU;IAEzB,WAAO,iOAAA,EAAkB;QACvB,WAAW,OAAO,WAAW,oBAAc,OAAO,QAAQ,UAAU,CAAuB,IAAlB,CAAkB,MAAX,OAAO;QACvF,WAAW,OAAO,IAAA,CAAK,IAAA,CAAK,MAAM;QAClC,MAAM;IACR,CAAC;AACH", "debugId": null}}, {"offset": {"line": 1024, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/app-router/client/useAwaitableReplace.ts"], "sourcesContent": ["'use client';\n\nimport { useRouter } from 'next/navigation';\n\nimport { useInternalNavFun } from './useInternalNavFun';\n\n/**\n * Creates an \"awaitable\" navigation function that will do its best effort to wait for Next.js to finish its route transition.\n * This is accomplished by wrapping the call to `router.replace` in `startTransition()`, which should rely on <PERSON>act to coordinate the pending state. We key off of\n * `isPending` to flush the stored promises and ensure the navigates \"resolve\".\n */\nexport const useAwaitableReplace = () => {\n  const router = useRouter();\n\n  return useInternalNavFun({\n    windowNav: typeof window !== 'undefined' ? window.history.replaceState.bind(window.history) : undefined,\n    routerNav: router.replace.bind(router),\n    name: 'replace',\n  });\n};\n"], "names": [], "mappings": ";;;;AAEA,SAAS,iBAAiB;AAE1B,SAAS,yBAAyB;;;;;AAO3B,MAAM,sBAAsB,MAAM;IACvC,MAAM,aAAS,2JAAA,CAAU;IAEzB,WAAO,iOAAA,EAAkB;QACvB,WAAW,OAAO,WAAW,oBAAc,OAAO,QAAQ,WAAoC,EAAvB,GAAuB,EAAlB,OAAO,OAAO;QAC1F,WAAW,OAAO,OAAA,CAAQ,IAAA,CAAK,MAAM;QACrC,MAAM;IACR,CAAC;AACH", "debugId": null}}, {"offset": {"line": 1048, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/app-router/client/ClerkProvider.tsx"], "sourcesContent": ["'use client';\nimport { <PERSON><PERSON><PERSON><PERSON> as ReactClerkProvider } from '@clerk/clerk-react';\nimport { inBrowser } from '@clerk/shared/browser';\nimport { logger } from '@clerk/shared/logger';\nimport dynamic from 'next/dynamic';\nimport { useRouter } from 'next/navigation';\nimport nextPackage from 'next/package.json';\nimport React, { useEffect, useTransition } from 'react';\n\nimport { useSafeLayoutEffect } from '../../client-boundary/hooks/useSafeLayoutEffect';\nimport { ClerkNextOptionsProvider, useClerkNextOptions } from '../../client-boundary/NextOptionsContext';\nimport type { NextClerkProviderProps } from '../../types';\nimport { ClerkJSScript } from '../../utils/clerk-js-script';\nimport { canUseKeyless } from '../../utils/feature-flags';\nimport { mergeNextClerkPropsWithEnv } from '../../utils/mergeNextClerkPropsWithEnv';\nimport { RouterTelemetry } from '../../utils/router-telemetry';\nimport { isNextWithUnstableServerActions } from '../../utils/sdk-versions';\nimport { detectKeylessEnvDriftAction } from '../keyless-actions';\nimport { invalidateCacheAction } from '../server-actions';\nimport { useAwaitablePush } from './useAwaitablePush';\nimport { useAwaitableReplace } from './useAwaitableReplace';\n\n/**\n * LazyCreateKeylessApplication should only be loaded if the conditions below are met.\n * Note: Using lazy() with Suspense instead of dynamic is not possible as React will throw a hydration error when `ClerkProvider` wraps `<html><body>...`\n */\nconst LazyCreateKeylessApplication = dynamic(() =>\n  import('./keyless-creator-reader.js').then(m => m.KeylessCreatorOrReader),\n);\n\nconst NextClientClerkProvider = (props: NextClerkProviderProps) => {\n  if (isNextWithUnstableServerActions) {\n    const deprecationWarning = `Clerk:\\nYour current Next.js version (${nextPackage.version}) will be deprecated in the next major release of \"@clerk/nextjs\". Please upgrade to next@14.1.0 or later.`;\n    if (inBrowser()) {\n      logger.warnOnce(deprecationWarning);\n    } else {\n      logger.logOnce(`\\n\\x1b[43m----------\\n${deprecationWarning}\\n----------\\x1b[0m\\n`);\n    }\n  }\n\n  const { __unstable_invokeMiddlewareOnAuthStateChange = true, children } = props;\n  const router = useRouter();\n  const push = useAwaitablePush();\n  const replace = useAwaitableReplace();\n  const [isPending, startTransition] = useTransition();\n\n  // Call drift detection on mount (client-side)\n  useSafeLayoutEffect(() => {\n    if (canUseKeyless) {\n      void detectKeylessEnvDriftAction();\n    }\n  }, []);\n\n  // Avoid rendering nested ClerkProviders by checking for the existence of the ClerkNextOptions context provider\n  const isNested = Boolean(useClerkNextOptions());\n  if (isNested) {\n    return props.children;\n  }\n\n  useEffect(() => {\n    if (!isPending) {\n      window.__clerk_internal_invalidateCachePromise?.();\n    }\n  }, [isPending]);\n\n  useSafeLayoutEffect(() => {\n    window.__unstable__onBeforeSetActive = intent => {\n      /**\n       * We need to invalidate the cache in case the user is navigating to a page that\n       * was previously cached using the auth state that was active at the time.\n       *\n       *  We also need to await for the invalidation to happen before we navigate,\n       * otherwise the navigation will use the cached page.\n       *\n       * For example, if we did not invalidate the flow, the following scenario would be broken:\n       * - The middleware is configured in such a way that it redirects you back to the same page if a certain condition is true (eg, you need to pick an org)\n       * - The user has a <Link href=/> component in the page\n       * - The UB is mounted with afterSignOutUrl=/\n       * - The user clicks the Link. A nav to / happens, a 307 to the current page is returned so a navigation does not take place. The / navigation is now cached as a 307 to the current page\n       * - The user clicks sign out\n       * - We call router.refresh()\n       * - We navigate to / but its cached and instead, we 'redirect' to the current page\n       *\n       *  For more information on cache invalidation, see:\n       * https://nextjs.org/docs/app/building-your-application/caching#invalidation-1\n       */\n      return new Promise(resolve => {\n        window.__clerk_internal_invalidateCachePromise = resolve;\n\n        const nextVersion = window?.next?.version || '';\n\n        // ATTENTION: Avoid using wrapping code with `startTransition` on versions >= 14\n        // otherwise the fetcher of `useReverification()` will be pending indefinitely when called within `startTransition`.\n        if (nextVersion.startsWith('13')) {\n          startTransition(() => {\n            router.refresh();\n          });\n        }\n        // On Next.js v15 calling a server action that returns a 404 error when deployed on Vercel is prohibited, failing with 405 status code.\n        // When a user transitions from \"signed in\" to \"singed out\", we clear the `__session` cookie, then we call `__unstable__onBeforeSetActive`.\n        // If we were to call `invalidateCacheAction` while the user is already signed out (deleted cookie), any page protected by `auth.protect()`\n        // will result to the server action returning a 404 error (this happens because server actions inherit the protection rules of the page they are called from).\n        // SOLUTION:\n        // To mitigate this, since the router cache on version 15 is much less aggressive, we can treat this as a noop and simply resolve the promise.\n        // Once `setActive` performs the navigation, `__unstable__onAfterSetActive` will kick in and perform a router.refresh ensuring shared layouts will also update with the correct authentication context.\n        else if (nextVersion.startsWith('15') && intent === 'sign-out') {\n          resolve(); // noop\n        } else {\n          void invalidateCacheAction().then(() => resolve());\n        }\n      });\n    };\n\n    window.__unstable__onAfterSetActive = () => {\n      if (__unstable_invokeMiddlewareOnAuthStateChange) {\n        return router.refresh();\n      }\n    };\n  }, []);\n\n  const mergedProps = mergeNextClerkPropsWithEnv({\n    ...props,\n    // @ts-expect-error Error because of the stricter types of internal `push`\n    routerPush: push,\n    // @ts-expect-error Error because of the stricter types of internal `replace`\n    routerReplace: replace,\n  });\n\n  return (\n    <ClerkNextOptionsProvider options={mergedProps}>\n      <ReactClerkProvider {...mergedProps}>\n        <RouterTelemetry />\n        <ClerkJSScript router='app' />\n        {children}\n      </ReactClerkProvider>\n    </ClerkNextOptionsProvider>\n  );\n};\n\nexport const ClientClerkProvider = (props: NextClerkProviderProps & { disableKeyless?: boolean }) => {\n  const { children, disableKeyless = false, ...rest } = props;\n  const safePublishableKey = mergeNextClerkPropsWithEnv(rest).publishableKey;\n\n  if (safePublishableKey || !canUseKeyless || disableKeyless) {\n    return <NextClientClerkProvider {...rest}>{children}</NextClientClerkProvider>;\n  }\n\n  return (\n    <LazyCreateKeylessApplication>\n      <NextClientClerkProvider {...rest}>{children}</NextClientClerkProvider>\n    </LazyCreateKeylessApplication>\n  );\n};\n"], "names": [], "mappings": ";;;;AACA,SAAS,iBAAiB,0BAA0B;AACpD,SAAS,iBAAiB;;AAC1B,SAAS,cAAc;;AACvB,OAAO,aAAa;AACpB,SAAS,iBAAiB;AAC1B,OAAO,iBAAiB;AACxB,OAAO,SAAS,WAAW,qBAAqB;AAEhD,SAAS,2BAA2B;AACpC,SAAS,0BAA0B,2BAA2B;AAE9D,SAAS,qBAAqB;AAC9B,SAAS,qBAAqB;AAC9B,SAAS,kCAAkC;AAC3C,SAAS,uBAAuB;AAChC,SAAS,uCAAuC;AAChD,SAAS,mCAAmC;AAC5C,SAAS,6BAA6B;AACtC,SAAS,wBAAwB;AACjC,SAAS,2BAA2B;;;;;;;;;;;;;;;;;;;;;AAMpC,MAAM,mCAA+B,sLAAA,EAAQ,IAC3C,OAAO,6BAA6B,+HAAE,IAAA,CAAK,CAAA,IAAK,EAAE,sBAAsB;AAG1E,MAAM,0BAA0B,CAAC,UAAkC;IACjE,IAAI,2NAAA,EAAiC;QACnC,MAAM,qBAAqB,CAAA;8BAAA,EAAyC,8HAAA,CAAY,OAAO,CAAA,0GAAA,CAAA;QACvF,QAAI,wLAAA,CAAU,IAAG;YACf,qLAAA,CAAO,QAAA,CAAS,kBAAkB;QACpC,OAAO;YACL,qLAAA,CAAO,OAAA,CAAQ,CAAA;;AAAA,EAAyB,kBAAkB,CAAA;;AAAA,CAAuB;QACnF;IACF;IAEA,MAAM,EAAE,+CAA+C,IAAA,EAAM,QAAA,CAAS,CAAA,GAAI;IAC1E,MAAM,aAAS,2JAAA,CAAU;IACzB,MAAM,WAAO,+NAAA,CAAiB;IAC9B,MAAM,cAAU,qOAAA,CAAoB;IACpC,MAAM,CAAC,WAAW,eAAe,CAAA,OAAI,kOAAA,CAAc;IAGnD,IAAA,yOAAA,EAAoB,MAAM;QACxB,IAAI,0MAAA,EAAe;YACjB,SAAK,oPAAA,CAA4B;QACnC;IACF,GAAG,CAAC,CAAC;IAGL,MAAM,WAAW,YAAQ,+NAAA,CAAoB,CAAC;IAC9C,IAAI,UAAU;QACZ,OAAO,MAAM,QAAA;IACf;IAEA,IAAA,8NAAA,EAAU,MAAM;QA3DlB,IAAA;QA4DI,IAAI,CAAC,WAAW;YACd,CAAA,KAAA,OAAO,uCAAA,KAAP,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA;QACF;IACF,GAAG;QAAC,SAAS;KAAC;IAEd,IAAA,yOAAA,EAAoB,MAAM;QACxB,OAAO,6BAAA,GAAgC,CAAA,WAAU;YAoB/C,OAAO,IAAI,QAAQ,CAAA,YAAW;gBAtFpC,IAAA;gBAuFQ,OAAO,uCAAA,GAA0C;gBAEjD,MAAM,cAAA,CAAA,CAAc,KAAA,UAAA,OAAA,KAAA,IAAA,OAAQ,IAAA,KAAR,OAAA,KAAA,IAAA,GAAc,OAAA,KAAW;gBAI7C,IAAI,YAAY,UAAA,CAAW,IAAI,GAAG;oBAChC,gBAAgB,MAAM;wBACpB,OAAO,OAAA,CAAQ;oBACjB,CAAC;gBACH,OAAA,IAQS,YAAY,UAAA,CAAW,IAAI,KAAK,WAAW,YAAY;oBAC9D,QAAQ;gBACV,OAAO;oBACL,SAAK,8OAAA,CAAsB,GAAE,IAAA,CAAK,IAAM,QAAQ,CAAC;gBACnD;YACF,CAAC;QACH;QAEA,OAAO,4BAAA,GAA+B,MAAM;YAC1C,IAAI,8CAA8C;gBAChD,OAAO,OAAO,OAAA,CAAQ;YACxB;QACF;IACF,GAAG,CAAC,CAAC;IAEL,MAAM,kBAAc,iOAAA,EAA2B;QAC7C,GAAG,KAAA;QAAA,0EAAA;QAEH,YAAY;QAAA,6EAAA;QAEZ,eAAe;IACjB,CAAC;IAED,OACE,aAAA,GAAA,4NAAA,CAAA,aAAA,CAAC,oOAAA,EAAA;QAAyB,SAAS;IAAA,GACjC,aAAA,GAAA,4NAAA,CAAA,aAAA,CAAC,wMAAA,EAAA;QAAoB,GAAG,WAAA;IAAA,GACtB,aAAA,GAAA,4NAAA,CAAA,aAAA,CAAC,+MAAA,EAAA,IAAgB,GACjB,aAAA,GAAA,4NAAA,CAAA,aAAA,CAAC,+MAAA,EAAA;QAAc,QAAO;IAAA,CAAM,GAC3B,QACH,CACF;AAEJ;AAEO,MAAM,sBAAsB,CAAC,UAAiE;IACnG,MAAM,EAAE,QAAA,EAAU,iBAAiB,KAAA,EAAO,GAAG,KAAK,CAAA,GAAI;IACtD,MAAM,yBAAqB,iOAAA,EAA2B,IAAI,EAAE,cAAA;IAE5D,IAAI,sBAAsB,CAAC,0MAAA,IAAiB,gBAAgB;QAC1D,OAAO,aAAA,GAAA,4NAAA,CAAA,aAAA,CAAC,yBAAA;YAAyB,GAAG,IAAA;QAAA,GAAO,QAAS;IACtD;IAEA,OACE,aAAA,GAAA,4NAAA,CAAA,aAAA,CAAC,8BAAA,MACC,aAAA,GAAA,4NAAA,CAAA,aAAA,CAAC,yBAAA;QAAyB,GAAG,IAAA;IAAA,GAAO,QAAS,CAC/C;AAEJ", "debugId": null}}, {"offset": {"line": 1185, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/app-router/client/keyless-cookie-sync.tsx"], "sourcesContent": ["'use client';\n\nimport type { AccountlessApplication } from '@clerk/backend';\nimport { useSelectedLayoutSegments } from 'next/navigation';\nimport type { PropsWithChildren } from 'react';\nimport { useEffect } from 'react';\n\nimport { canUseKeyless } from '../../utils/feature-flags';\n\nexport function KeylessCookieSync(props: PropsWithChildren<AccountlessApplication>) {\n  const segments = useSelectedLayoutSegments();\n  const isNotFoundRoute = segments[0]?.startsWith('/_not-found') || false;\n\n  useEffect(() => {\n    if (canUseKeyless && !isNotFoundRoute) {\n      void import('../keyless-actions.js').then(m =>\n        m.syncKeylessConfigAction({\n          ...props,\n          // Preserve the current url and return back, once keys are synced in the middleware\n          returnUrl: window.location.href,\n        }),\n      );\n    }\n  }, [isNotFoundRoute]);\n\n  return props.children;\n}\n"], "names": [], "mappings": ";;;;AAGA,SAAS,iCAAiC;AAE1C,SAAS,iBAAiB;AAE1B,SAAS,qBAAqB;;;;;;AAEvB,SAAS,kBAAkB,KAAA,EAAkD;IATpF,IAAA;IAUE,MAAM,eAAW,2KAAA,CAA0B;IAC3C,MAAM,kBAAA,CAAA,CAAkB,KAAA,QAAA,CAAS,CAAC,CAAA,KAAV,OAAA,KAAA,IAAA,GAAa,UAAA,CAAW,cAAA,KAAkB;IAElE,IAAA,8NAAA,EAAU,MAAM;QACd,IAAI,0MAAA,IAAiB,CAAC,iBAAiB;YACrC,KAAK,OAAO,uBAAuB,uHAAE,IAAA,CAAK,CAAA,IACxC,EAAE,uBAAA,CAAwB;oBACxB,GAAG,KAAA;oBAAA,mFAAA;oBAEH,WAAW,OAAO,QAAA,CAAS,IAAA;gBAC7B,CAAC;QAEL;IACF,GAAG;QAAC,eAAe;KAAC;IAEpB,OAAO,MAAM,QAAA;AACf", "debugId": null}}, {"offset": {"line": 1220, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40swc/helpers/cjs/_interop_require_default.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : { default: obj };\n}\nexports._ = _interop_require_default;\n"], "names": [], "mappings": "AAEA,SAAS,yBAAyB,GAAG;IACjC,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,SAAS;IAAI;AACxD;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1230, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40swc/helpers/cjs/_interop_require_wildcard.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interop_require_wildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) return obj;\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") return { default: obj };\n\n    var cache = _getRequireWildcardCache(nodeInterop);\n\n    if (cache && cache.has(obj)) return cache.get(obj);\n\n    var newObj = { __proto__: null };\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n\n    for (var key in obj) {\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) Object.defineProperty(newObj, key, desc);\n            else newObj[key] = obj[key];\n        }\n    }\n\n    newObj.default = obj;\n\n    if (cache) cache.set(obj, newObj);\n\n    return newObj;\n}\nexports._ = _interop_require_wildcard;\n"], "names": [], "mappings": "AAEA,SAAS,yBAAyB,WAAW;IACzC,IAAI,OAAO,YAAY,YAAY,OAAO;IAE1C,IAAI,oBAAoB,IAAI;IAC5B,IAAI,mBAAmB,IAAI;IAE3B,OAAO,CAAC,2BAA2B,SAAS,WAAW;QACnD,OAAO,cAAc,mBAAmB;IAC5C,CAAC,EAAE;AACP;AACA,SAAS,0BAA0B,GAAG,EAAE,WAAW;IAC/C,IAAI,CAAC,eAAe,OAAO,IAAI,UAAU,EAAE,OAAO;IAClD,IAAI,QAAQ,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,YAAY,OAAO;QAAE,SAAS;IAAI;IAEhG,IAAI,QAAQ,yBAAyB;IAErC,IAAI,SAAS,MAAM,GAAG,CAAC,MAAM,OAAO,MAAM,GAAG,CAAC;IAE9C,IAAI,SAAS;QAAE,WAAW;IAAK;IAC/B,IAAI,wBAAwB,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAEpF,IAAK,IAAI,OAAO,IAAK;QACjB,IAAI,QAAQ,aAAa,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,MAAM;YACrE,IAAI,OAAO,wBAAwB,OAAO,wBAAwB,CAAC,KAAK,OAAO;YAC/E,IAAI,QAAQ,CAAC,KAAK,GAAG,IAAI,KAAK,GAAG,GAAG,OAAO,cAAc,CAAC,QAAQ,KAAK;iBAClE,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;QAC/B;IACJ;IAEA,OAAO,OAAO,GAAG;IAEjB,IAAI,OAAO,MAAM,GAAG,CAAC,KAAK;IAE1B,OAAO;AACX;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}]}