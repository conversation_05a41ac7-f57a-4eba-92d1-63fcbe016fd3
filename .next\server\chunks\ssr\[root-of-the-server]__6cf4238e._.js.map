{"version": 3, "sources": [], "sections": [{"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/app/page.tsx"], "sourcesContent": ["import { SignedIn, SignedOut, SignInButton, UserButton } from '@clerk/nextjs'\nimport Link from 'next/link'\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      <nav className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between h-16\">\n            <div className=\"flex items-center\">\n              <h1 className=\"text-xl font-bold text-gray-900\">Messages App</h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <SignedOut>\n                <SignInButton mode=\"modal\">\n                  <button className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium\">\n                    Sign In\n                  </button>\n                </SignInButton>\n              </SignedOut>\n              <SignedIn>\n                <UserButton afterSignOutUrl=\"/\" />\n              </SignedIn>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      <main className=\"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8\">\n        <SignedOut>\n          <div className=\"text-center\">\n            <h2 className=\"text-4xl font-bold text-gray-900 mb-4\">\n              Welcome to Messages App\n            </h2>\n            <p className=\"text-xl text-gray-600 mb-8\">\n              Connect with friends, send messages, and manage your social network\n            </p>\n            <div className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto\">\n                <div className=\"bg-white p-6 rounded-lg shadow-md\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Search Users</h3>\n                  <p className=\"text-gray-600\">Find and connect with other users by searching their usernames</p>\n                </div>\n                <div className=\"bg-white p-6 rounded-lg shadow-md\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Send Messages</h3>\n                  <p className=\"text-gray-600\">Chat with your friends in real-time messaging</p>\n                </div>\n                <div className=\"bg-white p-6 rounded-lg shadow-md\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Friend Requests</h3>\n                  <p className=\"text-gray-600\">Send and manage friend requests to build your network</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </SignedOut>\n\n        <SignedIn>\n          <div className=\"text-center\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-8\">\n              Welcome back!\n            </h2>\n            <Link\n              href=\"/dashboard\"\n              className=\"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md text-lg font-medium inline-block\"\n            >\n              Go to Dashboard\n            </Link>\n          </div>\n        </SignedIn>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,0PAAC;QAAI,WAAU;;0BACb,0PAAC;gBAAI,WAAU;0BACb,cAAA,0PAAC;oBAAI,WAAU;8BACb,cAAA,0PAAC;wBAAI,WAAU;;0CACb,0PAAC;gCAAI,WAAU;0CACb,cAAA,0PAAC;oCAAG,WAAU;8CAAkC;;;;;;;;;;;0CAElD,0PAAC;gCAAI,WAAU;;kDACb,0PAAC,kMAAS;kDACR,cAAA,0PAAC,kNAAY;4CAAC,MAAK;sDACjB,cAAA,0PAAC;gDAAO,WAAU;0DAAoF;;;;;;;;;;;;;;;;kDAK1G,0PAAC,iMAAQ;kDACP,cAAA,0PAAC,gNAAU;4CAAC,iBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOtC,0PAAC;gBAAK,WAAU;;kCACd,0PAAC,kMAAS;kCACR,cAAA,0PAAC;4BAAI,WAAU;;8CACb,0PAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,0PAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAG1C,0PAAC;oCAAI,WAAU;8CACb,cAAA,0PAAC;wCAAI,WAAU;;0DACb,0PAAC;gDAAI,WAAU;;kEACb,0PAAC;wDAAG,WAAU;kEAA2C;;;;;;kEACzD,0PAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;0DAE/B,0PAAC;gDAAI,WAAU;;kEACb,0PAAC;wDAAG,WAAU;kEAA2C;;;;;;kEACzD,0PAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;0DAE/B,0PAAC;gDAAI,WAAU;;kEACb,0PAAC;wDAAG,WAAU;kEAA2C;;;;;;kEACzD,0PAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOvC,0PAAC,iMAAQ;kCACP,cAAA,0PAAC;4BAAI,WAAU;;8CACb,0PAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,0PAAC,mLAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}]}