{"version": 3, "sources": ["../../src/webhooks.ts"], "sourcesContent": ["/* eslint-disable import/export */\nimport type { VerifyWebhookOptions } from '@clerk/backend/webhooks';\nimport { verifyWebhook as verifyWebhookBase } from '@clerk/backend/webhooks';\n\nimport { getHeader, isNextRequest, isRequestWebAPI } from './server/headers-utils';\nimport type { RequestLike } from './server/types';\n// Ordering of exports matter here since\n// we're overriding the base verifyWebhook\nexport * from '@clerk/backend/webhooks';\n\nconst SVIX_ID_HEADER = 'svix-id';\nconst SVIX_TIMESTAMP_HEADER = 'svix-timestamp';\nconst SVIX_SIGNATURE_HEADER = 'svix-signature';\n\n/**\n * Verifies the authenticity of a webhook request using Svix.\n *\n * @param request - The incoming webhook request object\n * @param options - Optional configuration object\n * @param options.signingSecret - Custom signing secret. If not provided, falls back to CLERK_WEBHOOK_SIGNING_SECRET env variable\n * @throws Will throw an error if the webhook signature verification fails\n * @returns A promise that resolves to the verified webhook event data\n *\n * @example\n * ```typescript\n * import { verifyWebhook } from '@clerk/nextjs/webhooks';\n *\n * export async function POST(req: Request) {\n *   try {\n *     const evt = await verifyWebhook(req);\n *\n *     // Access the event data\n *     const { id } = evt.data;\n *     const eventType = evt.type;\n *\n *     // Handle specific event types\n *     if (evt.type === 'user.created') {\n *       console.log('New user created:', evt.data.id);\n *       // Handle user creation\n *     }\n *\n *     return new Response('Success', { status: 200 });\n *   } catch (err) {\n *     console.error('Webhook verification failed:', err);\n *     return new Response('Webhook verification failed', { status: 400 });\n *   }\n * }\n * ```\n */\nexport async function verifyWebhook(request: RequestLike, options?: VerifyWebhookOptions) {\n  if (isNextRequest(request) || isRequestWebAPI(request)) {\n    return verifyWebhookBase(request, options);\n  }\n\n  const webRequest = nextApiRequestToWebRequest(request);\n  return verifyWebhookBase(webRequest, options);\n}\n\nfunction nextApiRequestToWebRequest(req: RequestLike): Request {\n  const headers = new Headers();\n  const svixId = getHeader(req, SVIX_ID_HEADER) || '';\n  const svixTimestamp = getHeader(req, SVIX_TIMESTAMP_HEADER) || '';\n  const svixSignature = getHeader(req, SVIX_SIGNATURE_HEADER) || '';\n\n  headers.set(SVIX_ID_HEADER, svixId);\n  headers.set(SVIX_TIMESTAMP_HEADER, svixTimestamp);\n  headers.set(SVIX_SIGNATURE_HEADER, svixSignature);\n\n  // Create a dummy URL to make a Request object\n  const protocol = getHeader(req, 'x-forwarded-proto') || 'http';\n  const host = getHeader(req, 'x-forwarded-host') || 'clerk-dummy';\n  const dummyOriginReqUrl = new URL(req.url || '', `${protocol}://${host}`);\n\n  const body = 'body' in req && req.body ? JSON.stringify(req.body) : undefined;\n\n  return new Request(dummyOriginReqUrl, {\n    method: req.method,\n    headers,\n    body,\n  });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,sBAAmD;AAEnD,2BAA0D;AAI1D,6BAAc,oCARd;AAUA,MAAM,iBAAiB;AACvB,MAAM,wBAAwB;AAC9B,MAAM,wBAAwB;AAqC9B,eAAsB,cAAc,SAAsB,SAAgC;AACxF,UAAI,oCAAc,OAAO,SAAK,sCAAgB,OAAO,GAAG;AACtD,eAAO,gBAAAA,eAAkB,SAAS,OAAO;AAAA,EAC3C;AAEA,QAAM,aAAa,2BAA2B,OAAO;AACrD,aAAO,gBAAAA,eAAkB,YAAY,OAAO;AAC9C;AAEA,SAAS,2BAA2B,KAA2B;AAC7D,QAAM,UAAU,IAAI,QAAQ;AAC5B,QAAM,aAAS,gCAAU,KAAK,cAAc,KAAK;AACjD,QAAM,oBAAgB,gCAAU,KAAK,qBAAqB,KAAK;AAC/D,QAAM,oBAAgB,gCAAU,KAAK,qBAAqB,KAAK;AAE/D,UAAQ,IAAI,gBAAgB,MAAM;AAClC,UAAQ,IAAI,uBAAuB,aAAa;AAChD,UAAQ,IAAI,uBAAuB,aAAa;AAGhD,QAAM,eAAW,gCAAU,KAAK,mBAAmB,KAAK;AACxD,QAAM,WAAO,gCAAU,KAAK,kBAAkB,KAAK;AACnD,QAAM,oBAAoB,IAAI,IAAI,IAAI,OAAO,IAAI,GAAG,QAAQ,MAAM,IAAI,EAAE;AAExE,QAAM,OAAO,UAAU,OAAO,IAAI,OAAO,KAAK,UAAU,IAAI,IAAI,IAAI;AAEpE,SAAO,IAAI,QAAQ,mBAAmB;AAAA,IACpC,QAAQ,IAAI;AAAA,IACZ;AAAA,IACA;AAAA,EACF,CAAC;AACH;", "names": ["verifyWebhookBase"]}