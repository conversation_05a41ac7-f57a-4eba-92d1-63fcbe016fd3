'use client'

import { useState, useEffect } from 'react'
import { User } from '@/lib/types'

interface UserDisplayProps {
  userId: string
  children: (user: User | null, isLoading: boolean) => React.ReactNode
}

export default function UserDisplay({ userId, children }: UserDisplayProps) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const loadUser = async () => {
      try {
        const response = await fetch(`/api/users/${userId}`)
        if (response.ok) {
          const data = await response.json()
          setUser(data.user)
        }
      } catch (error) {
        console.error('Error loading user:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadUser()
  }, [userId])

  return <>{children(user, isLoading)}</>
}
