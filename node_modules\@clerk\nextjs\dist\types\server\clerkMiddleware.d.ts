import type { <PERSON><PERSON><PERSON> } from '@clerk/backend';
import type { AuthenticateRequestOptions, ClerkRequest, RedirectFun, SignedInAuthObject, SignedOutAuthObject } from '@clerk/backend/internal';
import type { NextMiddleware, NextRequest } from 'next/server';
import type { AuthFn } from '../app-router/server/auth';
import { type ContentSecurityPolicyOptions } from './content-security-policy';
import type { NextMiddlewareEvtParam, NextMiddlewareRequestParam, NextMiddlewareReturn } from './types';
export type ClerkMiddlewareSessionAuthObject = (SignedInAuthObject | SignedOutAuthObject) & {
    redirectToSignIn: RedirectFun<Response>;
    redirectToSignUp: RedirectFun<Response>;
};
/**
 * @deprecated Use `ClerkMiddlewareSessionAuthObject` instead.
 */
export type ClerkMiddlewareAuthObject = ClerkMiddlewareSessionAuthObject;
export type ClerkMiddlewareAuth = AuthFn<Response>;
type ClerkMiddlewareHandler = (auth: ClerkMiddlewareAuth, request: NextMiddlewareRequestParam, event: NextMiddlewareEvtParam) => NextMiddlewareReturn;
type AuthenticateAnyRequestOptions = Omit<AuthenticateRequestOptions, 'acceptsToken'>;
/**
 * The `clerkMiddleware()` function accepts an optional object. The following options are available.
 * @interface
 */
export interface ClerkMiddlewareOptions extends AuthenticateAnyRequestOptions {
    /**
     * If true, additional debug information will be logged to the console.
     */
    debug?: boolean;
    /**
     * When set, automatically injects a Content-Security-Policy header(s) compatible with Clerk.
     */
    contentSecurityPolicy?: ContentSecurityPolicyOptions;
}
type ClerkMiddlewareOptionsCallback = (req: NextRequest) => ClerkMiddlewareOptions | Promise<ClerkMiddlewareOptions>;
/**
 * Middleware for Next.js that handles authentication and authorization with Clerk.
 * For more details, please refer to the docs: https://clerk.com/docs/references/nextjs/clerk-middleware
 */
interface ClerkMiddleware {
    /**
     * @example
     * export default clerkMiddleware((auth, request, event) => { ... }, options);
     */
    (handler: ClerkMiddlewareHandler, options?: ClerkMiddlewareOptions): NextMiddleware;
    /**
     * @example
     * export default clerkMiddleware((auth, request, event) => { ... }, (req) => options);
     */
    (handler: ClerkMiddlewareHandler, options?: ClerkMiddlewareOptionsCallback): NextMiddleware;
    /**
     * @example
     * export default clerkMiddleware(options);
     */
    (options?: ClerkMiddlewareOptions): NextMiddleware;
    /**
     * @example
     * export default clerkMiddleware;
     */
    (request: NextMiddlewareRequestParam, event: NextMiddlewareEvtParam): NextMiddlewareReturn;
}
/**
 * The `clerkMiddleware()` helper integrates Clerk authentication into your Next.js application through Middleware. `clerkMiddleware()` is compatible with both the App and Pages routers.
 */
export declare const clerkMiddleware: ClerkMiddleware;
type AuthenticateRequest = Pick<ClerkClient, 'authenticateRequest'>['authenticateRequest'];
export declare const createAuthenticateRequestOptions: (clerkRequest: ClerkRequest, options: ClerkMiddlewareOptions) => Parameters<AuthenticateRequest>[1];
export {};
//# sourceMappingURL=clerkMiddleware.d.ts.map