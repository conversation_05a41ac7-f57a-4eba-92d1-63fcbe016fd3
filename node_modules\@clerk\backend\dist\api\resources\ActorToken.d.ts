import type { ActorTokenStatus } from './Enums';
import type { ActorTokenJSON } from './JSON';
export declare class ActorToken {
    readonly id: string;
    readonly status: ActorTokenStatus;
    readonly userId: string;
    readonly actor: Record<string, unknown> | null;
    readonly token: string | null | undefined;
    readonly url: string | null | undefined;
    readonly createdAt: number;
    readonly updatedAt: number;
    constructor(id: string, status: ActorTokenStatus, userId: string, actor: Record<string, unknown> | null, token: string | null | undefined, url: string | null | undefined, createdAt: number, updatedAt: number);
    static fromJSON(data: ActorTokenJSON): ActorToken;
}
//# sourceMappingURL=ActorToken.d.ts.map