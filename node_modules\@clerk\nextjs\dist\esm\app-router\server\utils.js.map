{"version": 3, "sources": ["../../../../src/app-router/server/utils.ts"], "sourcesContent": ["import { NextRequest } from 'next/server';\n\nexport const isPrerenderingBailout = (e: unknown) => {\n  if (!(e instanceof Error) || !('message' in e)) {\n    return false;\n  }\n\n  const { message } = e;\n\n  const lowerCaseInput = message.toLowerCase();\n  const dynamicServerUsage = lowerCaseInput.includes('dynamic server usage');\n  const bailOutPrerendering = lowerCaseInput.includes('this page needs to bail out of prerendering');\n\n  // note: new error message syntax introduced in next@14.1.1-canary.21\n  // but we still want to support older versions.\n  // https://github.com/vercel/next.js/pull/61332 (dynamic-rendering.ts:153)\n  const routeRegex = /Route .*? needs to bail out of prerendering at this point because it used .*?./;\n\n  return routeRegex.test(message) || dynamicServerUsage || bailOutPrerendering;\n};\n\nexport async function buildRequestLike(): Promise<NextRequest> {\n  try {\n    // Dynamically import next/headers, otherwise Next12 apps will break\n    // @ts-expect-error: Cannot find module 'next/headers' or its corresponding type declarations.ts(2307)\n    const { headers } = await import('next/headers');\n    const resolvedHeaders = await headers();\n    return new NextRequest('https://placeholder.com', { headers: resolvedHeaders });\n  } catch (e: any) {\n    // rethrow the error when react throws a prerendering bailout\n    // https://nextjs.org/docs/messages/ppr-caught-error\n    if (e && isPrerenderingBailout(e)) {\n      throw e;\n    }\n\n    throw new Error(\n      `Clerk: auth(), currentUser() and clerkClient(), are only supported in App Router (/app directory).\\nIf you're using /pages, try getAuth() instead.\\nOriginal error: ${e}`,\n    );\n  }\n}\n\n// Original source: https://github.com/vercel/next.js/blob/canary/packages/next/src/server/app-render/get-script-nonce-from-header.tsx\nexport function getScriptNonceFromHeader(cspHeaderValue: string): string | undefined {\n  const directives = cspHeaderValue\n    // Directives are split by ';'.\n    .split(';')\n    .map(directive => directive.trim());\n\n  // First try to find the directive for the 'script-src', otherwise try to\n  // fallback to the 'default-src'.\n  const directive =\n    directives.find(dir => dir.startsWith('script-src')) || directives.find(dir => dir.startsWith('default-src'));\n\n  // If no directive could be found, then we're done.\n  if (!directive) {\n    return;\n  }\n\n  // Extract the nonce from the directive\n  const nonce = directive\n    .split(' ')\n    // Remove the 'strict-src'/'default-src' string, this can't be the nonce.\n    .slice(1)\n    .map(source => source.trim())\n    // Find the first source with the 'nonce-' prefix.\n    .find(source => source.startsWith(\"'nonce-\") && source.length > 8 && source.endsWith(\"'\"))\n    // Grab the nonce by trimming the 'nonce-' prefix.\n    ?.slice(7, -1);\n\n  // If we couldn't find the nonce, then we're done.\n  if (!nonce) {\n    return;\n  }\n\n  // Don't accept the nonce value if it contains HTML escape characters.\n  // Technically, the spec requires a base64'd value, but this is just an\n  // extra layer.\n  if (/[&><\\u2028\\u2029]/g.test(nonce)) {\n    throw new Error(\n      'Nonce value from Content-Security-Policy contained invalid HTML escape characters, which is disallowed for security reasons. Make sure that your nonce value does not contain the following characters: `<`, `>`, `&`',\n    );\n  }\n\n  return nonce;\n}\n"], "mappings": ";AAAA,SAAS,mBAAmB;AAErB,MAAM,wBAAwB,CAAC,MAAe;AACnD,MAAI,EAAE,aAAa,UAAU,EAAE,aAAa,IAAI;AAC9C,WAAO;AAAA,EACT;AAEA,QAAM,EAAE,QAAQ,IAAI;AAEpB,QAAM,iBAAiB,QAAQ,YAAY;AAC3C,QAAM,qBAAqB,eAAe,SAAS,sBAAsB;AACzE,QAAM,sBAAsB,eAAe,SAAS,6CAA6C;AAKjG,QAAM,aAAa;AAEnB,SAAO,WAAW,KAAK,OAAO,KAAK,sBAAsB;AAC3D;AAEA,eAAsB,mBAAyC;AAC7D,MAAI;AAGF,UAAM,EAAE,QAAQ,IAAI,MAAM,OAAO,cAAc;AAC/C,UAAM,kBAAkB,MAAM,QAAQ;AACtC,WAAO,IAAI,YAAY,2BAA2B,EAAE,SAAS,gBAAgB,CAAC;AAAA,EAChF,SAAS,GAAQ;AAGf,QAAI,KAAK,sBAAsB,CAAC,GAAG;AACjC,YAAM;AAAA,IACR;AAEA,UAAM,IAAI;AAAA,MACR;AAAA;AAAA,kBAAuK,CAAC;AAAA,IAC1K;AAAA,EACF;AACF;AAGO,SAAS,yBAAyB,gBAA4C;AA1CrF;AA2CE,QAAM,aAAa,eAEhB,MAAM,GAAG,EACT,IAAI,CAAAA,eAAaA,WAAU,KAAK,CAAC;AAIpC,QAAM,YACJ,WAAW,KAAK,SAAO,IAAI,WAAW,YAAY,CAAC,KAAK,WAAW,KAAK,SAAO,IAAI,WAAW,aAAa,CAAC;AAG9G,MAAI,CAAC,WAAW;AACd;AAAA,EACF;AAGA,QAAM,SAAQ,eACX,MAAM,GAAG,EAET,MAAM,CAAC,EACP,IAAI,YAAU,OAAO,KAAK,CAAC,EAE3B,KAAK,YAAU,OAAO,WAAW,SAAS,KAAK,OAAO,SAAS,KAAK,OAAO,SAAS,GAAG,CAAC,MAN7E,mBAQV,MAAM,GAAG;AAGb,MAAI,CAAC,OAAO;AACV;AAAA,EACF;AAKA,MAAI,qBAAqB,KAAK,KAAK,GAAG;AACpC,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;", "names": ["directive"]}