import type { SignInToken } from '../resources/SignInTokens';
import { AbstractAPI } from './AbstractApi';
type CreateSignInTokensParams = {
    userId: string;
    expiresInSeconds: number;
};
export declare class SignInTokenAPI extends AbstractAPI {
    createSignInToken(params: CreateSignInTokensParams): Promise<SignInToken>;
    revokeSignInToken(signInTokenId: string): Promise<SignInToken>;
}
export {};
//# sourceMappingURL=SignInTokenApi.d.ts.map