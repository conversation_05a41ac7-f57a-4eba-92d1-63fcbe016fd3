module.exports = [
"[project]/messages/node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-creator-reader.js [app-ssr] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/65b60_@clerk_nextjs_dist_esm_app-router_107237a2._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/messages/node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-creator-reader.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/messages/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-ssr] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/65b60_@clerk_nextjs_dist_esm_app-router_cb475877._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/messages/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-ssr] (ecmascript)");
    });
});
}),
];