'use client'

import { useState, useEffect } from 'react'
import { User, FriendRequest } from '@/lib/types'

interface FriendRequestCardProps {
  request: FriendRequest
  users: User[]
  onResponse: (requestId: string, status: 'accepted' | 'rejected') => void
}

export default function FriendRequestCard({ request, users, onResponse }: FriendRequestCardProps) {
  const [sender, setSender] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const loadSender = async () => {
      try {
        const response = await fetch(`/api/users/${request.senderId}`)
        if (response.ok) {
          const data = await response.json()
          setSender(data.user)
        }
      } catch (error) {
        console.error('Error loading sender:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadSender()
  }, [request.senderId])

  if (isLoading) {
    return (
      <div className="border border-gray-200 rounded-lg p-4 bg-white">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-gray-200 rounded-full animate-pulse"></div>
          <div className="flex-1">
            <div className="h-4 bg-gray-200 rounded animate-pulse mb-2"></div>
            <div className="h-3 bg-gray-200 rounded animate-pulse w-1/2"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!sender) {
    return null
  }

  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)
    
    if (diffInHours < 1) {
      return 'Just now'
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)} hours ago`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`
    }
  }

  return (
    <div className="border border-gray-200 rounded-lg p-4 bg-white">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <img
            src={sender.imageUrl || `https://ui-avatars.com/api/?name=${sender.firstName}+${sender.lastName}&background=random`}
            alt={`${sender.firstName} ${sender.lastName}`}
            className="w-12 h-12 rounded-full object-cover"
          />
          
          <div>
            <h3 className="text-lg font-medium text-gray-900">
              {sender.firstName} {sender.lastName}
            </h3>
            <p className="text-sm text-gray-500">@{sender.username}</p>
            <p className="text-xs text-gray-400">{formatTime(request.timestamp)}</p>
          </div>
        </div>
        
        <div className="flex space-x-2">
          <button
            onClick={() => onResponse(request.id, 'accepted')}
            className="px-4 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700"
          >
            Accept
          </button>
          <button
            onClick={() => onResponse(request.id, 'rejected')}
            className="px-4 py-2 bg-red-600 text-white text-sm rounded-md hover:bg-red-700"
          >
            Decline
          </button>
        </div>
      </div>
    </div>
  )
}
