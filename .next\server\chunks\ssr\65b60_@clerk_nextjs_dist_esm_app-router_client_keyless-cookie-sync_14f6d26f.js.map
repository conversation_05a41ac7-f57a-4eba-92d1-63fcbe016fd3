{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const KeylessCookieSync = registerClientReference(\n    function() { throw new Error(\"Attempted to call KeylessCookieSync() from the server but KeylessCookieSync is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/messages/node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js <module evaluation>\",\n    \"KeylessCookieSync\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,oBAAoB,IAAA,oRAAuB,EACpD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,uHACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const KeylessCookieSync = registerClientReference(\n    function() { throw new Error(\"Attempted to call KeylessCookieSync() from the server but KeylessCookieSync is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/messages/node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js\",\n    \"KeylessCookieSync\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,oBAAoB,IAAA,oRAAuB,EACpD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,mGACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/app-router/client/keyless-cookie-sync.tsx"], "sourcesContent": ["'use client';\n\nimport type { AccountlessApplication } from '@clerk/backend';\nimport { useSelectedLayoutSegments } from 'next/navigation';\nimport type { PropsWithChildren } from 'react';\nimport { useEffect } from 'react';\n\nimport { canUseKeyless } from '../../utils/feature-flags';\n\nexport function KeylessCookieSync(props: PropsWithChildren<AccountlessApplication>) {\n  const segments = useSelectedLayoutSegments();\n  const isNotFoundRoute = segments[0]?.startsWith('/_not-found') || false;\n\n  useEffect(() => {\n    if (canUseKeyless && !isNotFoundRoute) {\n      void import('../keyless-actions.js').then(m =>\n        m.syncKeylessConfigAction({\n          ...props,\n          // Preserve the current url and return back, once keys are synced in the middleware\n          returnUrl: window.location.href,\n        }),\n      );\n    }\n  }, [isNotFoundRoute]);\n\n  return props.children;\n}\n"], "names": [], "mappings": "", "debugId": null}}]}