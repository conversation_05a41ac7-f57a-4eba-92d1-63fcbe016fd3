{"version": 3, "sources": ["../../../../src/client-boundary/hooks/useEnforceRoutingProps.tsx"], "sourcesContent": ["import { useRoutingProps } from '@clerk/clerk-react/internal';\nimport type { RoutingOptions } from '@clerk/types';\n\nimport { useEnforceCatchAllRoute } from './useEnforceCatchAllRoute';\nimport { usePathnameWithoutCatchAll } from './usePathnameWithoutCatchAll';\n\nexport function useEnforceCorrectRoutingProps<T extends RoutingOptions>(\n  componentName: string,\n  props: T,\n  requireSessionBeforeCheck = true,\n): T {\n  const path = usePathnameWithoutCatchAll();\n  const routingProps = useRoutingProps(componentName, props, { path });\n  useEnforceCatchAllRoute(componentName, path, routingProps.routing, requireSessionBeforeCheck);\n  return routingProps;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBAAgC;AAGhC,qCAAwC;AACxC,wCAA2C;AAEpC,SAAS,8BACd,eACA,OACA,4BAA4B,MACzB;AACH,QAAM,WAAO,8DAA2B;AACxC,QAAM,mBAAe,iCAAgB,eAAe,OAAO,EAAE,KAAK,CAAC;AACnE,8DAAwB,eAAe,MAAM,aAAa,SAAS,yBAAyB;AAC5F,SAAO;AACT;", "names": []}