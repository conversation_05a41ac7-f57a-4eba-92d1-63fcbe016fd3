{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/app/api/users/route.ts"], "sourcesContent": ["import { clerkClient } from '@clerk/nextjs/server'\nimport { auth } from '@clerk/nextjs/server'\nimport { NextResponse } from 'next/server'\n\nexport async function GET(request: Request) {\n  try {\n    const { userId } = await auth()\n\n    if (!userId) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const { searchParams } = new URL(request.url)\n    const query = searchParams.get('q') || ''\n\n    // Get all users from Clerk\n    const client = await clerkClient()\n    const users = await client.users.getUserList({\n      limit: 50,\n    })\n\n    // Filter users based on search query and exclude current user\n    const filteredUsers = users.data\n      .filter(user => user.id !== userId)\n      .filter(user => {\n        if (!query) return true\n        \n        const searchTerm = query.toLowerCase()\n        const username = user.username?.toLowerCase() || ''\n        const firstName = user.firstName?.toLowerCase() || ''\n        const lastName = user.lastName?.toLowerCase() || ''\n        const email = user.emailAddresses[0]?.emailAddress?.toLowerCase() || ''\n        \n        return username.includes(searchTerm) || \n               firstName.includes(searchTerm) || \n               lastName.includes(searchTerm) ||\n               email.includes(searchTerm)\n      })\n      .map(user => ({\n        id: user.id,\n        username: user.username || user.emailAddresses[0]?.emailAddress?.split('@')[0] || 'user',\n        firstName: user.firstName || '',\n        lastName: user.lastName || '',\n        imageUrl: user.imageUrl,\n        emailAddress: user.emailAddresses[0]?.emailAddress || ''\n      }))\n\n    return NextResponse.json({ users: filteredUsers })\n  } catch (error) {\n    console.error('Error fetching users:', error)\n    return NextResponse.json({ error: 'Failed to fetch users' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEO,eAAe,IAAI,OAAgB;IACxC,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAA,yMAAI;QAE7B,IAAI,CAAC,QAAQ;YACX,OAAO,4JAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,QAAQ,aAAa,GAAG,CAAC,QAAQ;QAEvC,2BAA2B;QAC3B,MAAM,SAAS,MAAM,IAAA,sMAAW;QAChC,MAAM,QAAQ,MAAM,OAAO,KAAK,CAAC,WAAW,CAAC;YAC3C,OAAO;QACT;QAEA,8DAA8D;QAC9D,MAAM,gBAAgB,MAAM,IAAI,CAC7B,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,QAC3B,MAAM,CAAC,CAAA;YACN,IAAI,CAAC,OAAO,OAAO;YAEnB,MAAM,aAAa,MAAM,WAAW;YACpC,MAAM,WAAW,KAAK,QAAQ,EAAE,iBAAiB;YACjD,MAAM,YAAY,KAAK,SAAS,EAAE,iBAAiB;YACnD,MAAM,WAAW,KAAK,QAAQ,EAAE,iBAAiB;YACjD,MAAM,QAAQ,KAAK,cAAc,CAAC,EAAE,EAAE,cAAc,iBAAiB;YAErE,OAAO,SAAS,QAAQ,CAAC,eAClB,UAAU,QAAQ,CAAC,eACnB,SAAS,QAAQ,CAAC,eAClB,MAAM,QAAQ,CAAC;QACxB,GACC,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACZ,IAAI,KAAK,EAAE;gBACX,UAAU,KAAK,QAAQ,IAAI,KAAK,cAAc,CAAC,EAAE,EAAE,cAAc,MAAM,IAAI,CAAC,EAAE,IAAI;gBAClF,WAAW,KAAK,SAAS,IAAI;gBAC7B,UAAU,KAAK,QAAQ,IAAI;gBAC3B,UAAU,KAAK,QAAQ;gBACvB,cAAc,KAAK,cAAc,CAAC,EAAE,EAAE,gBAAgB;YACxD,CAAC;QAEH,OAAO,4JAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAc;IAClD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,4JAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}]}