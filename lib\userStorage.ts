// Simple in-memory storage for demo purposes
// In production, you'd use a real database like PostgreSQL, MongoDB, etc.

interface RegisteredUser {
  id: string
  username: string
  firstName: string
  lastName: string
  imageUrl?: string
  emailAddress: string
}

class UserStorage {
  private users: RegisteredUser[] = [
    // Add some demo users for testing
    {
      id: 'demo_alice',
      username: 'alice_wonder',
      firstName: 'Alice',
      lastName: 'Wonder',
      imageUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
      emailAddress: '<EMAIL>'
    },
    {
      id: 'demo_bob',
      username: 'bob_builder',
      firstName: 'Bob',
      lastName: 'Builder',
      imageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
      emailAddress: '<EMAIL>'
    },
    {
      id: 'demo_charlie',
      username: 'charlie_brown',
      firstName: '<PERSON>',
      lastName: '<PERSON>',
      imageUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
      emailAddress: '<EMAIL>'
    }
  ]

  addUser(user: RegisteredUser) {
    const existingIndex = this.users.findIndex(u => u.id === user.id)
    if (existingIndex !== -1) {
      this.users[existingIndex] = user
    } else {
      this.users.push(user)
    }
  }

  getUser(id: string): RegisteredUser | undefined {
    return this.users.find(u => u.id === id)
  }

  getAllUsers(): RegisteredUser[] {
    return this.users
  }

  searchUsers(query: string, excludeUserId?: string): RegisteredUser[] {
    return this.users
      .filter(user => excludeUserId ? user.id !== excludeUserId : true)
      .filter(user => {
        if (!query) return true
        
        const searchTerm = query.toLowerCase()
        const username = user.username?.toLowerCase() || ''
        const firstName = user.firstName?.toLowerCase() || ''
        const lastName = user.lastName?.toLowerCase() || ''
        const email = user.emailAddress?.toLowerCase() || ''
        
        return username.includes(searchTerm) || 
               firstName.includes(searchTerm) || 
               lastName.includes(searchTerm) ||
               email.includes(searchTerm)
      })
  }
}

// Export a singleton instance
export const userStorage = new UserStorage()
export type { RegisteredUser }
