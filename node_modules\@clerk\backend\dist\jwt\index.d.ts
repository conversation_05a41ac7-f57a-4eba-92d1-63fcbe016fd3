export type { VerifyJwtOptions } from './verifyJwt';
export type { SignJwtOptions } from './signJwt';
export declare const verifyJwt: (token: string, options: import("./verifyJwt").VerifyJwtOptions) => Promise<NonNullable<import("@clerk/types").JwtPayload | undefined>>;
export declare const decodeJwt: (token: string) => import("@clerk/types").Jwt;
export declare const signJwt: (payload: Record<string, unknown>, key: string | JsonWebKey, options: import("./signJwt").SignJwtOptions) => Promise<string>;
export declare const hasValidSignature: (jwt: import("@clerk/types").Jwt, key: string | JsonWebKey) => Promise<NonNullable<boolean | undefined>>;
//# sourceMappingURL=index.d.ts.map