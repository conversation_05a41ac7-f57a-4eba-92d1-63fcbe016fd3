{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/app/api/friend-requests/route.ts"], "sourcesContent": ["import { auth } from '@clerk/nextjs/server'\nimport { NextResponse } from 'next/server'\n\n// In-memory storage for friend requests (in production, use a real database)\nlet friendRequests: any[] = []\n\nexport async function GET(request: Request) {\n  try {\n    const { userId } = await auth()\n    \n    if (!userId) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const { searchParams } = new URL(request.url)\n    const type = searchParams.get('type') // 'sent' or 'received'\n\n    let userRequests = friendRequests\n\n    if (type === 'sent') {\n      userRequests = friendRequests.filter(req => req.senderId === userId)\n    } else if (type === 'received') {\n      userRequests = friendRequests.filter(req => req.receiverId === userId)\n    } else {\n      // Get all requests involving the user\n      userRequests = friendRequests.filter(\n        req => req.senderId === userId || req.receiverId === userId\n      )\n    }\n\n    return NextResponse.json({ friendRequests: userRequests })\n  } catch (error) {\n    console.error('Error fetching friend requests:', error)\n    return NextResponse.json({ error: 'Failed to fetch friend requests' }, { status: 500 })\n  }\n}\n\nexport async function POST(request: Request) {\n  try {\n    const { userId } = await auth()\n    \n    if (!userId) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const { receiverId } = await request.json()\n\n    if (!receiverId) {\n      return NextResponse.json({ error: 'Missing receiverId' }, { status: 400 })\n    }\n\n    // Check if request already exists\n    const existingRequest = friendRequests.find(\n      req => req.senderId === userId && req.receiverId === receiverId && req.status === 'pending'\n    )\n\n    if (existingRequest) {\n      return NextResponse.json({ error: 'Friend request already sent' }, { status: 400 })\n    }\n\n    const newRequest = {\n      id: Date.now().toString(),\n      senderId: userId,\n      receiverId,\n      status: 'pending',\n      timestamp: Date.now()\n    }\n\n    friendRequests.push(newRequest)\n\n    return NextResponse.json({ friendRequest: newRequest })\n  } catch (error) {\n    console.error('Error sending friend request:', error)\n    return NextResponse.json({ error: 'Failed to send friend request' }, { status: 500 })\n  }\n}\n\nexport async function PATCH(request: Request) {\n  try {\n    const { userId } = await auth()\n    \n    if (!userId) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const { requestId, status } = await request.json()\n\n    if (!requestId || !status) {\n      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })\n    }\n\n    const requestIndex = friendRequests.findIndex(\n      req => req.id === requestId && req.receiverId === userId\n    )\n\n    if (requestIndex === -1) {\n      return NextResponse.json({ error: 'Friend request not found' }, { status: 404 })\n    }\n\n    friendRequests[requestIndex].status = status\n\n    return NextResponse.json({ friendRequest: friendRequests[requestIndex] })\n  } catch (error) {\n    console.error('Error updating friend request:', error)\n    return NextResponse.json({ error: 'Failed to update friend request' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEA,6EAA6E;AAC7E,IAAI,iBAAwB,EAAE;AAEvB,eAAe,IAAI,OAAgB;IACxC,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAA,yMAAI;QAE7B,IAAI,CAAC,QAAQ;YACX,OAAO,4JAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,aAAa,GAAG,CAAC,QAAQ,uBAAuB;;QAE7D,IAAI,eAAe;QAEnB,IAAI,SAAS,QAAQ;YACnB,eAAe,eAAe,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ,KAAK;QAC/D,OAAO,IAAI,SAAS,YAAY;YAC9B,eAAe,eAAe,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,KAAK;QACjE,OAAO;YACL,sCAAsC;YACtC,eAAe,eAAe,MAAM,CAClC,CAAA,MAAO,IAAI,QAAQ,KAAK,UAAU,IAAI,UAAU,KAAK;QAEzD;QAEA,OAAO,4JAAY,CAAC,IAAI,CAAC;YAAE,gBAAgB;QAAa;IAC1D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO,4JAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAkC,GAAG;YAAE,QAAQ;QAAI;IACvF;AACF;AAEO,eAAe,KAAK,OAAgB;IACzC,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAA,yMAAI;QAE7B,IAAI,CAAC,QAAQ;YACX,OAAO,4JAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEzC,IAAI,CAAC,YAAY;YACf,OAAO,4JAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAqB,GAAG;gBAAE,QAAQ;YAAI;QAC1E;QAEA,kCAAkC;QAClC,MAAM,kBAAkB,eAAe,IAAI,CACzC,CAAA,MAAO,IAAI,QAAQ,KAAK,UAAU,IAAI,UAAU,KAAK,cAAc,IAAI,MAAM,KAAK;QAGpF,IAAI,iBAAiB;YACnB,OAAO,4JAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA8B,GAAG;gBAAE,QAAQ;YAAI;QACnF;QAEA,MAAM,aAAa;YACjB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,UAAU;YACV;YACA,QAAQ;YACR,WAAW,KAAK,GAAG;QACrB;QAEA,eAAe,IAAI,CAAC;QAEpB,OAAO,4JAAY,CAAC,IAAI,CAAC;YAAE,eAAe;QAAW;IACvD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,4JAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAgC,GAAG;YAAE,QAAQ;QAAI;IACrF;AACF;AAEO,eAAe,MAAM,OAAgB;IAC1C,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAA,yMAAI;QAE7B,IAAI,CAAC,QAAQ;YACX,OAAO,4JAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEhD,IAAI,CAAC,aAAa,CAAC,QAAQ;YACzB,OAAO,4JAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA0B,GAAG;gBAAE,QAAQ;YAAI;QAC/E;QAEA,MAAM,eAAe,eAAe,SAAS,CAC3C,CAAA,MAAO,IAAI,EAAE,KAAK,aAAa,IAAI,UAAU,KAAK;QAGpD,IAAI,iBAAiB,CAAC,GAAG;YACvB,OAAO,4JAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA2B,GAAG;gBAAE,QAAQ;YAAI;QAChF;QAEA,cAAc,CAAC,aAAa,CAAC,MAAM,GAAG;QAEtC,OAAO,4JAAY,CAAC,IAAI,CAAC;YAAE,eAAe,cAAc,CAAC,aAAa;QAAC;IACzE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO,4JAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAkC,GAAG;YAAE,QAAQ;QAAI;IACvF;AACF", "debugId": null}}]}