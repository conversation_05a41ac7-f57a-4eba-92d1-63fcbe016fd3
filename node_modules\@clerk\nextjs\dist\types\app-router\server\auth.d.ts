import type { AuthObject, InvalidTokenAuthObject, MachineAuthObject, SessionAuthObject } from '@clerk/backend';
import type { AuthenticateRequestOptions, InferAuthObjectFromToken, InferAuthObjectFromTokenArray, RedirectFun, SessionTokenType } from '@clerk/backend/internal';
import { TokenType } from '@clerk/backend/internal';
import type { PendingSessionOptions } from '@clerk/types';
import { redirect } from 'next/navigation';
import type { AuthProtect } from '../../server/protect';
/**
 * `Auth` object of the currently active user and the `redirectToSignIn()` method.
 */
type SessionAuthWithRedirect<TRedirect> = SessionAuthObject & {
    /**
     * The `auth()` helper returns the `redirectToSignIn()` method, which you can use to redirect the user to the sign-in page.
     *
     * @param [returnBackUrl] {string | URL} - The URL to redirect the user back to after they sign in.
     *
     * > [!NOTE]
     * > `auth()` on the server-side can only access redirect URLs defined via [environment variables](https://clerk.com/docs/deployments/clerk-environment-variables#sign-in-and-sign-up-redirects) or [`clerkMiddleware` dynamic keys](https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys).
     */
    redirectToSignIn: RedirectFun<TRedirect>;
    /**
     * The `auth()` helper returns the `redirectToSignUp()` method, which you can use to redirect the user to the sign-up page.
     *
     * @param [returnBackUrl] {string | URL} - The URL to redirect the user back to after they sign up.
     *
     * > [!NOTE]
     * > `auth()` on the server-side can only access redirect URLs defined via [environment variables](https://clerk.com/docs/deployments/clerk-environment-variables#sign-in-and-sign-up-redirects) or [`clerkMiddleware` dynamic keys](https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys).
     */
    redirectToSignUp: RedirectFun<TRedirect>;
};
export type AuthOptions = PendingSessionOptions & {
    acceptsToken?: AuthenticateRequestOptions['acceptsToken'];
};
export interface AuthFn<TRedirect = ReturnType<typeof redirect>> {
    /**
     * @example
     * const authObject = await auth({ acceptsToken: ['session_token', 'api_key'] })
     */
    <T extends TokenType[]>(options: AuthOptions & {
        acceptsToken: T;
    }): Promise<InferAuthObjectFromTokenArray<T, SessionAuthWithRedirect<TRedirect>, MachineAuthObject<Exclude<T[number], SessionTokenType>>> | InvalidTokenAuthObject>;
    /**
     * @example
     * const authObject = await auth({ acceptsToken: 'session_token' })
     */
    <T extends TokenType>(options: AuthOptions & {
        acceptsToken: T;
    }): Promise<InferAuthObjectFromToken<T, SessionAuthWithRedirect<TRedirect>, MachineAuthObject<Exclude<T, SessionTokenType>>>>;
    /**
     * @example
     * const authObject = await auth({ acceptsToken: 'any' })
     */
    (options: AuthOptions & {
        acceptsToken: 'any';
    }): Promise<AuthObject>;
    /**
     * @example
     * const authObject = await auth()
     */
    (options?: PendingSessionOptions): Promise<SessionAuthWithRedirect<TRedirect>>;
    /**
     * `auth` includes a single property, the `protect()` method, which you can use in two ways:
     * - to check if a user is authenticated (signed in)
     * - to check if a user is authorized (has the correct roles or permissions) to access something, such as a component or a route handler
     *
     * The following table describes how auth.protect() behaves based on user authentication or authorization status:
     *
     * | Authenticated | Authorized | `auth.protect()` will |
     * | - | - | - |
     * | Yes | Yes | Return the [`Auth`](https://clerk.com/docs/references/backend/types/auth-object) object. |
     * | Yes | No | Return a `404` error. |
     * | No | No | Redirect the user to the sign-in page\*. |
     *
     * > [!IMPORTANT]
     * > \*For non-document requests, such as API requests, `auth.protect()` returns a `404` error to users who aren't authenticated.
     *
     * `auth.protect()` can be used to check if a user is authenticated or authorized to access certain parts of your application or even entire routes. See detailed examples in the [dedicated guide](https://clerk.com/docs/organizations/verify-user-permissions).
     */
    protect: AuthProtect;
}
/**
 * The `auth()` helper returns the [`Auth`](https://clerk.com/docs/references/backend/types/auth-object) object of the currently active user, as well as the [`redirectToSignIn()`](https://clerk.com/docs/references/nextjs/auth#redirect-to-sign-in) method.
 *
 * - Only available for App Router.
 * - Only works on the server-side, such as in Server Components, Route Handlers, and Server Actions.
 * - Requires [`clerkMiddleware()`](https://clerk.com/docs/references/nextjs/clerk-middleware) to be configured.
 */
export declare const auth: AuthFn;
export {};
//# sourceMappingURL=auth.d.ts.map