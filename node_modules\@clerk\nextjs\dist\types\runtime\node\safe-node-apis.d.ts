export namespace fs {
    export { existsSync };
    export { writeFileSync };
    export { readFileSync };
    export { appendFileSync };
    export { mkdirSync };
    export { rmSync };
}
import path = require("path");
export function cwd(): string;
import { existsSync } from "fs";
import { writeFileSync } from "fs";
import { readFileSync } from "fs";
import { appendFileSync } from "fs";
import { mkdirSync } from "fs";
import { rmSync } from "fs";
export { path };
//# sourceMappingURL=safe-node-apis.d.ts.map