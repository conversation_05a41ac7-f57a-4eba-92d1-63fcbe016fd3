module.exports = [
"[project]/messages/.next-internal/server/app/api/users/[id]/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/node:crypto [external] (node:crypto, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("node:crypto", () => require("node:crypto"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[project]/messages/lib/userStorage.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

// Simple in-memory storage for demo purposes
// In production, you'd use a real database like PostgreSQL, MongoDB, etc.
__turbopack_context__.s([
    "userStorage",
    ()=>userStorage
]);
class UserStorage {
    users = [
        // Add some demo users for testing
        {
            id: 'demo_alice',
            username: 'alice_wonder',
            firstName: 'Alice',
            lastName: 'Wonder',
            imageUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
            emailAddress: '<EMAIL>'
        },
        {
            id: 'demo_bob',
            username: 'bob_builder',
            firstName: 'Bob',
            lastName: 'Builder',
            imageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
            emailAddress: '<EMAIL>'
        },
        {
            id: 'demo_charlie',
            username: 'charlie_brown',
            firstName: 'Charlie',
            lastName: 'Brown',
            imageUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
            emailAddress: '<EMAIL>'
        }
    ];
    addUser(user) {
        const existingIndex = this.users.findIndex((u)=>u.id === user.id);
        if (existingIndex !== -1) {
            this.users[existingIndex] = user;
        } else {
            this.users.push(user);
        }
    }
    getUser(id) {
        return this.users.find((u)=>u.id === id);
    }
    getAllUsers() {
        return this.users;
    }
    searchUsers(query, excludeUserId) {
        return this.users.filter((user)=>excludeUserId ? user.id !== excludeUserId : true).filter((user)=>{
            if (!query) return true;
            const searchTerm = query.toLowerCase();
            const username = user.username?.toLowerCase() || '';
            const firstName = user.firstName?.toLowerCase() || '';
            const lastName = user.lastName?.toLowerCase() || '';
            const email = user.emailAddress?.toLowerCase() || '';
            return username.includes(searchTerm) || firstName.includes(searchTerm) || lastName.includes(searchTerm) || email.includes(searchTerm);
        });
    }
}
const userStorage = new UserStorage();
}),
"[project]/messages/app/api/users/[id]/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "GET",
    ()=>GET
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$messages$2f$node_modules$2f40$clerk$2f$nextjs$2f$dist$2f$esm$2f$app$2d$router$2f$server$2f$auth$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/messages/node_modules/@clerk/nextjs/dist/esm/app-router/server/auth.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$messages$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/messages/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$messages$2f$lib$2f$userStorage$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/messages/lib/userStorage.ts [app-route] (ecmascript)");
;
;
;
async function GET(request, { params }) {
    try {
        const { userId } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$messages$2f$node_modules$2f40$clerk$2f$nextjs$2f$dist$2f$esm$2f$app$2d$router$2f$server$2f$auth$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["auth"])();
        if (!userId) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$messages$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Unauthorized'
            }, {
                status: 401
            });
        }
        const { id } = await params;
        const user = __TURBOPACK__imported__module__$5b$project$5d2f$messages$2f$lib$2f$userStorage$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userStorage"].getUser(id);
        if (!user) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$messages$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'User not found'
            }, {
                status: 404
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$messages$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            user
        });
    } catch (error) {
        console.error('Error fetching user:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$messages$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'User not found'
        }, {
            status: 404
        });
    }
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__6b925720._.js.map