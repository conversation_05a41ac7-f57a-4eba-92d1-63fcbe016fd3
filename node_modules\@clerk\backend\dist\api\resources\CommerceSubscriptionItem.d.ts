import { type CommerceMoneyAmount, CommercePlan } from './CommercePlan';
import type { CommerceSubscriptionItemJSON } from './JSON';
/**
 * @experimental This is an experimental API for the Billing feature that is available under a public beta, and the API is subject to change.
 * It is advised to pin the SDK version to avoid breaking changes.
 */
export declare class CommerceSubscriptionItem {
    /**
     * The unique identifier for the subscription item.
     */
    readonly id: string;
    /**
     * The status of the subscription item.
     */
    readonly status: CommerceSubscriptionItemJSON['status'];
    /**
     * The plan period for the subscription item.
     */
    readonly planPeriod: 'month' | 'annual';
    /**
     * The start of the current period.
     */
    readonly periodStart: number;
    /**
     * The next payment information.
     */
    readonly nextPayment: {
        amount: number;
        date: number;
    } | null;
    /**
     * The current amount for the subscription item.
     */
    readonly amount: CommerceMoneyAmount | null | undefined;
    /**
     * The plan associated with this subscription item.
     */
    readonly plan: CommercePlan;
    /**
     * The plan ID.
     */
    readonly planId: string;
    /**
     * The date and time the subscription item was created.
     */
    readonly createdAt: number;
    /**
     * The date and time the subscription item was last updated.
     */
    readonly updatedAt: number;
    /**
     * The end of the current period.
     */
    readonly periodEnd: number | null;
    /**
     * When the subscription item was canceled.
     */
    readonly canceledAt: number | null;
    /**
     * When the subscription item became past due.
     */
    readonly pastDueAt: number | null;
    /**
     * When the subscription item ended.
     */
    readonly endedAt: number | null;
    /**
     * The payer ID.
     */
    readonly payerId: string;
    /**
     * Whether this subscription item is currently in a free trial period.
     */
    readonly isFreeTrial?: boolean | undefined;
    /**
     * The lifetime amount paid for this subscription item.
     */
    readonly lifetimePaid?: (CommerceMoneyAmount | null) | undefined;
    constructor(
    /**
     * The unique identifier for the subscription item.
     */
    id: string, 
    /**
     * The status of the subscription item.
     */
    status: CommerceSubscriptionItemJSON['status'], 
    /**
     * The plan period for the subscription item.
     */
    planPeriod: 'month' | 'annual', 
    /**
     * The start of the current period.
     */
    periodStart: number, 
    /**
     * The next payment information.
     */
    nextPayment: {
        amount: number;
        date: number;
    } | null, 
    /**
     * The current amount for the subscription item.
     */
    amount: CommerceMoneyAmount | null | undefined, 
    /**
     * The plan associated with this subscription item.
     */
    plan: CommercePlan, 
    /**
     * The plan ID.
     */
    planId: string, 
    /**
     * The date and time the subscription item was created.
     */
    createdAt: number, 
    /**
     * The date and time the subscription item was last updated.
     */
    updatedAt: number, 
    /**
     * The end of the current period.
     */
    periodEnd: number | null, 
    /**
     * When the subscription item was canceled.
     */
    canceledAt: number | null, 
    /**
     * When the subscription item became past due.
     */
    pastDueAt: number | null, 
    /**
     * When the subscription item ended.
     */
    endedAt: number | null, 
    /**
     * The payer ID.
     */
    payerId: string, 
    /**
     * Whether this subscription item is currently in a free trial period.
     */
    isFreeTrial?: boolean | undefined, 
    /**
     * The lifetime amount paid for this subscription item.
     */
    lifetimePaid?: (CommerceMoneyAmount | null) | undefined);
    static fromJSON(data: CommerceSubscriptionItemJSON): CommerceSubscriptionItem;
}
//# sourceMappingURL=CommerceSubscriptionItem.d.ts.map