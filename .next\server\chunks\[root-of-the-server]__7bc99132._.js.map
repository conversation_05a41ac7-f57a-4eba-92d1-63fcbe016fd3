{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/app/api/messages/route.ts"], "sourcesContent": ["import { auth } from '@clerk/nextjs/server'\nimport { NextResponse } from 'next/server'\n\n// In-memory storage for messages (in production, use a real database)\nlet messages: any[] = []\n\nexport async function GET(request: Request) {\n  try {\n    const { userId } = await auth()\n    \n    if (!userId) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const { searchParams } = new URL(request.url)\n    const otherUserId = searchParams.get('otherUserId')\n\n    if (otherUserId) {\n      // Get conversation between two users\n      const conversationMessages = messages.filter(\n        msg => \n          (msg.senderId === userId && msg.receiverId === otherUserId) ||\n          (msg.senderId === otherUserId && msg.receiverId === userId)\n      ).sort((a, b) => a.timestamp - b.timestamp)\n\n      return NextResponse.json({ messages: conversationMessages })\n    } else {\n      // Get all messages for the current user\n      const userMessages = messages.filter(\n        msg => msg.senderId === userId || msg.receiverId === userId\n      )\n\n      return NextResponse.json({ messages: userMessages })\n    }\n  } catch (error) {\n    console.error('Error fetching messages:', error)\n    return NextResponse.json({ error: 'Failed to fetch messages' }, { status: 500 })\n  }\n}\n\nexport async function POST(request: Request) {\n  try {\n    const { userId } = await auth()\n    \n    if (!userId) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const { receiverId, content } = await request.json()\n\n    if (!receiverId || !content) {\n      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })\n    }\n\n    const newMessage = {\n      id: Date.now().toString(),\n      senderId: userId,\n      receiverId,\n      content,\n      timestamp: Date.now(),\n      read: false\n    }\n\n    messages.push(newMessage)\n\n    return NextResponse.json({ message: newMessage })\n  } catch (error) {\n    console.error('Error sending message:', error)\n    return NextResponse.json({ error: 'Failed to send message' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEA,sEAAsE;AACtE,IAAI,WAAkB,EAAE;AAEjB,eAAe,IAAI,OAAgB;IACxC,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAA,yMAAI;QAE7B,IAAI,CAAC,QAAQ;YACX,OAAO,4JAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,cAAc,aAAa,GAAG,CAAC;QAErC,IAAI,aAAa;YACf,qCAAqC;YACrC,MAAM,uBAAuB,SAAS,MAAM,CAC1C,CAAA,MACE,AAAC,IAAI,QAAQ,KAAK,UAAU,IAAI,UAAU,KAAK,eAC9C,IAAI,QAAQ,KAAK,eAAe,IAAI,UAAU,KAAK,QACtD,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;YAE1C,OAAO,4JAAY,CAAC,IAAI,CAAC;gBAAE,UAAU;YAAqB;QAC5D,OAAO;YACL,wCAAwC;YACxC,MAAM,eAAe,SAAS,MAAM,CAClC,CAAA,MAAO,IAAI,QAAQ,KAAK,UAAU,IAAI,UAAU,KAAK;YAGvD,OAAO,4JAAY,CAAC,IAAI,CAAC;gBAAE,UAAU;YAAa;QACpD;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,4JAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAA2B,GAAG;YAAE,QAAQ;QAAI;IAChF;AACF;AAEO,eAAe,KAAK,OAAgB;IACzC,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAA,yMAAI;QAE7B,IAAI,CAAC,QAAQ;YACX,OAAO,4JAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,MAAM,QAAQ,IAAI;QAElD,IAAI,CAAC,cAAc,CAAC,SAAS;YAC3B,OAAO,4JAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA0B,GAAG;gBAAE,QAAQ;YAAI;QAC/E;QAEA,MAAM,aAAa;YACjB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,UAAU;YACV;YACA;YACA,WAAW,KAAK,GAAG;YACnB,MAAM;QACR;QAEA,SAAS,IAAI,CAAC;QAEd,OAAO,4JAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAW;IACjD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,4JAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAyB,GAAG;YAAE,QAAQ;QAAI;IAC9E;AACF", "debugId": null}}]}