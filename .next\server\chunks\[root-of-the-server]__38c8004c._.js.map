{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/app/api/users/%5Bid%5D/route.ts"], "sourcesContent": ["import { auth, clerkClient } from '@clerk/nextjs/server'\nimport { NextResponse } from 'next/server'\n\nexport async function GET(\n  request: Request,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const { userId } = await auth()\n\n    if (!userId) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const { id } = await params\n\n    // Get user from Clerk\n    const client = await clerkClient()\n    const clerkUser = await client.users.getUser(id)\n\n    const userData = {\n      id: clerkUser.id,\n      username: clerkUser.username || clerkUser.emailAddresses[0]?.emailAddress?.split('@')[0] || 'user',\n      firstName: clerkUser.firstName || '',\n      lastName: clerkUser.lastName || '',\n      imageUrl: clerkUser.imageUrl,\n      emailAddress: clerkUser.emailAddresses[0]?.emailAddress || ''\n    }\n\n    return NextResponse.json({ user: userData })\n  } catch (error) {\n    console.error('Error fetching user:', error)\n    return NextResponse.json({ error: 'User not found' }, { status: 404 })\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;AAEO,eAAe,IACpB,OAAgB,EAChB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAA,yMAAI;QAE7B,IAAI,CAAC,QAAQ;YACX,OAAO,4JAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QAErB,sBAAsB;QACtB,MAAM,SAAS,MAAM,IAAA,sMAAW;QAChC,MAAM,YAAY,MAAM,OAAO,KAAK,CAAC,OAAO,CAAC;QAE7C,MAAM,WAAW;YACf,IAAI,UAAU,EAAE;YAChB,UAAU,UAAU,QAAQ,IAAI,UAAU,cAAc,CAAC,EAAE,EAAE,cAAc,MAAM,IAAI,CAAC,EAAE,IAAI;YAC5F,WAAW,UAAU,SAAS,IAAI;YAClC,UAAU,UAAU,QAAQ,IAAI;YAChC,UAAU,UAAU,QAAQ;YAC5B,cAAc,UAAU,cAAc,CAAC,EAAE,EAAE,gBAAgB;QAC7D;QAEA,OAAO,4JAAY,CAAC,IAAI,CAAC;YAAE,MAAM;QAAS;IAC5C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,4JAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAiB,GAAG;YAAE,QAAQ;QAAI;IACtE;AACF", "debugId": null}}]}