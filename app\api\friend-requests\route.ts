import { auth } from '@clerk/nextjs/server'
import { NextResponse } from 'next/server'

// In-memory storage for friend requests (in production, use a real database)
let friendRequests: any[] = []

export async function GET(request: Request) {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') // 'sent' or 'received'

    let userRequests = friendRequests

    if (type === 'sent') {
      userRequests = friendRequests.filter(req => req.senderId === userId)
    } else if (type === 'received') {
      userRequests = friendRequests.filter(req => req.receiverId === userId)
    } else {
      // Get all requests involving the user
      userRequests = friendRequests.filter(
        req => req.senderId === userId || req.receiverId === userId
      )
    }

    return NextResponse.json({ friendRequests: userRequests })
  } catch (error) {
    console.error('Error fetching friend requests:', error)
    return NextResponse.json({ error: 'Failed to fetch friend requests' }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { receiverId } = await request.json()

    if (!receiverId) {
      return NextResponse.json({ error: 'Missing receiverId' }, { status: 400 })
    }

    // Check if request already exists
    const existingRequest = friendRequests.find(
      req => req.senderId === userId && req.receiverId === receiverId && req.status === 'pending'
    )

    if (existingRequest) {
      return NextResponse.json({ error: 'Friend request already sent' }, { status: 400 })
    }

    const newRequest = {
      id: Date.now().toString(),
      senderId: userId,
      receiverId,
      status: 'pending',
      timestamp: Date.now()
    }

    friendRequests.push(newRequest)

    return NextResponse.json({ friendRequest: newRequest })
  } catch (error) {
    console.error('Error sending friend request:', error)
    return NextResponse.json({ error: 'Failed to send friend request' }, { status: 500 })
  }
}

export async function PATCH(request: Request) {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { requestId, status } = await request.json()

    if (!requestId || !status) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    const requestIndex = friendRequests.findIndex(
      req => req.id === requestId && req.receiverId === userId
    )

    if (requestIndex === -1) {
      return NextResponse.json({ error: 'Friend request not found' }, { status: 404 })
    }

    friendRequests[requestIndex].status = status

    return NextResponse.json({ friendRequest: friendRequests[requestIndex] })
  } catch (error) {
    console.error('Error updating friend request:', error)
    return NextResponse.json({ error: 'Failed to update friend request' }, { status: 500 })
  }
}
