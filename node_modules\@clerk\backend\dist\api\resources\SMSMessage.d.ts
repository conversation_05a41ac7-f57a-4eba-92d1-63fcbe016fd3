import type { SMSMessageJSON } from './JSON';
export declare class SMSMessage {
    readonly id: string;
    readonly fromPhoneNumber: string;
    readonly toPhoneNumber: string;
    readonly message: string;
    readonly status: string;
    readonly phoneNumberId: string | null;
    readonly data?: (Record<string, any> | null) | undefined;
    constructor(id: string, fromPhoneNumber: string, toPhoneNumber: string, message: string, status: string, phoneNumberId: string | null, data?: (Record<string, any> | null) | undefined);
    static fromJSON(data: SMSMessageJSON): SMSMessage;
}
//# sourceMappingURL=SMSMessage.d.ts.map