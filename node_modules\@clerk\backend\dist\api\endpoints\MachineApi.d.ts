import type { PaginatedResourceResponse } from '../resources/Deserializer';
import type { Machine } from '../resources/Machine';
import type { MachineScope } from '../resources/MachineScope';
import type { MachineSecretKey } from '../resources/MachineSecretKey';
import { AbstractAPI } from './AbstractApi';
type CreateMachineParams = {
    /**
     * The name of the machine.
     */
    name: string;
    /**
     * Array of machine IDs that this machine will have access to.
     */
    scopedMachines?: string[];
    /**
     * The default time-to-live (TTL) in seconds for tokens created by this machine.
     */
    defaultTokenTtl?: number;
};
type UpdateMachineParams = {
    /**
     * The ID of the machine to update.
     */
    machineId: string;
    /**
     * The name of the machine.
     */
    name?: string;
    /**
     * The default time-to-live (TTL) in seconds for tokens created by this machine.
     */
    defaultTokenTtl?: number;
};
type GetMachineListParams = {
    limit?: number;
    offset?: number;
    query?: string;
};
export declare class <PERSON><PERSON><PERSON> extends AbstractAPI {
    get(machineId: string): Promise<Machine>;
    list(queryParams?: GetMachineListParams): Promise<PaginatedResourceResponse<Machine[]>>;
    create(bodyParams: CreateMachineParams): Promise<Machine>;
    update(params: UpdateMachineParams): Promise<Machine>;
    delete(machineId: string): Promise<Machine>;
    getSecretKey(machineId: string): Promise<MachineSecretKey>;
    /**
     * Creates a new machine scope, allowing the specified machine to access another machine.
     *
     * @param machineId - The ID of the machine that will have access to another machine.
     * @param toMachineId - The ID of the machine that will be scoped to the current machine.
     */
    createScope(machineId: string, toMachineId: string): Promise<MachineScope>;
    /**
     * Deletes a machine scope, removing access from one machine to another.
     *
     * @param machineId - The ID of the machine that has access to another machine.
     * @param otherMachineId - The ID of the machine that is being accessed.
     */
    deleteScope(machineId: string, otherMachineId: string): Promise<MachineScope>;
}
export {};
//# sourceMappingURL=MachineApi.d.ts.map