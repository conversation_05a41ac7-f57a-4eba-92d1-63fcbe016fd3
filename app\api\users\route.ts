import { auth, currentUser } from '@clerk/nextjs/server'
import { NextResponse } from 'next/server'
import { userStorage } from '@/lib/userStorage'

export async function GET(request: Request) {
  try {
    const { userId } = await auth()

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q') || ''

    // Search users using the storage service
    const filteredUsers = userStorage.searchUsers(query, userId)

    return NextResponse.json({ users: filteredUsers })
  } catch (error) {
    console.error('Error fetching users:', error)
    return NextResponse.json({ error: 'Failed to fetch users' }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    const { userId } = await auth()

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const user = await currentUser()

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const userData = {
      id: user.id,
      username: user.username || user.emailAddresses[0]?.emailAddress?.split('@')[0] || 'user',
      firstName: user.firstName || '',
      lastName: user.lastName || '',
      imageUrl: user.imageUrl,
      emailAddress: user.emailAddresses[0]?.emailAddress || ''
    }

    // Add user to storage
    userStorage.addUser(userData)

    return NextResponse.json({ user: userData })
  } catch (error) {
    console.error('Error registering user:', error)
    return NextResponse.json({ error: 'Failed to register user' }, { status: 500 })
  }
}
