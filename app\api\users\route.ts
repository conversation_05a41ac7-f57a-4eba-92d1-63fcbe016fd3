import { clerkClient } from '@clerk/nextjs/server'
import { auth } from '@clerk/nextjs/server'
import { NextResponse } from 'next/server'

export async function GET(request: Request) {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q') || ''

    // Get all users from Clerk
    const users = await clerkClient.users.getUserList({
      limit: 50,
    })

    // Filter users based on search query and exclude current user
    const filteredUsers = users.data
      .filter(user => user.id !== userId)
      .filter(user => {
        if (!query) return true
        
        const searchTerm = query.toLowerCase()
        const username = user.username?.toLowerCase() || ''
        const firstName = user.firstName?.toLowerCase() || ''
        const lastName = user.lastName?.toLowerCase() || ''
        const email = user.emailAddresses[0]?.emailAddress?.toLowerCase() || ''
        
        return username.includes(searchTerm) || 
               firstName.includes(searchTerm) || 
               lastName.includes(searchTerm) ||
               email.includes(searchTerm)
      })
      .map(user => ({
        id: user.id,
        username: user.username || user.emailAddresses[0]?.emailAddress?.split('@')[0] || 'user',
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        imageUrl: user.imageUrl,
        emailAddress: user.emailAddresses[0]?.emailAddress || ''
      }))

    return NextResponse.json({ users: filteredUsers })
  } catch (error) {
    console.error('Error fetching users:', error)
    return NextResponse.json({ error: 'Failed to fetch users' }, { status: 500 })
  }
}
