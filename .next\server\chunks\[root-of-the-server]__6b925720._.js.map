{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/lib/userStorage.ts"], "sourcesContent": ["// Simple in-memory storage for demo purposes\n// In production, you'd use a real database like PostgreSQL, MongoDB, etc.\n\ninterface RegisteredUser {\n  id: string\n  username: string\n  firstName: string\n  lastName: string\n  imageUrl?: string\n  emailAddress: string\n}\n\nclass UserStorage {\n  private users: RegisteredUser[] = []\n\n  addUser(user: RegisteredUser) {\n    const existingIndex = this.users.findIndex(u => u.id === user.id)\n    if (existingIndex !== -1) {\n      this.users[existingIndex] = user\n    } else {\n      this.users.push(user)\n    }\n  }\n\n  getUser(id: string): RegisteredUser | undefined {\n    return this.users.find(u => u.id === id)\n  }\n\n  getAllUsers(): RegisteredUser[] {\n    return this.users\n  }\n\n  searchUsers(query: string, excludeUserId?: string): RegisteredUser[] {\n    return this.users\n      .filter(user => excludeUserId ? user.id !== excludeUserId : true)\n      .filter(user => {\n        if (!query) return true\n        \n        const searchTerm = query.toLowerCase()\n        const username = user.username?.toLowerCase() || ''\n        const firstName = user.firstName?.toLowerCase() || ''\n        const lastName = user.lastName?.toLowerCase() || ''\n        const email = user.emailAddress?.toLowerCase() || ''\n        \n        return username.includes(searchTerm) || \n               firstName.includes(searchTerm) || \n               lastName.includes(searchTerm) ||\n               email.includes(searchTerm)\n      })\n  }\n}\n\n// Export a singleton instance\nexport const userStorage = new UserStorage()\nexport type { RegisteredUser }\n"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,0EAA0E;;;;;AAW1E,MAAM;IACI,QAA0B,EAAE,CAAA;IAEpC,QAAQ,IAAoB,EAAE;QAC5B,MAAM,gBAAgB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE;QAChE,IAAI,kBAAkB,CAAC,GAAG;YACxB,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG;QAC9B,OAAO;YACL,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;QAClB;IACF;IAEA,QAAQ,EAAU,EAA8B;QAC9C,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACvC;IAEA,cAAgC;QAC9B,OAAO,IAAI,CAAC,KAAK;IACnB;IAEA,YAAY,KAAa,EAAE,aAAsB,EAAoB;QACnE,OAAO,IAAI,CAAC,KAAK,CACd,MAAM,CAAC,CAAA,OAAQ,gBAAgB,KAAK,EAAE,KAAK,gBAAgB,MAC3D,MAAM,CAAC,CAAA;YACN,IAAI,CAAC,OAAO,OAAO;YAEnB,MAAM,aAAa,MAAM,WAAW;YACpC,MAAM,WAAW,KAAK,QAAQ,EAAE,iBAAiB;YACjD,MAAM,YAAY,KAAK,SAAS,EAAE,iBAAiB;YACnD,MAAM,WAAW,KAAK,QAAQ,EAAE,iBAAiB;YACjD,MAAM,QAAQ,KAAK,YAAY,EAAE,iBAAiB;YAElD,OAAO,SAAS,QAAQ,CAAC,eAClB,UAAU,QAAQ,CAAC,eACnB,SAAS,QAAQ,CAAC,eAClB,MAAM,QAAQ,CAAC;QACxB;IACJ;AACF;AAGO,MAAM,cAAc,IAAI", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/app/api/users/%5Bid%5D/route.ts"], "sourcesContent": ["import { auth } from '@clerk/nextjs/server'\nimport { NextResponse } from 'next/server'\nimport { userStorage } from '@/lib/userStorage'\n\nexport async function GET(\n  request: Request,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const { userId } = await auth()\n\n    if (!userId) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const user = userStorage.getUser(params.id)\n\n    if (!user) {\n      return NextResponse.json({ error: 'User not found' }, { status: 404 })\n    }\n\n    return NextResponse.json({ user })\n  } catch (error) {\n    console.error('Error fetching user:', error)\n    return NextResponse.json({ error: 'User not found' }, { status: 404 })\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEO,eAAe,IACpB,OAAgB,EAChB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAA,yMAAI;QAE7B,IAAI,CAAC,QAAQ;YACX,OAAO,4JAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,OAAO,+IAAW,CAAC,OAAO,CAAC,OAAO,EAAE;QAE1C,IAAI,CAAC,MAAM;YACT,OAAO,4JAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAiB,GAAG;gBAAE,QAAQ;YAAI;QACtE;QAEA,OAAO,4JAAY,CAAC,IAAI,CAAC;YAAE;QAAK;IAClC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,4JAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAiB,GAAG;YAAE,QAAQ;QAAI;IACtE;AACF", "debugId": null}}]}