import type { PaginatedResourceResponse } from '../resources/Deserializer';
import type { RedirectUrl } from '../resources/RedirectUrl';
import { AbstractAPI } from './AbstractApi';
type CreateRedirectUrlParams = {
    url: string;
};
export declare class RedirectUrlAPI extends AbstractAPI {
    getRedirectUrlList(): Promise<PaginatedResourceResponse<RedirectUrl[]>>;
    getRedirectUrl(redirectUrlId: string): Promise<RedirectUrl>;
    createRedirectUrl(params: CreateRedirectUrlParams): Promise<RedirectUrl>;
    deleteRedirectUrl(redirectUrlId: string): Promise<RedirectUrl>;
}
export {};
//# sourceMappingURL=RedirectUrlApi.d.ts.map