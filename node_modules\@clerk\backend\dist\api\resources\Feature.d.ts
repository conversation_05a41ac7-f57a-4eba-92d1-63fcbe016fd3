import type { FeatureJSON } from './JSON';
export declare class Feature {
    readonly id: string;
    readonly name: string;
    readonly description: string;
    readonly slug: string;
    readonly avatarUrl: string;
    constructor(id: string, name: string, description: string, slug: string, avatarUrl: string);
    static fromJSON(data: FeatureJSON): Feature;
}
//# sourceMappingURL=Feature.d.ts.map