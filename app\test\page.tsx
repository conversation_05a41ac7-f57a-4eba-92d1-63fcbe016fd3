'use client'

import { useEffect, useState } from 'react'
import { useUser } from '@clerk/nextjs'

export default function TestPage() {
  const { user } = useUser()
  const [users, setUsers] = useState([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const testAPI = async () => {
    setLoading(true)
    setError('')
    try {
      const response = await fetch('/api/users?q=')
      const data = await response.json()
      
      if (response.ok) {
        setUsers(data.users)
      } else {
        setError(data.error || 'Failed to fetch users')
      }
    } catch (err) {
      setError('Network error: ' + err.message)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">API Test Page</h1>
      
      {user ? (
        <div className="mb-4">
          <p>Logged in as: {user.firstName} {user.lastName} ({user.username})</p>
          <p>User ID: {user.id}</p>
        </div>
      ) : (
        <p className="mb-4">Not logged in</p>
      )}

      <button 
        onClick={testAPI}
        disabled={loading}
        className="bg-blue-600 text-white px-4 py-2 rounded mb-4"
      >
        {loading ? 'Loading...' : 'Test Users API'}
      </button>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          Error: {error}
        </div>
      )}

      {users.length > 0 && (
        <div>
          <h2 className="text-xl font-semibold mb-2">Users Found:</h2>
          <div className="space-y-2">
            {users.map((user: any) => (
              <div key={user.id} className="border p-3 rounded">
                <p><strong>Name:</strong> {user.firstName} {user.lastName}</p>
                <p><strong>Username:</strong> {user.username}</p>
                <p><strong>Email:</strong> {user.emailAddress}</p>
                <p><strong>ID:</strong> {user.id}</p>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
