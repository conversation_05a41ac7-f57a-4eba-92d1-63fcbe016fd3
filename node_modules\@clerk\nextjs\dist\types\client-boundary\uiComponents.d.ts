import { OrganizationProfile as BaseOrganizationProfile, SignIn as BaseSignIn, SignUp as BaseSignUp, UserProfile as BaseUserProfile } from '@clerk/clerk-react';
import type { ComponentProps } from 'react';
import React from 'react';
export { APIKeys, CreateOrganization, GoogleOneTap, OrganizationList, OrganizationSwitcher, PricingTable, SignInButton, SignInWithMetamaskButton, SignOutButton, SignUpButton, TaskChooseOrganization, UserButton, Waitlist, } from '@clerk/clerk-react';
export declare const UserProfile: typeof BaseUserProfile;
export declare const OrganizationProfile: typeof BaseOrganizationProfile;
export declare const SignIn: (props: ComponentProps<typeof BaseSignIn>) => React.JSX.Element;
export declare const SignUp: (props: ComponentProps<typeof BaseSignUp>) => React.JSX.Element;
//# sourceMappingURL=uiComponents.d.ts.map