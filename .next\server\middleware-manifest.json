{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/messages_85b63348._.js", "server/edge/chunks/65b60_@clerk_backend_dist_7579d10a._.js", "server/edge/chunks/65b60_@clerk_nextjs_dist_esm_7d3842b5._.js", "server/edge/chunks/65b60_9819ab28._.js", "server/edge/chunks/[root-of-the-server]__2ab891ad._.js", "server/edge/chunks/turbopack-messages_edge-wrapper_acc7ce94.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "OxAWieUPWAb8h3sgBkBET1V4OzSwxPT93mzTMrPdOtk=", "__NEXT_PREVIEW_MODE_ID": "7120e921ef3f94918fd75cdca4ccce47", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "0e932133a648021714080b37bd1f9cc4b0300a0109ca1f4d9a630b6383303321", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "45775c2a5211ec7c482f7e7577bb207721da311da6c0e4f04bf2171dad2371c4"}}}, "sortedMiddleware": ["/"], "functions": {}}