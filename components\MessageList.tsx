'use client'

import { useState, useEffect } from 'react'
import { User, Message } from '@/lib/types'
import { storageUtils } from '@/lib/storage'
import Chat<PERSON><PERSON>ow from './ChatWindow'
import UserDisplay from './UserDisplay'

interface MessageListProps {
  currentUserId: string
  users: User[]
  messages: Message[]
  onSendMessage: (receiverId: string, content: string) => void
}

export default function MessageList({ currentUserId, users, messages, onSendMessage }: MessageListProps) {
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null)
  const [conversations, setConversations] = useState<{ userId: string; lastMessage?: Message; unreadCount: number }[]>([])

  useEffect(() => {
    // Get all users that have conversations with current user
    const userConversations = new Map<string, { lastMessage?: Message; unreadCount: number }>()
    
    messages.forEach(message => {
      const otherUserId = message.senderId === currentUserId ? message.receiverId : message.senderId
      
      if (otherUserId !== currentUserId) {
        const existing = userConversations.get(otherUserId)
        const isUnread = message.receiverId === currentUserId && !message.read
        
        userConversations.set(otherUserId, {
          lastMessage: !existing?.lastMessage || message.timestamp > existing.lastMessage.timestamp 
            ? message 
            : existing.lastMessage,
          unreadCount: (existing?.unreadCount || 0) + (isUnread ? 1 : 0)
        })
      }
    })

    const conversationList = Array.from(userConversations.entries())
      .map(([userId, data]) => ({ userId, ...data }))
      .sort((a, b) => (b.lastMessage?.timestamp || 0) - (a.lastMessage?.timestamp || 0))

    setConversations(conversationList)
  }, [messages, currentUserId])

  const getUserById = (userId: string) => {
    return users.find(user => user.id === userId)
  }

  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)
    
    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString([], { weekday: 'short' })
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' })
    }
  }

  const truncateMessage = (content: string, maxLength: number = 50) => {
    return content.length > maxLength ? content.substring(0, maxLength) + '...' : content
  }

  return (
    <div className="flex h-[600px]">
      {/* Conversations List */}
      <div className="w-1/3 border-r border-gray-200">
        <div className="p-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Messages</h3>
        </div>
        
        <div className="overflow-y-auto h-full">
          {conversations.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              <p>No conversations yet</p>
              <p className="text-sm mt-1">Search for users to start messaging</p>
            </div>
          ) : (
            conversations.map(({ userId, lastMessage, unreadCount }) => {
              const user = getUserById(userId)

              if (user) {
                return (
                  <div
                    key={userId}
                    onClick={() => setSelectedUserId(userId)}
                    className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                      selectedUserId === userId ? 'bg-blue-50 border-blue-200' : ''
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <img
                        src={user.imageUrl || `https://ui-avatars.com/api/?name=${user.firstName}+${user.lastName}&background=random`}
                        alt={`${user.firstName} ${user.lastName}`}
                        className="w-10 h-10 rounded-full object-cover"
                      />

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {user.firstName} {user.lastName}
                          </p>
                          {lastMessage && (
                            <p className="text-xs text-gray-500">
                              {formatTime(lastMessage.timestamp)}
                            </p>
                          )}
                        </div>

                        <div className="flex items-center justify-between">
                          {lastMessage && (
                            <p className="text-sm text-gray-500 truncate">
                              {lastMessage.senderId === currentUserId ? 'You: ' : ''}
                              {truncateMessage(lastMessage.content)}
                            </p>
                          )}
                          {unreadCount > 0 && (
                            <span className="bg-blue-600 text-white text-xs rounded-full px-2 py-1 ml-2">
                              {unreadCount}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )
              }

              // For users not in cache, use UserDisplay
              return (
                <UserDisplay key={userId} userId={userId}>
                  {(fetchedUser, isLoading) => {
                    if (isLoading) {
                      return (
                        <div className="p-4 border-b border-gray-100">
                          <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 bg-gray-200 rounded-full animate-pulse"></div>
                            <div className="flex-1">
                              <div className="h-4 bg-gray-200 rounded animate-pulse mb-2"></div>
                              <div className="h-3 bg-gray-200 rounded animate-pulse w-1/2"></div>
                            </div>
                          </div>
                        </div>
                      )
                    }

                    if (!fetchedUser) return null

                    return (
                      <div
                        onClick={() => setSelectedUserId(userId)}
                        className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                          selectedUserId === userId ? 'bg-blue-50 border-blue-200' : ''
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          <img
                            src={fetchedUser.imageUrl || `https://ui-avatars.com/api/?name=${fetchedUser.firstName}+${fetchedUser.lastName}&background=random`}
                            alt={`${fetchedUser.firstName} ${fetchedUser.lastName}`}
                            className="w-10 h-10 rounded-full object-cover"
                          />

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <p className="text-sm font-medium text-gray-900 truncate">
                                {fetchedUser.firstName} {fetchedUser.lastName}
                              </p>
                              {lastMessage && (
                                <p className="text-xs text-gray-500">
                                  {formatTime(lastMessage.timestamp)}
                                </p>
                              )}
                            </div>

                            <div className="flex items-center justify-between">
                              {lastMessage && (
                                <p className="text-sm text-gray-500 truncate">
                                  {lastMessage.senderId === currentUserId ? 'You: ' : ''}
                                  {truncateMessage(lastMessage.content)}
                                </p>
                              )}
                              {unreadCount > 0 && (
                                <span className="bg-blue-600 text-white text-xs rounded-full px-2 py-1 ml-2">
                                  {unreadCount}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    )
                  }}
                </UserDisplay>
              )
            })
          )}
        </div>
      </div>

      {/* Chat Window */}
      <div className="flex-1">
        {selectedUserId ? (
          <UserDisplay userId={selectedUserId}>
            {(otherUser, isLoading) => (
              <ChatWindow
                currentUserId={currentUserId}
                otherUserId={selectedUserId}
                otherUser={otherUser}
                onSendMessage={onSendMessage}
                isLoadingUser={isLoading}
              />
            )}
          </UserDisplay>
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            <div className="text-center">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-2.4-.32l-4.6 1.92 1.92-4.6A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
              </svg>
              <p className="mt-2">Select a conversation to start messaging</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
