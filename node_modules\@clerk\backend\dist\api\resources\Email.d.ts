import type { EmailJSON } from './JSON';
export declare class Email {
    readonly id: string;
    readonly fromEmailName: string;
    readonly emailAddressId: string | null;
    readonly toEmailAddress?: string | undefined;
    readonly subject?: string | undefined;
    readonly body?: string | undefined;
    readonly bodyPlain?: string | null | undefined;
    readonly status?: string | undefined;
    readonly slug?: string | null | undefined;
    readonly data?: (Record<string, any> | null) | undefined;
    readonly deliveredByClerk?: boolean | undefined;
    constructor(id: string, fromEmailName: string, emailAddressId: string | null, toEmailAddress?: string | undefined, subject?: string | undefined, body?: string | undefined, bodyPlain?: string | null | undefined, status?: string | undefined, slug?: string | null | undefined, data?: (Record<string, any> | null) | undefined, deliveredByClerk?: boolean | undefined);
    static fromJSON(data: EmailJSON): Email;
}
//# sourceMappingURL=Email.d.ts.map