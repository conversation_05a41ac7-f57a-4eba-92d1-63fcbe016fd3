module.exports = [
"[project]/messages/node_modules/@clerk/nextjs/dist/esm/server/fs/middleware-location.js [app-route] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/[root-of-the-server]__780ff4cd._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/messages/node_modules/@clerk/nextjs/dist/esm/server/fs/middleware-location.js [app-route] (ecmascript)");
    });
});
}),
"[project]/messages/node_modules/next/headers.js [app-route] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/65b60_next_07ab0b30._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/messages/node_modules/next/headers.js [app-route] (ecmascript)");
    });
});
}),
];