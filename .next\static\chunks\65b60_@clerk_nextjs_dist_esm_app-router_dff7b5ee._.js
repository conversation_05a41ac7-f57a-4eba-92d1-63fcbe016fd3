(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/messages/node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-creator-reader.js [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/65b60_@clerk_nextjs_dist_esm_app-router_641809b8._.js",
  "static/chunks/65b60_@clerk_nextjs_dist_esm_app-router_client_keyless-creator-reader_db7cb529.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/messages/node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-creator-reader.js [app-client] (ecmascript)");
    });
});
}),
"[project]/messages/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/65b60_@clerk_nextjs_dist_esm_app-router_d76cb3ac._.js",
  "static/chunks/65b60_@clerk_nextjs_dist_esm_app-router_keyless-actions_376e19c3.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/messages/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-client] (ecmascript)");
    });
});
}),
]);