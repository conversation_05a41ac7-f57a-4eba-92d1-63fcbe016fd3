// This file is generated automatically by Next.js
// Do not edit this file manually
// This file validates that all pages and layouts export the correct types

import type { AppRoutes, LayoutRoutes, ParamMap, AppRouteHandlerRoutes } from "./routes.js"
import type { ResolvingMetadata, ResolvingViewport } from "next/dist/lib/metadata/types/metadata-interface.js"
import type { NextRequest } from 'next/server.js'

type AppPageConfig<Route extends AppRoutes = AppRoutes> = {
  default: React.ComponentType<{ params: Promise<ParamMap[Route]> } & any> | ((props: { params: Promise<ParamMap[Route]> } & any) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type LayoutConfig<Route extends LayoutRoutes = LayoutRoutes> = {
  default: React.ComponentType<LayoutProps<Route>> | ((props: LayoutProps<Route>) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type RouteHandlerConfig<Route extends AppRouteHandlerRoutes = AppRouteHandlerRoutes> = {
  GET?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  POST?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  PUT?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  PATCH?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  DELETE?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  HEAD?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  OPTIONS?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
}


// Validate ../../app/dashboard/page.tsx
{
  const handler = {} as typeof import("../../app/dashboard/page.js")
  handler satisfies AppPageConfig<"/dashboard">
}

// Validate ../../app/page.tsx
{
  const handler = {} as typeof import("../../app/page.js")
  handler satisfies AppPageConfig<"/">
}

// Validate ../../app/sign-in/[[...sign-in]]/page.tsx
{
  const handler = {} as typeof import("../../app/sign-in/[[...sign-in]]/page.js")
  handler satisfies AppPageConfig<"/sign-in/[[...sign-in]]">
}

// Validate ../../app/sign-up/[[...sign-up]]/page.tsx
{
  const handler = {} as typeof import("../../app/sign-up/[[...sign-up]]/page.js")
  handler satisfies AppPageConfig<"/sign-up/[[...sign-up]]">
}

// Validate ../../app/test/page.tsx
{
  const handler = {} as typeof import("../../app/test/page.js")
  handler satisfies AppPageConfig<"/test">
}

// Validate ../../app/api/friend-requests/route.ts
{
  const handler = {} as typeof import("../../app/api/friend-requests/route.js")
  handler satisfies RouteHandlerConfig<"/api/friend-requests">
}

// Validate ../../app/api/messages/route.ts
{
  const handler = {} as typeof import("../../app/api/messages/route.js")
  handler satisfies RouteHandlerConfig<"/api/messages">
}

// Validate ../../app/api/users/[id]/route.ts
{
  const handler = {} as typeof import("../../app/api/users/[id]/route.js")
  handler satisfies RouteHandlerConfig<"/api/users/[id]">
}

// Validate ../../app/api/users/route.ts
{
  const handler = {} as typeof import("../../app/api/users/route.js")
  handler satisfies RouteHandlerConfig<"/api/users">
}





// Validate ../../app/layout.tsx
{
  const handler = {} as typeof import("../../app/layout.js")
  handler satisfies LayoutConfig<"/">
}
