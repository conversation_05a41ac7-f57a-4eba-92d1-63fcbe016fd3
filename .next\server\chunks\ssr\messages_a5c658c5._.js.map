{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/app/test/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useUser } from '@clerk/nextjs'\n\nexport default function TestPage() {\n  const { user } = useUser()\n  const [users, setUsers] = useState([])\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const testAPI = async () => {\n    setLoading(true)\n    setError('')\n    try {\n      const response = await fetch('/api/users?q=')\n      const data = await response.json()\n      \n      if (response.ok) {\n        setUsers(data.users)\n      } else {\n        setError(data.error || 'Failed to fetch users')\n      }\n    } catch (err) {\n      setError('Network error: ' + err.message)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"p-8\">\n      <h1 className=\"text-2xl font-bold mb-4\">API Test Page</h1>\n      \n      {user ? (\n        <div className=\"mb-4\">\n          <p>Logged in as: {user.firstName} {user.lastName} ({user.username})</p>\n          <p>User ID: {user.id}</p>\n        </div>\n      ) : (\n        <p className=\"mb-4\">Not logged in</p>\n      )}\n\n      <button \n        onClick={testAPI}\n        disabled={loading}\n        className=\"bg-blue-600 text-white px-4 py-2 rounded mb-4\"\n      >\n        {loading ? 'Loading...' : 'Test Users API'}\n      </button>\n\n      {error && (\n        <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n          Error: {error}\n        </div>\n      )}\n\n      {users.length > 0 && (\n        <div>\n          <h2 className=\"text-xl font-semibold mb-2\">Users Found:</h2>\n          <div className=\"space-y-2\">\n            {users.map((user: any) => (\n              <div key={user.id} className=\"border p-3 rounded\">\n                <p><strong>Name:</strong> {user.firstName} {user.lastName}</p>\n                <p><strong>Username:</strong> {user.username}</p>\n                <p><strong>Email:</strong> {user.emailAddress}</p>\n                <p><strong>ID:</strong> {user.id}</p>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,IAAA,mLAAO;IACxB,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,6NAAQ,EAAC,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,6NAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,6NAAQ,EAAC;IAEnC,MAAM,UAAU;QACd,WAAW;QACX,SAAS;QACT,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,SAAS,KAAK,KAAK;YACrB,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,oBAAoB,IAAI,OAAO;QAC1C,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,0PAAC;QAAI,WAAU;;0BACb,0PAAC;gBAAG,WAAU;0BAA0B;;;;;;YAEvC,qBACC,0PAAC;gBAAI,WAAU;;kCACb,0PAAC;;4BAAE;4BAAe,KAAK,SAAS;4BAAC;4BAAE,KAAK,QAAQ;4BAAC;4BAAG,KAAK,QAAQ;4BAAC;;;;;;;kCAClE,0PAAC;;4BAAE;4BAAU,KAAK,EAAE;;;;;;;;;;;;qCAGtB,0PAAC;gBAAE,WAAU;0BAAO;;;;;;0BAGtB,0PAAC;gBACC,SAAS;gBACT,UAAU;gBACV,WAAU;0BAET,UAAU,eAAe;;;;;;YAG3B,uBACC,0PAAC;gBAAI,WAAU;;oBAAuE;oBAC5E;;;;;;;YAIX,MAAM,MAAM,GAAG,mBACd,0PAAC;;kCACC,0PAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,0PAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,qBACV,0PAAC;gCAAkB,WAAU;;kDAC3B,0PAAC;;0DAAE,0PAAC;0DAAO;;;;;;4CAAc;4CAAE,KAAK,SAAS;4CAAC;4CAAE,KAAK,QAAQ;;;;;;;kDACzD,0PAAC;;0DAAE,0PAAC;0DAAO;;;;;;4CAAkB;4CAAE,KAAK,QAAQ;;;;;;;kDAC5C,0PAAC;;0DAAE,0PAAC;0DAAO;;;;;;4CAAe;4CAAE,KAAK,YAAY;;;;;;;kDAC7C,0PAAC;;0DAAE,0PAAC;0DAAO;;;;;;4CAAY;4CAAE,KAAK,EAAE;;;;;;;;+BAJxB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;AAY/B", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,qIACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}