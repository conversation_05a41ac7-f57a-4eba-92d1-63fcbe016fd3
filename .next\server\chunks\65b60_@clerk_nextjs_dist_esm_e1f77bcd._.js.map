{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/server/constants.ts"], "sourcesContent": ["import { apiUrlFromPublishableKey } from '@clerk/shared/apiUrlFromPublishableKey';\nimport { isTruthy } from '@clerk/shared/underscore';\n\nexport const CLERK_JS_VERSION = process.env.NEXT_PUBLIC_CLERK_JS_VERSION || '';\nexport const CLERK_JS_URL = process.env.NEXT_PUBLIC_CLERK_JS_URL || '';\nexport const API_VERSION = process.env.CLERK_API_VERSION || 'v1';\nexport const SECRET_KEY = process.env.CLERK_SECRET_KEY || '';\nexport const MACHINE_SECRET_KEY = process.env.CLERK_MACHINE_SECRET_KEY || '';\nexport const PUBLISHABLE_KEY = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY || '';\nexport const ENCRYPTION_KEY = process.env.CLERK_ENCRYPTION_KEY || '';\nexport const API_URL = process.env.CLERK_API_URL || apiUrlFromPublishableKey(PUBLISHABLE_KEY);\nexport const DOMAIN = process.env.NEXT_PUBLIC_CLERK_DOMAIN || '';\nexport const PROXY_URL = process.env.NEXT_PUBLIC_CLERK_PROXY_URL || '';\nexport const IS_SATELLITE = isTruthy(process.env.NEXT_PUBLIC_CLERK_IS_SATELLITE) || false;\nexport const SIGN_IN_URL = process.env.NEXT_PUBLIC_CLERK_SIGN_IN_URL || '';\nexport const SIGN_UP_URL = process.env.NEXT_PUBLIC_CLERK_SIGN_UP_URL || '';\nexport const SDK_METADATA = {\n  name: PACKAGE_NAME,\n  version: PACKAGE_VERSION,\n  environment: process.env.NODE_ENV,\n};\n\nexport const TELEMETRY_DISABLED = isTruthy(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DISABLED);\nexport const TELEMETRY_DEBUG = isTruthy(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DEBUG);\n\nexport const KEYLESS_DISABLED = isTruthy(process.env.NEXT_PUBLIC_CLERK_KEYLESS_DISABLED) || false;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAAS,gCAAgC;;AACzC,SAAS,gBAAgB;;;;AAElB,MAAM,mBAAmB,QAAQ,GAAA,CAAI,4BAAA,IAAgC;AACrE,MAAM,eAAe,QAAQ,GAAA,CAAI,wBAAA,IAA4B;AAC7D,MAAM,cAAc,QAAQ,GAAA,CAAI,iBAAA,IAAqB;AACrD,MAAM,aAAa,QAAQ,GAAA,CAAI,gBAAA,IAAoB;AACnD,MAAM,qBAAqB,QAAQ,GAAA,CAAI,wBAAA,IAA4B;AACnE,MAAM,kBAAkB,QAAQ,IAAI,sFAAqC;AACzE,MAAM,iBAAiB,QAAQ,GAAA,CAAI,oBAAA,IAAwB;AAC3D,MAAM,UAAU,QAAQ,GAAA,CAAI,aAAA,QAAiB,yMAAA,EAAyB,eAAe;AACrF,MAAM,SAAS,QAAQ,GAAA,CAAI,wBAAA,IAA4B;AACvD,MAAM,YAAY,QAAQ,GAAA,CAAI,2BAAA,IAA+B;AAC7D,MAAM,mBAAe,yLAAA,EAAS,QAAQ,GAAA,CAAI,8BAA8B,KAAK;AAC7E,MAAM,cAAc,QAAQ,GAAA,CAAI,6BAAA,IAAiC;AACjE,MAAM,cAAc,QAAQ,GAAA,CAAI,6BAAA,IAAiC;AACjE,MAAM,eAAe;IAC1B,MAAM;IACN,SAAS;IACT,WAAA,EAAa,QAAQ,IAAI;AAC3B;AAEO,MAAM,yBAAqB,yLAAA,EAAS,QAAQ,GAAA,CAAI,oCAAoC;AACpF,MAAM,sBAAkB,yLAAA,EAAS,QAAQ,GAAA,CAAI,iCAAiC;AAE9E,MAAM,uBAAmB,yLAAA,EAAS,QAAQ,GAAA,CAAI,kCAAkC,KAAK", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/utils/logFormatter.ts"], "sourcesContent": ["import type { LogEntry } from './debugLogger';\n\n// Move to shared once clerk/shared is used in clerk/nextjs\nconst maskSecretKey = (str: any) => {\n  if (!str || typeof str !== 'string') {\n    return str;\n  }\n\n  try {\n    return (str || '').replace(/^(sk_(live|test)_)(.+?)(.{3})$/, '$1*********$4');\n  } catch {\n    return '';\n  }\n};\n\nexport const logFormatter = (entry: LogEntry) => {\n  return (Array.isArray(entry) ? entry : [entry])\n    .map(entry => {\n      if (typeof entry === 'string') {\n        return maskSecretKey(entry);\n      }\n\n      const masked = Object.fromEntries(Object.entries(entry).map(([k, v]) => [k, maskSecretKey(v)]));\n      return JSON.stringify(masked, null, 2);\n    })\n    .join(', ');\n};\n"], "names": ["entry"], "mappings": ";;;;;AAGA,MAAM,gBAAgB,CAAC,QAAa;IAClC,IAAI,CAAC,OAAO,OAAO,QAAQ,UAAU;QACnC,OAAO;IACT;IAEA,IAAI;QACF,OAAA,CAAQ,OAAO,EAAA,EAAI,OAAA,CAAQ,kCAAkC,eAAe;IAC9E,EAAA,OAAQ;QACN,OAAO;IACT;AACF;AAEO,MAAM,eAAe,CAAC,UAAoB;IAC/C,OAAA,CAAQ,MAAM,OAAA,CAAQ,KAAK,IAAI,QAAQ;QAAC,KAAK;KAAA,EAC1C,GAAA,CAAI,CAAAA,WAAS;QACZ,IAAI,OAAOA,WAAU,UAAU;YAC7B,OAAO,cAAcA,MAAK;QAC5B;QAEA,MAAM,SAAS,OAAO,WAAA,CAAY,OAAO,OAAA,CAAQA,MAAK,EAAE,GAAA,CAAI,CAAC,CAAC,GAAG,CAAC,CAAA,GAAM;gBAAC;gBAAG,cAAc,CAAC,CAAC;aAAC,CAAC;QAC9F,OAAO,KAAK,SAAA,CAAU,QAAQ,MAAM,CAAC;IACvC,CAAC,EACA,IAAA,CAAK,IAAI;AACd", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/utils/debugLogger.ts"], "sourcesContent": ["// TODO: Replace with a more sophisticated logging solution\n\nimport nextPkg from 'next/package.json';\n\nimport { logFormatter } from './logFormatter';\n\nexport type Log = string | Record<string, unknown>;\nexport type LogEntry = Log | Log[];\nexport type Logger<L = Log> = {\n  commit: () => void;\n  debug: (...args: Array<L | (() => L)>) => void;\n  enable: () => void;\n};\nexport type LoggerNoCommit<L = Logger> = Omit<L, 'commit'>;\n\nexport const createDebugLogger = (name: string, formatter: (val: LogEntry) => string) => (): Logger => {\n  const entries: LogEntry[] = [];\n  let isEnabled = false;\n\n  return {\n    enable: () => {\n      isEnabled = true;\n    },\n    debug: (...args) => {\n      if (isEnabled) {\n        entries.push(args.map(arg => (typeof arg === 'function' ? arg() : arg)));\n      }\n    },\n    commit: () => {\n      if (isEnabled) {\n        console.log(debugLogHeader(name));\n\n        /**\n         * We buffer each collected log entry so we can format them and log them all at once.\n         * Individual console.log calls are used to ensure we don't hit platform-specific log limits (Vercel and Netlify are 4kb).\n         */\n        for (const log of entries) {\n          let output = formatter(log);\n\n          output = output\n            .split('\\n')\n            .map(l => `  ${l}`)\n            .join('\\n');\n\n          // Vercel errors if the output is > 4kb, so we truncate it\n          if (process.env.VERCEL) {\n            output = truncate(output, 4096);\n          }\n\n          console.log(output);\n        }\n\n        console.log(debugLogFooter(name));\n      }\n    },\n  };\n};\n\ntype WithLogger = <L extends Logger, H extends (...args: any[]) => any>(\n  loggerFactoryOrName: string | (() => L),\n  handlerCtor: (logger: LoggerNoCommit<L>) => H,\n) => H;\n\nexport const withLogger: WithLogger = (loggerFactoryOrName, handlerCtor) => {\n  return ((...args: any) => {\n    const factory =\n      typeof loggerFactoryOrName === 'string'\n        ? createDebugLogger(loggerFactoryOrName, logFormatter)\n        : loggerFactoryOrName;\n\n    const logger = factory();\n    const handler = handlerCtor(logger as any);\n\n    try {\n      const res = handler(...args);\n      if (typeof res === 'object' && 'then' in res && typeof res.then === 'function') {\n        return res\n          .then((val: any) => {\n            logger.commit();\n            return val;\n          })\n          .catch((err: any) => {\n            logger.commit();\n            throw err;\n          });\n      }\n      // handle sync methods\n      logger.commit();\n      return res;\n    } catch (err: any) {\n      logger.commit();\n      throw err;\n    }\n  }) as ReturnType<typeof handlerCtor>;\n};\n\nfunction debugLogHeader(name: string) {\n  return `[clerk debug start: ${name}]`;\n}\n\nfunction debugLogFooter(name: string) {\n  return `[clerk debug end: ${name}] (@clerk/nextjs=${PACKAGE_VERSION},next=${nextPkg.version},timestamp=${Math.round(new Date().getTime() / 1_000)})`;\n}\n\n// ref: https://stackoverflow.com/questions/57769465/javascript-truncate-text-by-bytes-length\nfunction truncate(str: string, maxLength: number) {\n  const encoder = new TextEncoder();\n  const decoder = new TextDecoder('utf-8');\n\n  const encodedString = encoder.encode(str);\n  const truncatedString = encodedString.slice(0, maxLength);\n\n  // return the truncated string, removing any replacement characters that result from partially truncated characters\n  return decoder.decode(truncatedString).replace(/\\uFFFD/g, '');\n}\n"], "names": [], "mappings": ";;;;;;AAEA,OAAO,aAAa;AAEpB,SAAS,oBAAoB;;;;AAWtB,MAAM,oBAAoB,CAAC,MAAc,YAAyC,MAAc;QACrG,MAAM,UAAsB,CAAC,CAAA;QAC7B,IAAI,YAAY;QAEhB,OAAO;YACL,QAAQ,MAAM;gBACZ,YAAY;YACd;YACA,OAAO,CAAA,GAAI,SAAS;gBAClB,IAAI,WAAW;oBACb,QAAQ,IAAA,CAAK,KAAK,GAAA,CAAI,CAAA,MAAQ,OAAO,QAAQ,aAAa,IAAI,IAAI,GAAI,CAAC;gBACzE;YACF;YACA,QAAQ,MAAM;gBACZ,IAAI,WAAW;oBACb,QAAQ,GAAA,CAAI,eAAe,IAAI,CAAC;oBAMhC,KAAA,MAAW,OAAO,QAAS;wBACzB,IAAI,SAAS,UAAU,GAAG;wBAE1B,SAAS,OACN,KAAA,CAAM,IAAI,EACV,GAAA,CAAI,CAAA,IAAK,CAAA,EAAA,EAAK,CAAC,EAAE,EACjB,IAAA,CAAK,IAAI;wBAGZ,IAAI,QAAQ,GAAA,CAAI,MAAA,EAAQ;4BACtB,SAAS,SAAS,QAAQ,IAAI;wBAChC;wBAEA,QAAQ,GAAA,CAAI,MAAM;oBACpB;oBAEA,QAAQ,GAAA,CAAI,eAAe,IAAI,CAAC;gBAClC;YACF;QACF;IACF;AAOO,MAAM,aAAyB,CAAC,qBAAqB,gBAAgB;IAC1E,OAAQ,CAAA,GAAI,SAAc;QACxB,MAAM,UACJ,OAAO,wBAAwB,WAC3B,kBAAkB,qBAAqB,uMAAY,IACnD;QAEN,MAAM,SAAS,QAAQ;QACvB,MAAM,UAAU,YAAY,MAAa;QAEzC,IAAI;YACF,MAAM,MAAM,QAAQ,GAAG,IAAI;YAC3B,IAAI,OAAO,QAAQ,YAAY,UAAU,OAAO,OAAO,IAAI,IAAA,KAAS,YAAY;gBAC9E,OAAO,IACJ,IAAA,CAAK,CAAC,QAAa;oBAClB,OAAO,MAAA,CAAO;oBACd,OAAO;gBACT,CAAC,EACA,KAAA,CAAM,CAAC,QAAa;oBACnB,OAAO,MAAA,CAAO;oBACd,MAAM;gBACR,CAAC;YACL;YAEA,OAAO,MAAA,CAAO;YACd,OAAO;QACT,EAAA,OAAS,KAAU;YACjB,OAAO,MAAA,CAAO;YACd,MAAM;QACR;IACF;AACF;AAEA,SAAS,eAAe,IAAA,EAAc;IACpC,OAAO,CAAA,oBAAA,EAAuB,IAAI,CAAA,CAAA,CAAA;AACpC;AAEA,SAAS,eAAe,IAAA,EAAc;IACpC,OAAO,CAAA,kBAAA,EAAqB,IAAI,CAAA,iBAAA,EAAoB,QAAe,CAAA,MAAA,EAAS,8HAAA,CAAQ,OAAO,CAAA,WAAA,EAAc,KAAK,KAAA,CAAA,AAAM,aAAA,GAAA,IAAI,KAAK,EAAE,OAAA,CAAQ,IAAI,GAAK,CAAC,CAAA,CAAA,CAAA;AACnJ;AAGA,SAAS,SAAS,GAAA,EAAa,SAAA,EAAmB;IAChD,MAAM,UAAU,IAAI,YAAY;IAChC,MAAM,UAAU,IAAI,YAAY,OAAO;IAEvC,MAAM,gBAAgB,QAAQ,MAAA,CAAO,GAAG;IACxC,MAAM,kBAAkB,cAAc,KAAA,CAAM,GAAG,SAAS;IAGxD,OAAO,QAAQ,MAAA,CAAO,eAAe,EAAE,OAAA,CAAQ,WAAW,EAAE;AAC9D", "debugId": null}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/utils/sdk-versions.ts"], "sourcesContent": ["import nextPkg from 'next/package.json';\n\nconst isNext13 = nextPkg.version.startsWith('13.');\n\n/**\n * Those versions are affected by a bundling issue that will break the application if `node:fs` is used inside a server function.\n * The affected versions are >=next@13.5.4 and <=next@14.0.4\n */\nconst isNextWithUnstableServerActions = isNext13 || nextPkg.version.startsWith('14.0');\n\nexport { isNext13, isNextWithUnstableServerActions };\n"], "names": [], "mappings": ";;;;;;AAAA,OAAO,aAAa;;;AAEpB,MAAM,WAAW,8HAAA,CAAQ,OAAA,CAAQ,UAAA,CAAW,KAAK;AAMjD,MAAM,kCAAkC,YAAY,8HAAA,CAAQ,OAAA,CAAQ,UAAA,CAAW,MAAM", "debugId": null}}, {"offset": {"line": 208, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/server/headers-utils.ts"], "sourcesContent": ["import { constants } from '@clerk/backend/internal';\nimport type { NextRequest } from 'next/server';\n\nimport type { RequestLike } from './types';\n\nexport function getCustomAttributeFromRequest(req: RequestLike, key: string): string | null | undefined {\n  // @ts-expect-error - TS doesn't like indexing into RequestLike\n  return key in req ? req[key] : undefined;\n}\n\nexport function getAuthKeyFromRequest(\n  req: RequestLike,\n  key: keyof typeof constants.Attributes,\n): string | null | undefined {\n  return getCustomAttributeFromRequest(req, constants.Attributes[key]) || getHeader(req, constants.Headers[key]);\n}\n\nexport function getHeader(req: RequestLike, name: string): string | null | undefined {\n  if (isNextRequest(req) || isRequestWebAPI(req)) {\n    return req.headers.get(name);\n  }\n\n  // If no header has been determined for IncomingMessage case, check if available within private `socket` headers\n  // When deployed to vercel, req.headers for API routes is a `IncomingHttpHeaders` key-val object which does not follow\n  // the Headers spec so the name is no longer case-insensitive.\n  return req.headers[name] || req.headers[name.toLowerCase()] || (req.socket as any)?._httpMessage?.getHeader(name);\n}\n\nexport function detectClerkMiddleware(req: RequestLike): boolean {\n  return Boolean(getAuthKeyFromRequest(req, 'AuthStatus'));\n}\n\nexport function isNextRequest(val: unknown): val is NextRequest {\n  try {\n    const { headers, nextUrl, cookies } = (val || {}) as NextRequest;\n    return (\n      typeof headers?.get === 'function' &&\n      typeof nextUrl?.searchParams.get === 'function' &&\n      typeof cookies?.get === 'function'\n    );\n  } catch {\n    return false;\n  }\n}\n\nexport function isRequestWebAPI(val: unknown): val is Request {\n  try {\n    const { headers } = (val || {}) as Request;\n    return typeof headers?.get === 'function';\n  } catch {\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,SAAS,iBAAiB;;;AAKnB,SAAS,8BAA8B,GAAA,EAAkB,GAAA,EAAwC;IAEtG,OAAO,OAAO,MAAM,GAAA,CAAI,GAAG,CAAA,GAAI,KAAA;AACjC;AAEO,SAAS,sBACd,GAAA,EACA,GAAA,EAC2B;IAC3B,OAAO,8BAA8B,KAAK,2MAAA,CAAU,UAAA,CAAW,GAAG,CAAC,KAAK,UAAU,KAAK,2MAAA,CAAU,OAAA,CAAQ,GAAG,CAAC;AAC/G;AAEO,SAAS,UAAU,GAAA,EAAkB,IAAA,EAAyC;IAjBrF,IAAA,IAAA;IAkBE,IAAI,cAAc,GAAG,KAAK,gBAAgB,GAAG,GAAG;QAC9C,OAAO,IAAI,OAAA,CAAQ,GAAA,CAAI,IAAI;IAC7B;IAKA,OAAO,IAAI,OAAA,CAAQ,IAAI,CAAA,IAAK,IAAI,OAAA,CAAQ,KAAK,WAAA,CAAY,CAAC,CAAA,IAAA,CAAA,CAAM,KAAA,CAAA,KAAA,IAAI,MAAA,KAAJ,OAAA,KAAA,IAAA,GAAoB,YAAA,KAApB,OAAA,KAAA,IAAA,GAAkC,SAAA,CAAU,KAAA;AAC9G;AAEO,SAAS,sBAAsB,GAAA,EAA2B;IAC/D,OAAO,QAAQ,sBAAsB,KAAK,YAAY,CAAC;AACzD;AAEO,SAAS,cAAc,GAAA,EAAkC;IAC9D,IAAI;QACF,MAAM,EAAE,OAAA,EAAS,OAAA,EAAS,OAAA,CAAQ,CAAA,GAAK,OAAO,CAAC;QAC/C,OACE,OAAA,CAAO,WAAA,OAAA,KAAA,IAAA,QAAS,GAAA,MAAQ,cACxB,OAAA,CAAO,WAAA,OAAA,KAAA,IAAA,QAAS,YAAA,CAAa,GAAA,MAAQ,cACrC,OAAA,CAAO,WAAA,OAAA,KAAA,IAAA,QAAS,GAAA,MAAQ;IAE5B,EAAA,OAAQ;QACN,OAAO;IACT;AACF;AAEO,SAAS,gBAAgB,GAAA,EAA8B;IAC5D,IAAI;QACF,MAAM,EAAE,OAAA,CAAQ,CAAA,GAAK,OAAO,CAAC;QAC7B,OAAO,OAAA,CAAO,WAAA,OAAA,KAAA,IAAA,QAAS,GAAA,MAAQ;IACjC,EAAA,OAAQ;QACN,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 264, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/constants.ts"], "sourcesContent": ["const Headers = {\n  NextRewrite: 'x-middleware-rewrite',\n  NextResume: 'x-middleware-next',\n  NextRedirect: 'Location',\n  // Used by next to identify internal navigation for app router\n  NextUrl: 'next-url',\n  NextAction: 'next-action',\n  // Used by next to identify internal navigation for pages router\n  NextjsData: 'x-nextjs-data',\n} as const;\n\nexport const constants = {\n  Headers,\n} as const;\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,UAAU;IACd,aAAa;IACb,YAAY;IACZ,cAAc;IAAA,8DAAA;IAEd,SAAS;IACT,YAAY;IAAA,gEAAA;IAEZ,YAAY;AACd;AAEO,MAAM,YAAY;IACvB;AACF", "debugId": null}}, {"offset": {"line": 288, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/utils/feature-flags.ts"], "sourcesContent": ["import { isDevelopmentEnvironment } from '@clerk/shared/utils';\n\nimport { KEYLESS_DISABLED } from '../server/constants';\nimport { isNextWithUnstableServerActions } from './sdk-versions';\n\nconst canUseKeyless =\n  !isNextWithUnstableServerActions &&\n  // Next.js will inline the value of 'development' or 'production' on the client bundle, so this is client-safe.\n  isDevelopmentEnvironment() &&\n  !KEYLESS_DISABLED;\n\nexport { canUseKeyless };\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,gCAAgC;AAEzC,SAAS,wBAAwB;AACjC,SAAS,uCAAuC;;;;;AAEhD,MAAM,gBACJ,CAAC,6NAAA,IAAA,+GAAA;IAED,yMAAA,CAAyB,MACzB,CAAC,yMAAA", "debugId": null}}, {"offset": {"line": 308, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/dist/esm/vendor/crypto-es.js"], "sourcesContent": ["var kt=Object.defineProperty;var bt=(c,t,s)=>t in c?kt(c,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):c[t]=s;var it=(c,t,s)=>bt(c,typeof t!=\"symbol\"?t+\"\":t,s);var lt,ht,dt,pt,xt,_t,at=((lt=typeof globalThis!=\"undefined\"?globalThis:void 0)==null?void 0:lt.crypto)||((ht=typeof global!=\"undefined\"?global:void 0)==null?void 0:ht.crypto)||((dt=typeof window!=\"undefined\"?window:void 0)==null?void 0:dt.crypto)||((pt=typeof self!=\"undefined\"?self:void 0)==null?void 0:pt.crypto)||((_t=(xt=typeof frames!=\"undefined\"?frames:void 0)==null?void 0:xt[0])==null?void 0:_t.crypto),Z;at?Z=c=>{let t=[];for(let s=0,e;s<c;s+=4)t.push(at.getRandomValues(new Uint32Array(1))[0]);return new u(t,c)}:Z=c=>{let t=[],s=e=>{let r=e,o=987654321,n=4294967295;return()=>{o=36969*(o&65535)+(o>>16)&n,r=18e3*(r&65535)+(r>>16)&n;let h=(o<<16)+r&n;return h/=4294967296,h+=.5,h*(Math.random()>.5?1:-1)}};for(let e=0,r;e<c;e+=4){let o=s((r||Math.random())*4294967296);r=o()*987654071,t.push(o()*4294967296|0)}return new u(t,c)};var m=class{static create(...t){return new this(...t)}mixIn(t){return Object.assign(this,t)}clone(){let t=new this.constructor;return Object.assign(t,this),t}},u=class extends m{constructor(t=[],s=t.length*4){super();let e=t;if(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),(e instanceof Int8Array||e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array)&&(e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength)),e instanceof Uint8Array){let r=e.byteLength,o=[];for(let n=0;n<r;n+=1)o[n>>>2]|=e[n]<<24-n%4*8;this.words=o,this.sigBytes=r}else this.words=t,this.sigBytes=s}toString(t=Mt){return t.stringify(this)}concat(t){let s=this.words,e=t.words,r=this.sigBytes,o=t.sigBytes;if(this.clamp(),r%4)for(let n=0;n<o;n+=1){let h=e[n>>>2]>>>24-n%4*8&255;s[r+n>>>2]|=h<<24-(r+n)%4*8}else for(let n=0;n<o;n+=4)s[r+n>>>2]=e[n>>>2];return this.sigBytes+=o,this}clamp(){let{words:t,sigBytes:s}=this;t[s>>>2]&=4294967295<<32-s%4*8,t.length=Math.ceil(s/4)}clone(){let t=super.clone.call(this);return t.words=this.words.slice(0),t}};it(u,\"random\",Z);var Mt={stringify(c){let{words:t,sigBytes:s}=c,e=[];for(let r=0;r<s;r+=1){let o=t[r>>>2]>>>24-r%4*8&255;e.push((o>>>4).toString(16)),e.push((o&15).toString(16))}return e.join(\"\")},parse(c){let t=c.length,s=[];for(let e=0;e<t;e+=2)s[e>>>3]|=parseInt(c.substr(e,2),16)<<24-e%8*4;return new u(s,t/2)}},ft={stringify(c){let{words:t,sigBytes:s}=c,e=[];for(let r=0;r<s;r+=1){let o=t[r>>>2]>>>24-r%4*8&255;e.push(String.fromCharCode(o))}return e.join(\"\")},parse(c){let t=c.length,s=[];for(let e=0;e<t;e+=1)s[e>>>2]|=(c.charCodeAt(e)&255)<<24-e%4*8;return new u(s,t)}},X={stringify(c){try{return decodeURIComponent(escape(ft.stringify(c)))}catch{throw new Error(\"Malformed UTF-8 data\")}},parse(c){return ft.parse(unescape(encodeURIComponent(c)))}},N=class extends m{constructor(){super(),this._minBufferSize=0}reset(){this._data=new u,this._nDataBytes=0}_append(t){let s=t;typeof s==\"string\"&&(s=X.parse(s)),this._data.concat(s),this._nDataBytes+=s.sigBytes}_process(t){let s,{_data:e,blockSize:r}=this,o=e.words,n=e.sigBytes,h=r*4,x=n/h;t?x=Math.ceil(x):x=Math.max((x|0)-this._minBufferSize,0);let p=x*r,_=Math.min(p*4,n);if(p){for(let y=0;y<p;y+=r)this._doProcessBlock(o,y);s=o.splice(0,p),e.sigBytes-=_}return new u(s,_)}clone(){let t=super.clone.call(this);return t._data=this._data.clone(),t}},H=class extends N{constructor(t){super(),this.blockSize=512/32,this.cfg=Object.assign(new m,t),this.reset()}static _createHelper(t){return(s,e)=>new t(e).finalize(s)}static _createHmacHelper(t){return(s,e)=>new $(t,e).finalize(s)}reset(){super.reset.call(this),this._doReset()}update(t){return this._append(t),this._process(),this}finalize(t){return t&&this._append(t),this._doFinalize()}},$=class extends m{constructor(t,s){super();let e=new t;this._hasher=e;let r=s;typeof r==\"string\"&&(r=X.parse(r));let o=e.blockSize,n=o*4;r.sigBytes>n&&(r=e.finalize(s)),r.clamp();let h=r.clone();this._oKey=h;let x=r.clone();this._iKey=x;let p=h.words,_=x.words;for(let y=0;y<o;y+=1)p[y]^=1549556828,_[y]^=909522486;h.sigBytes=n,x.sigBytes=n,this.reset()}reset(){let t=this._hasher;t.reset(),t.update(this._iKey)}update(t){return this._hasher.update(t),this}finalize(t){let s=this._hasher,e=s.finalize(t);return s.reset(),s.finalize(this._oKey.clone().concat(e))}};var zt=(c,t,s)=>{let e=[],r=0;for(let o=0;o<t;o+=1)if(o%4){let n=s[c.charCodeAt(o-1)]<<o%4*2,h=s[c.charCodeAt(o)]>>>6-o%4*2,x=n|h;e[r>>>2]|=x<<24-r%4*8,r+=1}return u.create(e,r)},tt={stringify(c){let{words:t,sigBytes:s}=c,e=this._map;c.clamp();let r=[];for(let n=0;n<s;n+=3){let h=t[n>>>2]>>>24-n%4*8&255,x=t[n+1>>>2]>>>24-(n+1)%4*8&255,p=t[n+2>>>2]>>>24-(n+2)%4*8&255,_=h<<16|x<<8|p;for(let y=0;y<4&&n+y*.75<s;y+=1)r.push(e.charAt(_>>>6*(3-y)&63))}let o=e.charAt(64);if(o)for(;r.length%4;)r.push(o);return r.join(\"\")},parse(c){let t=c.length,s=this._map,e=this._reverseMap;if(!e){this._reverseMap=[],e=this._reverseMap;for(let o=0;o<s.length;o+=1)e[s.charCodeAt(o)]=o}let r=s.charAt(64);if(r){let o=c.indexOf(r);o!==-1&&(t=o)}return zt(c,t,e)},_map:\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\"};var d=[];for(let c=0;c<64;c+=1)d[c]=Math.abs(Math.sin(c+1))*4294967296|0;var w=(c,t,s,e,r,o,n)=>{let h=c+(t&s|~t&e)+r+n;return(h<<o|h>>>32-o)+t},B=(c,t,s,e,r,o,n)=>{let h=c+(t&e|s&~e)+r+n;return(h<<o|h>>>32-o)+t},k=(c,t,s,e,r,o,n)=>{let h=c+(t^s^e)+r+n;return(h<<o|h>>>32-o)+t},b=(c,t,s,e,r,o,n)=>{let h=c+(s^(t|~e))+r+n;return(h<<o|h>>>32-o)+t},L=class extends H{_doReset(){this._hash=new u([1732584193,4023233417,2562383102,271733878])}_doProcessBlock(t,s){let e=t;for(let Y=0;Y<16;Y+=1){let ct=s+Y,G=t[ct];e[ct]=(G<<8|G>>>24)&16711935|(G<<24|G>>>8)&4278255360}let r=this._hash.words,o=e[s+0],n=e[s+1],h=e[s+2],x=e[s+3],p=e[s+4],_=e[s+5],y=e[s+6],M=e[s+7],z=e[s+8],v=e[s+9],g=e[s+10],O=e[s+11],S=e[s+12],P=e[s+13],I=e[s+14],W=e[s+15],i=r[0],a=r[1],f=r[2],l=r[3];i=w(i,a,f,l,o,7,d[0]),l=w(l,i,a,f,n,12,d[1]),f=w(f,l,i,a,h,17,d[2]),a=w(a,f,l,i,x,22,d[3]),i=w(i,a,f,l,p,7,d[4]),l=w(l,i,a,f,_,12,d[5]),f=w(f,l,i,a,y,17,d[6]),a=w(a,f,l,i,M,22,d[7]),i=w(i,a,f,l,z,7,d[8]),l=w(l,i,a,f,v,12,d[9]),f=w(f,l,i,a,g,17,d[10]),a=w(a,f,l,i,O,22,d[11]),i=w(i,a,f,l,S,7,d[12]),l=w(l,i,a,f,P,12,d[13]),f=w(f,l,i,a,I,17,d[14]),a=w(a,f,l,i,W,22,d[15]),i=B(i,a,f,l,n,5,d[16]),l=B(l,i,a,f,y,9,d[17]),f=B(f,l,i,a,O,14,d[18]),a=B(a,f,l,i,o,20,d[19]),i=B(i,a,f,l,_,5,d[20]),l=B(l,i,a,f,g,9,d[21]),f=B(f,l,i,a,W,14,d[22]),a=B(a,f,l,i,p,20,d[23]),i=B(i,a,f,l,v,5,d[24]),l=B(l,i,a,f,I,9,d[25]),f=B(f,l,i,a,x,14,d[26]),a=B(a,f,l,i,z,20,d[27]),i=B(i,a,f,l,P,5,d[28]),l=B(l,i,a,f,h,9,d[29]),f=B(f,l,i,a,M,14,d[30]),a=B(a,f,l,i,S,20,d[31]),i=k(i,a,f,l,_,4,d[32]),l=k(l,i,a,f,z,11,d[33]),f=k(f,l,i,a,O,16,d[34]),a=k(a,f,l,i,I,23,d[35]),i=k(i,a,f,l,n,4,d[36]),l=k(l,i,a,f,p,11,d[37]),f=k(f,l,i,a,M,16,d[38]),a=k(a,f,l,i,g,23,d[39]),i=k(i,a,f,l,P,4,d[40]),l=k(l,i,a,f,o,11,d[41]),f=k(f,l,i,a,x,16,d[42]),a=k(a,f,l,i,y,23,d[43]),i=k(i,a,f,l,v,4,d[44]),l=k(l,i,a,f,S,11,d[45]),f=k(f,l,i,a,W,16,d[46]),a=k(a,f,l,i,h,23,d[47]),i=b(i,a,f,l,o,6,d[48]),l=b(l,i,a,f,M,10,d[49]),f=b(f,l,i,a,I,15,d[50]),a=b(a,f,l,i,_,21,d[51]),i=b(i,a,f,l,S,6,d[52]),l=b(l,i,a,f,x,10,d[53]),f=b(f,l,i,a,g,15,d[54]),a=b(a,f,l,i,n,21,d[55]),i=b(i,a,f,l,z,6,d[56]),l=b(l,i,a,f,W,10,d[57]),f=b(f,l,i,a,y,15,d[58]),a=b(a,f,l,i,P,21,d[59]),i=b(i,a,f,l,p,6,d[60]),l=b(l,i,a,f,O,10,d[61]),f=b(f,l,i,a,h,15,d[62]),a=b(a,f,l,i,v,21,d[63]),r[0]=r[0]+i|0,r[1]=r[1]+a|0,r[2]=r[2]+f|0,r[3]=r[3]+l|0}_doFinalize(){let t=this._data,s=t.words,e=this._nDataBytes*8,r=t.sigBytes*8;s[r>>>5]|=128<<24-r%32;let o=Math.floor(e/4294967296),n=e;s[(r+64>>>9<<4)+15]=(o<<8|o>>>24)&16711935|(o<<24|o>>>8)&4278255360,s[(r+64>>>9<<4)+14]=(n<<8|n>>>24)&16711935|(n<<24|n>>>8)&4278255360,t.sigBytes=(s.length+1)*4,this._process();let h=this._hash,x=h.words;for(let p=0;p<4;p+=1){let _=x[p];x[p]=(_<<8|_>>>24)&16711935|(_<<24|_>>>8)&4278255360}return h}clone(){let t=super.clone.call(this);return t._hash=this._hash.clone(),t}},St=H._createHelper(L),Pt=H._createHmacHelper(L);var T=class extends m{constructor(t){super(),this.cfg=Object.assign(new m,{keySize:128/32,hasher:L,iterations:1},t)}compute(t,s){let e,{cfg:r}=this,o=r.hasher.create(),n=u.create(),h=n.words,{keySize:x,iterations:p}=r;for(;h.length<x;){e&&o.update(e),e=o.update(t).finalize(s),o.reset();for(let _=1;_<p;_+=1)e=o.finalize(e),o.reset();n.concat(e)}return n.sigBytes=x*4,n}};var C=class extends N{constructor(t,s,e){super(),this.cfg=Object.assign(new m,e),this._xformMode=t,this._key=s,this.reset()}static createEncryptor(t,s){return this.create(this._ENC_XFORM_MODE,t,s)}static createDecryptor(t,s){return this.create(this._DEC_XFORM_MODE,t,s)}static _createHelper(t){let s=e=>typeof e==\"string\"?q:E;return{encrypt(e,r,o){return s(r).encrypt(t,e,r,o)},decrypt(e,r,o){return s(r).decrypt(t,e,r,o)}}}reset(){super.reset.call(this),this._doReset()}process(t){return this._append(t),this._process()}finalize(t){return t&&this._append(t),this._doFinalize()}};C._ENC_XFORM_MODE=1;C._DEC_XFORM_MODE=2;C.keySize=128/32;C.ivSize=128/32;var et=class extends m{constructor(t,s){super(),this._cipher=t,this._iv=s}static createEncryptor(t,s){return this.Encryptor.create(t,s)}static createDecryptor(t,s){return this.Decryptor.create(t,s)}};function yt(c,t,s){let e=c,r,o=this._iv;o?(r=o,this._iv=void 0):r=this._prevBlock;for(let n=0;n<s;n+=1)e[t+n]^=r[n]}var j=class extends et{};j.Encryptor=class extends j{processBlock(c,t){let s=this._cipher,{blockSize:e}=s;yt.call(this,c,t,e),s.encryptBlock(c,t),this._prevBlock=c.slice(t,t+e)}};j.Decryptor=class extends j{processBlock(c,t){let s=this._cipher,{blockSize:e}=s,r=c.slice(t,t+e);s.decryptBlock(c,t),yt.call(this,c,t,e),this._prevBlock=r}};var vt={pad(c,t){let s=t*4,e=s-c.sigBytes%s,r=e<<24|e<<16|e<<8|e,o=[];for(let h=0;h<e;h+=4)o.push(r);let n=u.create(o,e);c.concat(n)},unpad(c){let t=c,s=t.words[t.sigBytes-1>>>2]&255;t.sigBytes-=s}},U=class extends C{constructor(t,s,e){super(t,s,Object.assign({mode:j,padding:vt},e)),this.blockSize=128/32}reset(){let t;super.reset.call(this);let{cfg:s}=this,{iv:e,mode:r}=s;this._xformMode===this.constructor._ENC_XFORM_MODE?t=r.createEncryptor:(t=r.createDecryptor,this._minBufferSize=1),this._mode=t.call(r,this,e&&e.words),this._mode.__creator=t}_doProcessBlock(t,s){this._mode.processBlock(t,s)}_doFinalize(){let t,{padding:s}=this.cfg;return this._xformMode===this.constructor._ENC_XFORM_MODE?(s.pad(this._data,this.blockSize),t=this._process(!0)):(t=this._process(!0),s.unpad(t)),t}},V=class extends m{constructor(t){super(),this.mixIn(t)}toString(t){return(t||this.formatter).stringify(this)}},Rt={stringify(c){let t,{ciphertext:s,salt:e}=c;return e?t=u.create([1398893684,1701076831]).concat(e).concat(s):t=s,t.toString(tt)},parse(c){let t,s=tt.parse(c),e=s.words;return e[0]===1398893684&&e[1]===1701076831&&(t=u.create(e.slice(2,4)),e.splice(0,4),s.sigBytes-=16),V.create({ciphertext:s,salt:t})}},E=class extends m{static encrypt(t,s,e,r){let o=Object.assign(new m,this.cfg,r),n=t.createEncryptor(e,o),h=n.finalize(s),x=n.cfg;return V.create({ciphertext:h,key:e,iv:x.iv,algorithm:t,mode:x.mode,padding:x.padding,blockSize:n.blockSize,formatter:o.format})}static decrypt(t,s,e,r){let o=s,n=Object.assign(new m,this.cfg,r);return o=this._parse(o,n.format),t.createDecryptor(e,n).finalize(o.ciphertext)}static _parse(t,s){return typeof t==\"string\"?s.parse(t,this):t}};E.cfg=Object.assign(new m,{format:Rt});var Ft={execute(c,t,s,e,r){let o=e;o||(o=u.random(64/8));let n;r?n=T.create({keySize:t+s,hasher:r}).compute(c,o):n=T.create({keySize:t+s}).compute(c,o);let h=u.create(n.words.slice(t),s*4);return n.sigBytes=t*4,V.create({key:n,iv:h,salt:o})}},q=class extends E{static encrypt(t,s,e,r){let o=Object.assign(new m,this.cfg,r),n=o.kdf.execute(e,t.keySize,t.ivSize,o.salt,o.hasher);o.iv=n.iv;let h=E.encrypt.call(this,t,s,n.key,o);return h.mixIn(n),h}static decrypt(t,s,e,r){let o=s,n=Object.assign(new m,this.cfg,r);o=this._parse(o,n.format);let h=n.kdf.execute(e,t.keySize,t.ivSize,o.salt,n.hasher);return n.iv=h.iv,E.decrypt.call(this,t,o,h.key,n)}};q.cfg=Object.assign(E.cfg,{kdf:Ft});var R=[],ut=[],gt=[],mt=[],wt=[],Bt=[],st=[],rt=[],ot=[],nt=[],A=[];for(let c=0;c<256;c+=1)c<128?A[c]=c<<1:A[c]=c<<1^283;var F=0,D=0;for(let c=0;c<256;c+=1){let t=D^D<<1^D<<2^D<<3^D<<4;t=t>>>8^t&255^99,R[F]=t,ut[t]=F;let s=A[F],e=A[s],r=A[e],o=A[t]*257^t*16843008;gt[F]=o<<24|o>>>8,mt[F]=o<<16|o>>>16,wt[F]=o<<8|o>>>24,Bt[F]=o,o=r*16843009^e*65537^s*257^F*16843008,st[t]=o<<24|o>>>8,rt[t]=o<<16|o>>>16,ot[t]=o<<8|o>>>24,nt[t]=o,F?(F=s^A[A[A[r^s]]],D^=A[A[D]]):(D=1,F=D)}var At=[0,1,2,4,8,16,32,64,128,27,54],J=class extends U{_doReset(){let t;if(this._nRounds&&this._keyPriorReset===this._key)return;this._keyPriorReset=this._key;let s=this._keyPriorReset,e=s.words,r=s.sigBytes/4;this._nRounds=r+6;let n=(this._nRounds+1)*4;this._keySchedule=[];let h=this._keySchedule;for(let p=0;p<n;p+=1)p<r?h[p]=e[p]:(t=h[p-1],p%r?r>6&&p%r===4&&(t=R[t>>>24]<<24|R[t>>>16&255]<<16|R[t>>>8&255]<<8|R[t&255]):(t=t<<8|t>>>24,t=R[t>>>24]<<24|R[t>>>16&255]<<16|R[t>>>8&255]<<8|R[t&255],t^=At[p/r|0]<<24),h[p]=h[p-r]^t);this._invKeySchedule=[];let x=this._invKeySchedule;for(let p=0;p<n;p+=1){let _=n-p;p%4?t=h[_]:t=h[_-4],p<4||_<=4?x[p]=t:x[p]=st[R[t>>>24]]^rt[R[t>>>16&255]]^ot[R[t>>>8&255]]^nt[R[t&255]]}}encryptBlock(t,s){this._doCryptBlock(t,s,this._keySchedule,gt,mt,wt,Bt,R)}decryptBlock(t,s){let e=t,r=e[s+1];e[s+1]=e[s+3],e[s+3]=r,this._doCryptBlock(e,s,this._invKeySchedule,st,rt,ot,nt,ut),r=e[s+1],e[s+1]=e[s+3],e[s+3]=r}_doCryptBlock(t,s,e,r,o,n,h,x){let p=t,_=this._nRounds,y=p[s]^e[0],M=p[s+1]^e[1],z=p[s+2]^e[2],v=p[s+3]^e[3],g=4;for(let W=1;W<_;W+=1){let i=r[y>>>24]^o[M>>>16&255]^n[z>>>8&255]^h[v&255]^e[g];g+=1;let a=r[M>>>24]^o[z>>>16&255]^n[v>>>8&255]^h[y&255]^e[g];g+=1;let f=r[z>>>24]^o[v>>>16&255]^n[y>>>8&255]^h[M&255]^e[g];g+=1;let l=r[v>>>24]^o[y>>>16&255]^n[M>>>8&255]^h[z&255]^e[g];g+=1,y=i,M=a,z=f,v=l}let O=(x[y>>>24]<<24|x[M>>>16&255]<<16|x[z>>>8&255]<<8|x[v&255])^e[g];g+=1;let S=(x[M>>>24]<<24|x[z>>>16&255]<<16|x[v>>>8&255]<<8|x[y&255])^e[g];g+=1;let P=(x[z>>>24]<<24|x[v>>>16&255]<<16|x[y>>>8&255]<<8|x[M&255])^e[g];g+=1;let I=(x[v>>>24]<<24|x[y>>>16&255]<<16|x[M>>>8&255]<<8|x[z&255])^e[g];g+=1,p[s]=O,p[s+1]=S,p[s+2]=P,p[s+3]=I}};J.keySize=256/32;var Ht=U._createHelper(J);var K=[],Q=class extends H{_doReset(){this._hash=new u([1732584193,4023233417,2562383102,271733878,3285377520])}_doProcessBlock(t,s){let e=this._hash.words,r=e[0],o=e[1],n=e[2],h=e[3],x=e[4];for(let p=0;p<80;p+=1){if(p<16)K[p]=t[s+p]|0;else{let y=K[p-3]^K[p-8]^K[p-14]^K[p-16];K[p]=y<<1|y>>>31}let _=(r<<5|r>>>27)+x+K[p];p<20?_+=(o&n|~o&h)+1518500249:p<40?_+=(o^n^h)+1859775393:p<60?_+=(o&n|o&h|n&h)-1894007588:_+=(o^n^h)-899497514,x=h,h=n,n=o<<30|o>>>2,o=r,r=_}e[0]=e[0]+r|0,e[1]=e[1]+o|0,e[2]=e[2]+n|0,e[3]=e[3]+h|0,e[4]=e[4]+x|0}_doFinalize(){let t=this._data,s=t.words,e=this._nDataBytes*8,r=t.sigBytes*8;return s[r>>>5]|=128<<24-r%32,s[(r+64>>>9<<4)+14]=Math.floor(e/4294967296),s[(r+64>>>9<<4)+15]=e,t.sigBytes=s.length*4,this._process(),this._hash}clone(){let t=super.clone.call(this);return t._hash=this._hash.clone(),t}},Xt=H._createHelper(Q),Dt=H._createHmacHelper(Q);export{Ht as AES,Dt as HmacSHA1,X as Utf8};\n"], "names": [], "mappings": ";;;;;;;;AAAA,IAAI,KAAG,OAAO,cAAc;AAAC,IAAI,KAAG,CAAC,GAAE,GAAE,IAAI,KAAK,IAAE,GAAG,GAAE,GAAE;QAAC,YAAW,CAAC;QAAE,cAAa,CAAC;QAAE,UAAS,CAAC;QAAE,OAAM;IAAC,KAAG,CAAC,CAAC,EAAE,GAAC;AAAE,IAAI,KAAG,CAAC,GAAE,GAAE,IAAI,GAAG,GAAE,OAAO,KAAG,WAAS,IAAE,KAAG,GAAE;AAAG,IAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAG,CAAC,CAAC,KAAG,OAAO,cAAY,cAAY,aAAW,KAAK,CAAC,KAAG,OAAK,KAAK,IAAE,GAAG,MAAM,KAAG,CAAC,CAAC,KAAG,kGAAkC,uBAAM,KAAG,OAAK,KAAK,IAAE,GAAG,MAAM,KAAG,CAAC,CAAC,KAAG,sCAA2B,0BAAO,KAAK,CAAC,KAAG,OAAK,KAAK,IAAE,GAAG,MAAM,KAAG,CAAC,CAAC,KAAG,OAAO,QAAM,cAAY,OAAK,KAAK,CAAC,KAAG,OAAK,KAAK,IAAE,GAAG,MAAM,KAAG,CAAC,CAAC,KAAG,CAAC,KAAG,OAAO,UAAQ,cAAY,SAAO,KAAK,CAAC,KAAG,OAAK,KAAK,IAAE,EAAE,CAAC,EAAE,KAAG,OAAK,KAAK,IAAE,GAAG,MAAM,GAAE;AAAE,KAAG,IAAE,CAAA;IAAI,IAAI,IAAE,EAAE;IAAC,IAAI,IAAI,IAAE,GAAE,GAAE,IAAE,GAAE,KAAG,EAAE,EAAE,IAAI,CAAC,GAAG,eAAe,CAAC,IAAI,YAAY,GAAG,CAAC,EAAE;IAAE,OAAO,IAAI,EAAE,GAAE;AAAE,IAAE,IAAE,CAAA;IAAI,IAAI,IAAE,EAAE,EAAC,IAAE,CAAA;QAAI,IAAI,IAAE,GAAE,IAAE,WAAU,IAAE;QAAW,OAAM;YAAK,IAAE,QAAM,CAAC,IAAE,KAAK,IAAE,CAAC,KAAG,EAAE,IAAE,GAAE,IAAE,OAAK,CAAC,IAAE,KAAK,IAAE,CAAC,KAAG,EAAE,IAAE;YAAE,IAAI,IAAE,CAAC,KAAG,EAAE,IAAE,IAAE;YAAE,OAAO,KAAG,YAAW,KAAG,IAAG,IAAE,CAAC,KAAK,MAAM,KAAG,KAAG,IAAE,CAAC,CAAC;QAAC;IAAC;IAAE,IAAI,IAAI,IAAE,GAAE,GAAE,IAAE,GAAE,KAAG,EAAE;QAAC,IAAI,IAAE,EAAE,CAAC,KAAG,KAAK,MAAM,EAAE,IAAE;QAAY,IAAE,MAAI,WAAU,EAAE,IAAI,CAAC,MAAI,aAAW;IAAE;IAAC,OAAO,IAAI,EAAE,GAAE;AAAE;AAAE,IAAI,IAAE;IAAM,OAAO,OAAO,GAAG,CAAC,EAAC;QAAC,OAAO,IAAI,IAAI,IAAI;IAAE;IAAC,MAAM,CAAC,EAAC;QAAC,OAAO,OAAO,MAAM,CAAC,IAAI,EAAC;IAAE;IAAC,QAAO;QAAC,IAAI,IAAE,IAAI,IAAI,CAAC,WAAW;QAAC,OAAO,OAAO,MAAM,CAAC,GAAE,IAAI,GAAE;IAAC;AAAC,GAAE,IAAE,cAAc;IAAE,YAAY,IAAE,EAAE,EAAC,IAAE,EAAE,MAAM,GAAC,CAAC,CAAC;QAAC,KAAK;QAAG,IAAI,IAAE;QAAE,IAAG,aAAa,eAAa,CAAC,IAAE,IAAI,WAAW,EAAE,GAAE,CAAC,aAAa,aAAW,aAAa,qBAAmB,aAAa,cAAY,aAAa,eAAa,aAAa,cAAY,aAAa,eAAa,aAAa,gBAAc,aAAa,YAAY,KAAG,CAAC,IAAE,IAAI,WAAW,EAAE,MAAM,EAAC,EAAE,UAAU,EAAC,EAAE,UAAU,CAAC,GAAE,aAAa,YAAW;YAAC,IAAI,IAAE,EAAE,UAAU,EAAC,IAAE,EAAE;YAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE,CAAC,CAAC,MAAI,EAAE,IAAE,CAAC,CAAC,EAAE,IAAE,KAAG,IAAE,IAAE;YAAE,IAAI,CAAC,KAAK,GAAC,GAAE,IAAI,CAAC,QAAQ,GAAC;QAAC,OAAM,IAAI,CAAC,KAAK,GAAC,GAAE,IAAI,CAAC,QAAQ,GAAC;IAAC;IAAC,SAAS,IAAE,EAAE,EAAC;QAAC,OAAO,EAAE,SAAS,CAAC,IAAI;IAAC;IAAC,OAAO,CAAC,EAAC;QAAC,IAAI,IAAE,IAAI,CAAC,KAAK,EAAC,IAAE,EAAE,KAAK,EAAC,IAAE,IAAI,CAAC,QAAQ,EAAC,IAAE,EAAE,QAAQ;QAAC,IAAG,IAAI,CAAC,KAAK,IAAG,IAAE,GAAE,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE;YAAC,IAAI,IAAE,CAAC,CAAC,MAAI,EAAE,KAAG,KAAG,IAAE,IAAE,IAAE;YAAI,CAAC,CAAC,IAAE,MAAI,EAAE,IAAE,KAAG,KAAG,CAAC,IAAE,CAAC,IAAE,IAAE;QAAC;aAAM,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE,CAAC,CAAC,IAAE,MAAI,EAAE,GAAC,CAAC,CAAC,MAAI,EAAE;QAAC,OAAO,IAAI,CAAC,QAAQ,IAAE,GAAE,IAAI;IAAA;IAAC,QAAO;QAAC,IAAG,EAAC,OAAM,CAAC,EAAC,UAAS,CAAC,EAAC,GAAC,IAAI;QAAC,CAAC,CAAC,MAAI,EAAE,IAAE,cAAY,KAAG,IAAE,IAAE,GAAE,EAAE,MAAM,GAAC,KAAK,IAAI,CAAC,IAAE;IAAE;IAAC,QAAO;QAAC,IAAI,IAAE,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI;QAAE,OAAO,EAAE,KAAK,GAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAG;IAAC;AAAC;AAAE,GAAG,GAAE,UAAS;AAAG,IAAI,KAAG;IAAC,WAAU,CAAC;QAAE,IAAG,EAAC,OAAM,CAAC,EAAC,UAAS,CAAC,EAAC,GAAC,GAAE,IAAE,EAAE;QAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE;YAAC,IAAI,IAAE,CAAC,CAAC,MAAI,EAAE,KAAG,KAAG,IAAE,IAAE,IAAE;YAAI,EAAE,IAAI,CAAC,CAAC,MAAI,CAAC,EAAE,QAAQ,CAAC,MAAK,EAAE,IAAI,CAAC,CAAC,IAAE,EAAE,EAAE,QAAQ,CAAC;QAAI;QAAC,OAAO,EAAE,IAAI,CAAC;IAAG;IAAE,OAAM,CAAC;QAAE,IAAI,IAAE,EAAE,MAAM,EAAC,IAAE,EAAE;QAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE,CAAC,CAAC,MAAI,EAAE,IAAE,SAAS,EAAE,MAAM,CAAC,GAAE,IAAG,OAAK,KAAG,IAAE,IAAE;QAAE,OAAO,IAAI,EAAE,GAAE,IAAE;IAAE;AAAC,GAAE,KAAG;IAAC,WAAU,CAAC;QAAE,IAAG,EAAC,OAAM,CAAC,EAAC,UAAS,CAAC,EAAC,GAAC,GAAE,IAAE,EAAE;QAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE;YAAC,IAAI,IAAE,CAAC,CAAC,MAAI,EAAE,KAAG,KAAG,IAAE,IAAE,IAAE;YAAI,EAAE,IAAI,CAAC,OAAO,YAAY,CAAC;QAAG;QAAC,OAAO,EAAE,IAAI,CAAC;IAAG;IAAE,OAAM,CAAC;QAAE,IAAI,IAAE,EAAE,MAAM,EAAC,IAAE,EAAE;QAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE,CAAC,CAAC,MAAI,EAAE,IAAE,CAAC,EAAE,UAAU,CAAC,KAAG,GAAG,KAAG,KAAG,IAAE,IAAE;QAAE,OAAO,IAAI,EAAE,GAAE;IAAE;AAAC,GAAE,IAAE;IAAC,WAAU,CAAC;QAAE,IAAG;YAAC,OAAO,mBAAmB,OAAO,GAAG,SAAS,CAAC;QAAI,EAAC,OAAK;YAAC,MAAM,IAAI,MAAM;QAAuB;IAAC;IAAE,OAAM,CAAC;QAAE,OAAO,GAAG,KAAK,CAAC,SAAS,mBAAmB;IAAI;AAAC,GAAE,IAAE,cAAc;IAAE,aAAa;QAAC,KAAK,IAAG,IAAI,CAAC,cAAc,GAAC;IAAC;IAAC,QAAO;QAAC,IAAI,CAAC,KAAK,GAAC,IAAI,GAAE,IAAI,CAAC,WAAW,GAAC;IAAC;IAAC,QAAQ,CAAC,EAAC;QAAC,IAAI,IAAE;QAAE,OAAO,KAAG,YAAU,CAAC,IAAE,EAAE,KAAK,CAAC,EAAE,GAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAG,IAAI,CAAC,WAAW,IAAE,EAAE,QAAQ;IAAA;IAAC,SAAS,CAAC,EAAC;QAAC,IAAI,GAAE,EAAC,OAAM,CAAC,EAAC,WAAU,CAAC,EAAC,GAAC,IAAI,EAAC,IAAE,EAAE,KAAK,EAAC,IAAE,EAAE,QAAQ,EAAC,IAAE,IAAE,GAAE,IAAE,IAAE;QAAE,IAAE,IAAE,KAAK,IAAI,CAAC,KAAG,IAAE,KAAK,GAAG,CAAC,CAAC,IAAE,CAAC,IAAE,IAAI,CAAC,cAAc,EAAC;QAAG,IAAI,IAAE,IAAE,GAAE,IAAE,KAAK,GAAG,CAAC,IAAE,GAAE;QAAG,IAAG,GAAE;YAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE,IAAI,CAAC,eAAe,CAAC,GAAE;YAAG,IAAE,EAAE,MAAM,CAAC,GAAE,IAAG,EAAE,QAAQ,IAAE;QAAC;QAAC,OAAO,IAAI,EAAE,GAAE;IAAE;IAAC,QAAO;QAAC,IAAI,IAAE,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI;QAAE,OAAO,EAAE,KAAK,GAAC,IAAI,CAAC,KAAK,CAAC,KAAK,IAAG;IAAC;AAAC,GAAE,IAAE,cAAc;IAAE,YAAY,CAAC,CAAC;QAAC,KAAK,IAAG,IAAI,CAAC,SAAS,GAAC,MAAI,IAAG,IAAI,CAAC,GAAG,GAAC,OAAO,MAAM,CAAC,IAAI,GAAE,IAAG,IAAI,CAAC,KAAK;IAAE;IAAC,OAAO,cAAc,CAAC,EAAC;QAAC,OAAM,CAAC,GAAE,IAAI,IAAI,EAAE,GAAG,QAAQ,CAAC;IAAE;IAAC,OAAO,kBAAkB,CAAC,EAAC;QAAC,OAAM,CAAC,GAAE,IAAI,IAAI,EAAE,GAAE,GAAG,QAAQ,CAAC;IAAE;IAAC,QAAO;QAAC,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,GAAE,IAAI,CAAC,QAAQ;IAAE;IAAC,OAAO,CAAC,EAAC;QAAC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAG,IAAI,CAAC,QAAQ,IAAG,IAAI;IAAA;IAAC,SAAS,CAAC,EAAC;QAAC,OAAO,KAAG,IAAI,CAAC,OAAO,CAAC,IAAG,IAAI,CAAC,WAAW;IAAE;AAAC,GAAE,IAAE,cAAc;IAAE,YAAY,CAAC,EAAC,CAAC,CAAC;QAAC,KAAK;QAAG,IAAI,IAAE,IAAI;QAAE,IAAI,CAAC,OAAO,GAAC;QAAE,IAAI,IAAE;QAAE,OAAO,KAAG,YAAU,CAAC,IAAE,EAAE,KAAK,CAAC,EAAE;QAAE,IAAI,IAAE,EAAE,SAAS,EAAC,IAAE,IAAE;QAAE,EAAE,QAAQ,GAAC,KAAG,CAAC,IAAE,EAAE,QAAQ,CAAC,EAAE,GAAE,EAAE,KAAK;QAAG,IAAI,IAAE,EAAE,KAAK;QAAG,IAAI,CAAC,KAAK,GAAC;QAAE,IAAI,IAAE,EAAE,KAAK;QAAG,IAAI,CAAC,KAAK,GAAC;QAAE,IAAI,IAAE,EAAE,KAAK,EAAC,IAAE,EAAE,KAAK;QAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE,CAAC,CAAC,EAAE,IAAE,YAAW,CAAC,CAAC,EAAE,IAAE;QAAU,EAAE,QAAQ,GAAC,GAAE,EAAE,QAAQ,GAAC,GAAE,IAAI,CAAC,KAAK;IAAE;IAAC,QAAO;QAAC,IAAI,IAAE,IAAI,CAAC,OAAO;QAAC,EAAE,KAAK,IAAG,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK;IAAC;IAAC,OAAO,CAAC,EAAC;QAAC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAG,IAAI;IAAA;IAAC,SAAS,CAAC,EAAC;QAAC,IAAI,IAAE,IAAI,CAAC,OAAO,EAAC,IAAE,EAAE,QAAQ,CAAC;QAAG,OAAO,EAAE,KAAK,IAAG,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC;IAAG;AAAC;AAAE,IAAI,KAAG,CAAC,GAAE,GAAE;IAAK,IAAI,IAAE,EAAE,EAAC,IAAE;IAAE,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE,IAAG,IAAE,GAAE;QAAC,IAAI,IAAE,CAAC,CAAC,EAAE,UAAU,CAAC,IAAE,GAAG,IAAE,IAAE,IAAE,GAAE,IAAE,CAAC,CAAC,EAAE,UAAU,CAAC,GAAG,KAAG,IAAE,IAAE,IAAE,GAAE,IAAE,IAAE;QAAE,CAAC,CAAC,MAAI,EAAE,IAAE,KAAG,KAAG,IAAE,IAAE,GAAE,KAAG;IAAC;IAAC,OAAO,EAAE,MAAM,CAAC,GAAE;AAAE,GAAE,KAAG;IAAC,WAAU,CAAC;QAAE,IAAG,EAAC,OAAM,CAAC,EAAC,UAAS,CAAC,EAAC,GAAC,GAAE,IAAE,IAAI,CAAC,IAAI;QAAC,EAAE,KAAK;QAAG,IAAI,IAAE,EAAE;QAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE;YAAC,IAAI,IAAE,CAAC,CAAC,MAAI,EAAE,KAAG,KAAG,IAAE,IAAE,IAAE,KAAI,IAAE,CAAC,CAAC,IAAE,MAAI,EAAE,KAAG,KAAG,CAAC,IAAE,CAAC,IAAE,IAAE,IAAE,KAAI,IAAE,CAAC,CAAC,IAAE,MAAI,EAAE,KAAG,KAAG,CAAC,IAAE,CAAC,IAAE,IAAE,IAAE,KAAI,IAAE,KAAG,KAAG,KAAG,IAAE;YAAE,IAAI,IAAI,IAAE,GAAE,IAAE,KAAG,IAAE,IAAE,MAAI,GAAE,KAAG,EAAE,EAAE,IAAI,CAAC,EAAE,MAAM,CAAC,MAAI,IAAE,CAAC,IAAE,CAAC,IAAE;QAAI;QAAC,IAAI,IAAE,EAAE,MAAM,CAAC;QAAI,IAAG,GAAE,MAAK,EAAE,MAAM,GAAC,GAAG,EAAE,IAAI,CAAC;QAAG,OAAO,EAAE,IAAI,CAAC;IAAG;IAAE,OAAM,CAAC;QAAE,IAAI,IAAE,EAAE,MAAM,EAAC,IAAE,IAAI,CAAC,IAAI,EAAC,IAAE,IAAI,CAAC,WAAW;QAAC,IAAG,CAAC,GAAE;YAAC,IAAI,CAAC,WAAW,GAAC,EAAE,EAAC,IAAE,IAAI,CAAC,WAAW;YAAC,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,KAAG,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC,GAAG,GAAC;QAAC;QAAC,IAAI,IAAE,EAAE,MAAM,CAAC;QAAI,IAAG,GAAE;YAAC,IAAI,IAAE,EAAE,OAAO,CAAC;YAAG,MAAI,CAAC,KAAG,CAAC,IAAE,CAAC;QAAC;QAAC,OAAO,GAAG,GAAE,GAAE;IAAE;IAAE,MAAK;AAAmE;AAAE,IAAI,IAAE,EAAE;AAAC,IAAI,IAAI,IAAE,GAAE,IAAE,IAAG,KAAG,EAAE,CAAC,CAAC,EAAE,GAAC,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,IAAE,MAAI,aAAW;AAAE,IAAI,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;IAAK,IAAI,IAAE,IAAE,CAAC,IAAE,IAAE,CAAC,IAAE,CAAC,IAAE,IAAE;IAAE,OAAM,CAAC,KAAG,IAAE,MAAI,KAAG,CAAC,IAAE;AAAC,GAAE,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;IAAK,IAAI,IAAE,IAAE,CAAC,IAAE,IAAE,IAAE,CAAC,CAAC,IAAE,IAAE;IAAE,OAAM,CAAC,KAAG,IAAE,MAAI,KAAG,CAAC,IAAE;AAAC,GAAE,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;IAAK,IAAI,IAAE,IAAE,CAAC,IAAE,IAAE,CAAC,IAAE,IAAE;IAAE,OAAM,CAAC,KAAG,IAAE,MAAI,KAAG,CAAC,IAAE;AAAC,GAAE,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;IAAK,IAAI,IAAE,IAAE,CAAC,IAAE,CAAC,IAAE,CAAC,CAAC,CAAC,IAAE,IAAE;IAAE,OAAM,CAAC,KAAG,IAAE,MAAI,KAAG,CAAC,IAAE;AAAC,GAAE,IAAE,cAAc;IAAE,WAAU;QAAC,IAAI,CAAC,KAAK,GAAC,IAAI,EAAE;YAAC;YAAW;YAAW;YAAW;SAAU;IAAC;IAAC,gBAAgB,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,IAAE;QAAE,IAAI,IAAI,IAAE,GAAE,IAAE,IAAG,KAAG,EAAE;YAAC,IAAI,KAAG,IAAE,GAAE,IAAE,CAAC,CAAC,GAAG;YAAC,CAAC,CAAC,GAAG,GAAC,CAAC,KAAG,IAAE,MAAI,EAAE,IAAE,WAAS,CAAC,KAAG,KAAG,MAAI,CAAC,IAAE;QAAU;QAAC,IAAI,IAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,GAAG,EAAC,IAAE,CAAC,CAAC,IAAE,GAAG,EAAC,IAAE,CAAC,CAAC,IAAE,GAAG,EAAC,IAAE,CAAC,CAAC,IAAE,GAAG,EAAC,IAAE,CAAC,CAAC,IAAE,GAAG,EAAC,IAAE,CAAC,CAAC,IAAE,GAAG,EAAC,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,EAAE;QAAC,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,EAAE,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,EAAE,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,EAAE,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,EAAE,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,EAAE,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,EAAE,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,EAAE,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,EAAE,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,EAAE,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,EAAE,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,CAAC,CAAC,GAAG,GAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,IAAE,GAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,IAAE,GAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,IAAE,GAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,IAAE;IAAC;IAAC,cAAa;QAAC,IAAI,IAAE,IAAI,CAAC,KAAK,EAAC,IAAE,EAAE,KAAK,EAAC,IAAE,IAAI,CAAC,WAAW,GAAC,GAAE,IAAE,EAAE,QAAQ,GAAC;QAAE,CAAC,CAAC,MAAI,EAAE,IAAE,OAAK,KAAG,IAAE;QAAG,IAAI,IAAE,KAAK,KAAK,CAAC,IAAE,aAAY,IAAE;QAAE,CAAC,CAAC,CAAC,IAAE,OAAK,KAAG,CAAC,IAAE,GAAG,GAAC,CAAC,KAAG,IAAE,MAAI,EAAE,IAAE,WAAS,CAAC,KAAG,KAAG,MAAI,CAAC,IAAE,YAAW,CAAC,CAAC,CAAC,IAAE,OAAK,KAAG,CAAC,IAAE,GAAG,GAAC,CAAC,KAAG,IAAE,MAAI,EAAE,IAAE,WAAS,CAAC,KAAG,KAAG,MAAI,CAAC,IAAE,YAAW,EAAE,QAAQ,GAAC,CAAC,EAAE,MAAM,GAAC,CAAC,IAAE,GAAE,IAAI,CAAC,QAAQ;QAAG,IAAI,IAAE,IAAI,CAAC,KAAK,EAAC,IAAE,EAAE,KAAK;QAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE;YAAC,IAAI,IAAE,CAAC,CAAC,EAAE;YAAC,CAAC,CAAC,EAAE,GAAC,CAAC,KAAG,IAAE,MAAI,EAAE,IAAE,WAAS,CAAC,KAAG,KAAG,MAAI,CAAC,IAAE;QAAU;QAAC,OAAO;IAAC;IAAC,QAAO;QAAC,IAAI,IAAE,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI;QAAE,OAAO,EAAE,KAAK,GAAC,IAAI,CAAC,KAAK,CAAC,KAAK,IAAG;IAAC;AAAC,GAAE,KAAG,EAAE,aAAa,CAAC,IAAG,KAAG,EAAE,iBAAiB,CAAC;AAAG,IAAI,IAAE,cAAc;IAAE,YAAY,CAAC,CAAC;QAAC,KAAK,IAAG,IAAI,CAAC,GAAG,GAAC,OAAO,MAAM,CAAC,IAAI,GAAE;YAAC,SAAQ,MAAI;YAAG,QAAO;YAAE,YAAW;QAAC,GAAE;IAAE;IAAC,QAAQ,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,GAAE,EAAC,KAAI,CAAC,EAAC,GAAC,IAAI,EAAC,IAAE,EAAE,MAAM,CAAC,MAAM,IAAG,IAAE,EAAE,MAAM,IAAG,IAAE,EAAE,KAAK,EAAC,EAAC,SAAQ,CAAC,EAAC,YAAW,CAAC,EAAC,GAAC;QAAE,MAAK,EAAE,MAAM,GAAC,GAAG;YAAC,KAAG,EAAE,MAAM,CAAC,IAAG,IAAE,EAAE,MAAM,CAAC,GAAG,QAAQ,CAAC,IAAG,EAAE,KAAK;YAAG,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE,IAAE,EAAE,QAAQ,CAAC,IAAG,EAAE,KAAK;YAAG,EAAE,MAAM,CAAC;QAAE;QAAC,OAAO,EAAE,QAAQ,GAAC,IAAE,GAAE;IAAC;AAAC;AAAE,IAAI,IAAE,cAAc;IAAE,YAAY,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC;QAAC,KAAK,IAAG,IAAI,CAAC,GAAG,GAAC,OAAO,MAAM,CAAC,IAAI,GAAE,IAAG,IAAI,CAAC,UAAU,GAAC,GAAE,IAAI,CAAC,IAAI,GAAC,GAAE,IAAI,CAAC,KAAK;IAAE;IAAC,OAAO,gBAAgB,CAAC,EAAC,CAAC,EAAC;QAAC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,EAAC,GAAE;IAAE;IAAC,OAAO,gBAAgB,CAAC,EAAC,CAAC,EAAC;QAAC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,EAAC,GAAE;IAAE;IAAC,OAAO,cAAc,CAAC,EAAC;QAAC,IAAI,IAAE,CAAA,IAAG,OAAO,KAAG,WAAS,IAAE;QAAE,OAAM;YAAC,SAAQ,CAAC,EAAC,CAAC,EAAC,CAAC;gBAAE,OAAO,EAAE,GAAG,OAAO,CAAC,GAAE,GAAE,GAAE;YAAE;YAAE,SAAQ,CAAC,EAAC,CAAC,EAAC,CAAC;gBAAE,OAAO,EAAE,GAAG,OAAO,CAAC,GAAE,GAAE,GAAE;YAAE;QAAC;IAAC;IAAC,QAAO;QAAC,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,GAAE,IAAI,CAAC,QAAQ;IAAE;IAAC,QAAQ,CAAC,EAAC;QAAC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAG,IAAI,CAAC,QAAQ;IAAE;IAAC,SAAS,CAAC,EAAC;QAAC,OAAO,KAAG,IAAI,CAAC,OAAO,CAAC,IAAG,IAAI,CAAC,WAAW;IAAE;AAAC;AAAE,EAAE,eAAe,GAAC;AAAE,EAAE,eAAe,GAAC;AAAE,EAAE,OAAO,GAAC,MAAI;AAAG,EAAE,MAAM,GAAC,MAAI;AAAG,IAAI,KAAG,cAAc;IAAE,YAAY,CAAC,EAAC,CAAC,CAAC;QAAC,KAAK,IAAG,IAAI,CAAC,OAAO,GAAC,GAAE,IAAI,CAAC,GAAG,GAAC;IAAC;IAAC,OAAO,gBAAgB,CAAC,EAAC,CAAC,EAAC;QAAC,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAE;IAAE;IAAC,OAAO,gBAAgB,CAAC,EAAC,CAAC,EAAC;QAAC,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAE;IAAE;AAAC;AAAE,SAAS,GAAG,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,GAAE,GAAE,IAAE,IAAI,CAAC,GAAG;IAAC,IAAE,CAAC,IAAE,GAAE,IAAI,CAAC,GAAG,GAAC,KAAK,CAAC,IAAE,IAAE,IAAI,CAAC,UAAU;IAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE,CAAC,CAAC,IAAE,EAAE,IAAE,CAAC,CAAC,EAAE;AAAA;AAAC,IAAI,IAAE,cAAc;AAAG;AAAE,EAAE,SAAS,GAAC,cAAc;IAAE,aAAa,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,IAAE,IAAI,CAAC,OAAO,EAAC,EAAC,WAAU,CAAC,EAAC,GAAC;QAAE,GAAG,IAAI,CAAC,IAAI,EAAC,GAAE,GAAE,IAAG,EAAE,YAAY,CAAC,GAAE,IAAG,IAAI,CAAC,UAAU,GAAC,EAAE,KAAK,CAAC,GAAE,IAAE;IAAE;AAAC;AAAE,EAAE,SAAS,GAAC,cAAc;IAAE,aAAa,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,IAAE,IAAI,CAAC,OAAO,EAAC,EAAC,WAAU,CAAC,EAAC,GAAC,GAAE,IAAE,EAAE,KAAK,CAAC,GAAE,IAAE;QAAG,EAAE,YAAY,CAAC,GAAE,IAAG,GAAG,IAAI,CAAC,IAAI,EAAC,GAAE,GAAE,IAAG,IAAI,CAAC,UAAU,GAAC;IAAC;AAAC;AAAE,IAAI,KAAG;IAAC,KAAI,CAAC,EAAC,CAAC;QAAE,IAAI,IAAE,IAAE,GAAE,IAAE,IAAE,EAAE,QAAQ,GAAC,GAAE,IAAE,KAAG,KAAG,KAAG,KAAG,KAAG,IAAE,GAAE,IAAE,EAAE;QAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE,EAAE,IAAI,CAAC;QAAG,IAAI,IAAE,EAAE,MAAM,CAAC,GAAE;QAAG,EAAE,MAAM,CAAC;IAAE;IAAE,OAAM,CAAC;QAAE,IAAI,IAAE,GAAE,IAAE,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAC,MAAI,EAAE,GAAC;QAAI,EAAE,QAAQ,IAAE;IAAC;AAAC,GAAE,IAAE,cAAc;IAAE,YAAY,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC;QAAC,KAAK,CAAC,GAAE,GAAE,OAAO,MAAM,CAAC;YAAC,MAAK;YAAE,SAAQ;QAAE,GAAE,KAAI,IAAI,CAAC,SAAS,GAAC,MAAI;IAAE;IAAC,QAAO;QAAC,IAAI;QAAE,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI;QAAE,IAAG,EAAC,KAAI,CAAC,EAAC,GAAC,IAAI,EAAC,EAAC,IAAG,CAAC,EAAC,MAAK,CAAC,EAAC,GAAC;QAAE,IAAI,CAAC,UAAU,KAAG,IAAI,CAAC,WAAW,CAAC,eAAe,GAAC,IAAE,EAAE,eAAe,GAAC,CAAC,IAAE,EAAE,eAAe,EAAC,IAAI,CAAC,cAAc,GAAC,CAAC,GAAE,IAAI,CAAC,KAAK,GAAC,EAAE,IAAI,CAAC,GAAE,IAAI,EAAC,KAAG,EAAE,KAAK,GAAE,IAAI,CAAC,KAAK,CAAC,SAAS,GAAC;IAAC;IAAC,gBAAgB,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,GAAE;IAAE;IAAC,cAAa;QAAC,IAAI,GAAE,EAAC,SAAQ,CAAC,EAAC,GAAC,IAAI,CAAC,GAAG;QAAC,OAAO,IAAI,CAAC,UAAU,KAAG,IAAI,CAAC,WAAW,CAAC,eAAe,GAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,EAAC,IAAI,CAAC,SAAS,GAAE,IAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAE,CAAC,IAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAG,EAAE,KAAK,CAAC,EAAE,GAAE;IAAC;AAAC,GAAE,IAAE,cAAc;IAAE,YAAY,CAAC,CAAC;QAAC,KAAK,IAAG,IAAI,CAAC,KAAK,CAAC;IAAE;IAAC,SAAS,CAAC,EAAC;QAAC,OAAM,CAAC,KAAG,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,IAAI;IAAC;AAAC,GAAE,KAAG;IAAC,WAAU,CAAC;QAAE,IAAI,GAAE,EAAC,YAAW,CAAC,EAAC,MAAK,CAAC,EAAC,GAAC;QAAE,OAAO,IAAE,IAAE,EAAE,MAAM,CAAC;YAAC;YAAW;SAAW,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,KAAG,IAAE,GAAE,EAAE,QAAQ,CAAC;IAAG;IAAE,OAAM,CAAC;QAAE,IAAI,GAAE,IAAE,GAAG,KAAK,CAAC,IAAG,IAAE,EAAE,KAAK;QAAC,OAAO,CAAC,CAAC,EAAE,KAAG,cAAY,CAAC,CAAC,EAAE,KAAG,cAAY,CAAC,IAAE,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,GAAE,KAAI,EAAE,MAAM,CAAC,GAAE,IAAG,EAAE,QAAQ,IAAE,EAAE,GAAE,EAAE,MAAM,CAAC;YAAC,YAAW;YAAE,MAAK;QAAC;IAAE;AAAC,GAAE,IAAE,cAAc;IAAE,OAAO,QAAQ,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,IAAE,OAAO,MAAM,CAAC,IAAI,GAAE,IAAI,CAAC,GAAG,EAAC,IAAG,IAAE,EAAE,eAAe,CAAC,GAAE,IAAG,IAAE,EAAE,QAAQ,CAAC,IAAG,IAAE,EAAE,GAAG;QAAC,OAAO,EAAE,MAAM,CAAC;YAAC,YAAW;YAAE,KAAI;YAAE,IAAG,EAAE,EAAE;YAAC,WAAU;YAAE,MAAK,EAAE,IAAI;YAAC,SAAQ,EAAE,OAAO;YAAC,WAAU,EAAE,SAAS;YAAC,WAAU,EAAE,MAAM;QAAA;IAAE;IAAC,OAAO,QAAQ,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,IAAE,GAAE,IAAE,OAAO,MAAM,CAAC,IAAI,GAAE,IAAI,CAAC,GAAG,EAAC;QAAG,OAAO,IAAE,IAAI,CAAC,MAAM,CAAC,GAAE,EAAE,MAAM,GAAE,EAAE,eAAe,CAAC,GAAE,GAAG,QAAQ,CAAC,EAAE,UAAU;IAAC;IAAC,OAAO,OAAO,CAAC,EAAC,CAAC,EAAC;QAAC,OAAO,OAAO,KAAG,WAAS,EAAE,KAAK,CAAC,GAAE,IAAI,IAAE;IAAC;AAAC;AAAE,EAAE,GAAG,GAAC,OAAO,MAAM,CAAC,IAAI,GAAE;IAAC,QAAO;AAAE;AAAG,IAAI,KAAG;IAAC,SAAQ,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;QAAE,IAAI,IAAE;QAAE,KAAG,CAAC,IAAE,EAAE,MAAM,CAAC,KAAG,EAAE;QAAE,IAAI;QAAE,IAAE,IAAE,EAAE,MAAM,CAAC;YAAC,SAAQ,IAAE;YAAE,QAAO;QAAC,GAAG,OAAO,CAAC,GAAE,KAAG,IAAE,EAAE,MAAM,CAAC;YAAC,SAAQ,IAAE;QAAC,GAAG,OAAO,CAAC,GAAE;QAAG,IAAI,IAAE,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,IAAG,IAAE;QAAG,OAAO,EAAE,QAAQ,GAAC,IAAE,GAAE,EAAE,MAAM,CAAC;YAAC,KAAI;YAAE,IAAG;YAAE,MAAK;QAAC;IAAE;AAAC,GAAE,IAAE,cAAc;IAAE,OAAO,QAAQ,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,IAAE,OAAO,MAAM,CAAC,IAAI,GAAE,IAAI,CAAC,GAAG,EAAC,IAAG,IAAE,EAAE,GAAG,CAAC,OAAO,CAAC,GAAE,EAAE,OAAO,EAAC,EAAE,MAAM,EAAC,EAAE,IAAI,EAAC,EAAE,MAAM;QAAE,EAAE,EAAE,GAAC,EAAE,EAAE;QAAC,IAAI,IAAE,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,EAAC,GAAE,GAAE,EAAE,GAAG,EAAC;QAAG,OAAO,EAAE,KAAK,CAAC,IAAG;IAAC;IAAC,OAAO,QAAQ,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,IAAE,GAAE,IAAE,OAAO,MAAM,CAAC,IAAI,GAAE,IAAI,CAAC,GAAG,EAAC;QAAG,IAAE,IAAI,CAAC,MAAM,CAAC,GAAE,EAAE,MAAM;QAAE,IAAI,IAAE,EAAE,GAAG,CAAC,OAAO,CAAC,GAAE,EAAE,OAAO,EAAC,EAAE,MAAM,EAAC,EAAE,IAAI,EAAC,EAAE,MAAM;QAAE,OAAO,EAAE,EAAE,GAAC,EAAE,EAAE,EAAC,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,EAAC,GAAE,GAAE,EAAE,GAAG,EAAC;IAAE;AAAC;AAAE,EAAE,GAAG,GAAC,OAAO,MAAM,CAAC,EAAE,GAAG,EAAC;IAAC,KAAI;AAAE;AAAG,IAAI,IAAE,EAAE,EAAC,KAAG,EAAE,EAAC,KAAG,EAAE,EAAC,KAAG,EAAE,EAAC,KAAG,EAAE,EAAC,KAAG,EAAE,EAAC,KAAG,EAAE,EAAC,KAAG,EAAE,EAAC,KAAG,EAAE,EAAC,KAAG,EAAE,EAAC,IAAE,EAAE;AAAC,IAAI,IAAI,IAAE,GAAE,IAAE,KAAI,KAAG,EAAE,IAAE,MAAI,CAAC,CAAC,EAAE,GAAC,KAAG,IAAE,CAAC,CAAC,EAAE,GAAC,KAAG,IAAE;AAAI,IAAI,IAAE,GAAE,IAAE;AAAE,IAAI,IAAI,IAAE,GAAE,IAAE,KAAI,KAAG,EAAE;IAAC,IAAI,IAAE,IAAE,KAAG,IAAE,KAAG,IAAE,KAAG,IAAE,KAAG;IAAE,IAAE,MAAI,IAAE,IAAE,MAAI,IAAG,CAAC,CAAC,EAAE,GAAC,GAAE,EAAE,CAAC,EAAE,GAAC;IAAE,IAAI,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,EAAE,GAAC,MAAI,IAAE;IAAS,EAAE,CAAC,EAAE,GAAC,KAAG,KAAG,MAAI,GAAE,EAAE,CAAC,EAAE,GAAC,KAAG,KAAG,MAAI,IAAG,EAAE,CAAC,EAAE,GAAC,KAAG,IAAE,MAAI,IAAG,EAAE,CAAC,EAAE,GAAC,GAAE,IAAE,IAAE,WAAS,IAAE,QAAM,IAAE,MAAI,IAAE,UAAS,EAAE,CAAC,EAAE,GAAC,KAAG,KAAG,MAAI,GAAE,EAAE,CAAC,EAAE,GAAC,KAAG,KAAG,MAAI,IAAG,EAAE,CAAC,EAAE,GAAC,KAAG,IAAE,MAAI,IAAG,EAAE,CAAC,EAAE,GAAC,GAAE,IAAE,CAAC,IAAE,IAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAE,EAAE,CAAC,CAAC,EAAC,KAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAE,CAAC,IAAE,GAAE,IAAE,CAAC;AAAC;AAAC,IAAI,KAAG;IAAC;IAAE;IAAE;IAAE;IAAE;IAAE;IAAG;IAAG;IAAG;IAAI;IAAG;CAAG,EAAC,IAAE,cAAc;IAAE,WAAU;QAAC,IAAI;QAAE,IAAG,IAAI,CAAC,QAAQ,IAAE,IAAI,CAAC,cAAc,KAAG,IAAI,CAAC,IAAI,EAAC;QAAO,IAAI,CAAC,cAAc,GAAC,IAAI,CAAC,IAAI;QAAC,IAAI,IAAE,IAAI,CAAC,cAAc,EAAC,IAAE,EAAE,KAAK,EAAC,IAAE,EAAE,QAAQ,GAAC;QAAE,IAAI,CAAC,QAAQ,GAAC,IAAE;QAAE,IAAI,IAAE,CAAC,IAAI,CAAC,QAAQ,GAAC,CAAC,IAAE;QAAE,IAAI,CAAC,YAAY,GAAC,EAAE;QAAC,IAAI,IAAE,IAAI,CAAC,YAAY;QAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE,IAAE,IAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,CAAC,IAAE,CAAC,CAAC,IAAE,EAAE,EAAC,IAAE,IAAE,IAAE,KAAG,IAAE,MAAI,KAAG,CAAC,IAAE,CAAC,CAAC,MAAI,GAAG,IAAE,KAAG,CAAC,CAAC,MAAI,KAAG,IAAI,IAAE,KAAG,CAAC,CAAC,MAAI,IAAE,IAAI,IAAE,IAAE,CAAC,CAAC,IAAE,IAAI,IAAE,CAAC,IAAE,KAAG,IAAE,MAAI,IAAG,IAAE,CAAC,CAAC,MAAI,GAAG,IAAE,KAAG,CAAC,CAAC,MAAI,KAAG,IAAI,IAAE,KAAG,CAAC,CAAC,MAAI,IAAE,IAAI,IAAE,IAAE,CAAC,CAAC,IAAE,IAAI,EAAC,KAAG,EAAE,CAAC,IAAE,IAAE,EAAE,IAAE,EAAE,GAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,IAAE,EAAE,GAAC,CAAC;QAAE,IAAI,CAAC,eAAe,GAAC,EAAE;QAAC,IAAI,IAAE,IAAI,CAAC,eAAe;QAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE;YAAC,IAAI,IAAE,IAAE;YAAE,IAAE,IAAE,IAAE,CAAC,CAAC,EAAE,GAAC,IAAE,CAAC,CAAC,IAAE,EAAE,EAAC,IAAE,KAAG,KAAG,IAAE,CAAC,CAAC,EAAE,GAAC,IAAE,CAAC,CAAC,EAAE,GAAC,EAAE,CAAC,CAAC,CAAC,MAAI,GAAG,CAAC,GAAC,EAAE,CAAC,CAAC,CAAC,MAAI,KAAG,IAAI,CAAC,GAAC,EAAE,CAAC,CAAC,CAAC,MAAI,IAAE,IAAI,CAAC,GAAC,EAAE,CAAC,CAAC,CAAC,IAAE,IAAI,CAAC;QAAA;IAAC;IAAC,aAAa,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,CAAC,aAAa,CAAC,GAAE,GAAE,IAAI,CAAC,YAAY,EAAC,IAAG,IAAG,IAAG,IAAG;IAAE;IAAC,aAAa,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,IAAE,GAAE,IAAE,CAAC,CAAC,IAAE,EAAE;QAAC,CAAC,CAAC,IAAE,EAAE,GAAC,CAAC,CAAC,IAAE,EAAE,EAAC,CAAC,CAAC,IAAE,EAAE,GAAC,GAAE,IAAI,CAAC,aAAa,CAAC,GAAE,GAAE,IAAI,CAAC,eAAe,EAAC,IAAG,IAAG,IAAG,IAAG,KAAI,IAAE,CAAC,CAAC,IAAE,EAAE,EAAC,CAAC,CAAC,IAAE,EAAE,GAAC,CAAC,CAAC,IAAE,EAAE,EAAC,CAAC,CAAC,IAAE,EAAE,GAAC;IAAC;IAAC,cAAc,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,IAAE,GAAE,IAAE,IAAI,CAAC,QAAQ,EAAC,IAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE,GAAC,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE,GAAC,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,IAAE,EAAE,GAAC,CAAC,CAAC,EAAE,EAAC,IAAE;QAAE,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE;YAAC,IAAI,IAAE,CAAC,CAAC,MAAI,GAAG,GAAC,CAAC,CAAC,MAAI,KAAG,IAAI,GAAC,CAAC,CAAC,MAAI,IAAE,IAAI,GAAC,CAAC,CAAC,IAAE,IAAI,GAAC,CAAC,CAAC,EAAE;YAAC,KAAG;YAAE,IAAI,IAAE,CAAC,CAAC,MAAI,GAAG,GAAC,CAAC,CAAC,MAAI,KAAG,IAAI,GAAC,CAAC,CAAC,MAAI,IAAE,IAAI,GAAC,CAAC,CAAC,IAAE,IAAI,GAAC,CAAC,CAAC,EAAE;YAAC,KAAG;YAAE,IAAI,IAAE,CAAC,CAAC,MAAI,GAAG,GAAC,CAAC,CAAC,MAAI,KAAG,IAAI,GAAC,CAAC,CAAC,MAAI,IAAE,IAAI,GAAC,CAAC,CAAC,IAAE,IAAI,GAAC,CAAC,CAAC,EAAE;YAAC,KAAG;YAAE,IAAI,IAAE,CAAC,CAAC,MAAI,GAAG,GAAC,CAAC,CAAC,MAAI,KAAG,IAAI,GAAC,CAAC,CAAC,MAAI,IAAE,IAAI,GAAC,CAAC,CAAC,IAAE,IAAI,GAAC,CAAC,CAAC,EAAE;YAAC,KAAG,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE;QAAC;QAAC,IAAI,IAAE,CAAC,CAAC,CAAC,MAAI,GAAG,IAAE,KAAG,CAAC,CAAC,MAAI,KAAG,IAAI,IAAE,KAAG,CAAC,CAAC,MAAI,IAAE,IAAI,IAAE,IAAE,CAAC,CAAC,IAAE,IAAI,IAAE,CAAC,CAAC,EAAE;QAAC,KAAG;QAAE,IAAI,IAAE,CAAC,CAAC,CAAC,MAAI,GAAG,IAAE,KAAG,CAAC,CAAC,MAAI,KAAG,IAAI,IAAE,KAAG,CAAC,CAAC,MAAI,IAAE,IAAI,IAAE,IAAE,CAAC,CAAC,IAAE,IAAI,IAAE,CAAC,CAAC,EAAE;QAAC,KAAG;QAAE,IAAI,IAAE,CAAC,CAAC,CAAC,MAAI,GAAG,IAAE,KAAG,CAAC,CAAC,MAAI,KAAG,IAAI,IAAE,KAAG,CAAC,CAAC,MAAI,IAAE,IAAI,IAAE,IAAE,CAAC,CAAC,IAAE,IAAI,IAAE,CAAC,CAAC,EAAE;QAAC,KAAG;QAAE,IAAI,IAAE,CAAC,CAAC,CAAC,MAAI,GAAG,IAAE,KAAG,CAAC,CAAC,MAAI,KAAG,IAAI,IAAE,KAAG,CAAC,CAAC,MAAI,IAAE,IAAI,IAAE,IAAE,CAAC,CAAC,IAAE,IAAI,IAAE,CAAC,CAAC,EAAE;QAAC,KAAG,GAAE,CAAC,CAAC,EAAE,GAAC,GAAE,CAAC,CAAC,IAAE,EAAE,GAAC,GAAE,CAAC,CAAC,IAAE,EAAE,GAAC,GAAE,CAAC,CAAC,IAAE,EAAE,GAAC;IAAC;AAAC;AAAE,EAAE,OAAO,GAAC,MAAI;AAAG,IAAI,KAAG,EAAE,aAAa,CAAC;AAAG,IAAI,IAAE,EAAE,EAAC,IAAE,cAAc;IAAE,WAAU;QAAC,IAAI,CAAC,KAAK,GAAC,IAAI,EAAE;YAAC;YAAW;YAAW;YAAW;YAAU;SAAW;IAAC;IAAC,gBAAgB,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,IAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAC,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,EAAE;QAAC,IAAI,IAAI,IAAE,GAAE,IAAE,IAAG,KAAG,EAAE;YAAC,IAAG,IAAE,IAAG,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,IAAE,EAAE,GAAC;iBAAM;gBAAC,IAAI,IAAE,CAAC,CAAC,IAAE,EAAE,GAAC,CAAC,CAAC,IAAE,EAAE,GAAC,CAAC,CAAC,IAAE,GAAG,GAAC,CAAC,CAAC,IAAE,GAAG;gBAAC,CAAC,CAAC,EAAE,GAAC,KAAG,IAAE,MAAI;YAAE;YAAC,IAAI,IAAE,CAAC,KAAG,IAAE,MAAI,EAAE,IAAE,IAAE,CAAC,CAAC,EAAE;YAAC,IAAE,KAAG,KAAG,CAAC,IAAE,IAAE,CAAC,IAAE,CAAC,IAAE,aAAW,IAAE,KAAG,KAAG,CAAC,IAAE,IAAE,CAAC,IAAE,aAAW,IAAE,KAAG,KAAG,CAAC,IAAE,IAAE,IAAE,IAAE,IAAE,CAAC,IAAE,aAAW,KAAG,CAAC,IAAE,IAAE,CAAC,IAAE,WAAU,IAAE,GAAE,IAAE,GAAE,IAAE,KAAG,KAAG,MAAI,GAAE,IAAE,GAAE,IAAE;QAAC;QAAC,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,IAAE,GAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,IAAE,GAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,IAAE,GAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,IAAE,GAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,IAAE;IAAC;IAAC,cAAa;QAAC,IAAI,IAAE,IAAI,CAAC,KAAK,EAAC,IAAE,EAAE,KAAK,EAAC,IAAE,IAAI,CAAC,WAAW,GAAC,GAAE,IAAE,EAAE,QAAQ,GAAC;QAAE,OAAO,CAAC,CAAC,MAAI,EAAE,IAAE,OAAK,KAAG,IAAE,IAAG,CAAC,CAAC,CAAC,IAAE,OAAK,KAAG,CAAC,IAAE,GAAG,GAAC,KAAK,KAAK,CAAC,IAAE,aAAY,CAAC,CAAC,CAAC,IAAE,OAAK,KAAG,CAAC,IAAE,GAAG,GAAC,GAAE,EAAE,QAAQ,GAAC,EAAE,MAAM,GAAC,GAAE,IAAI,CAAC,QAAQ,IAAG,IAAI,CAAC,KAAK;IAAA;IAAC,QAAO;QAAC,IAAI,IAAE,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI;QAAE,OAAO,EAAE,KAAK,GAAC,IAAI,CAAC,KAAK,CAAC,KAAK,IAAG;IAAC;AAAC,GAAE,KAAG,EAAE,aAAa,CAAC,IAAG,KAAG,EAAE,iBAAiB,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 886, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/server/errors.ts"], "sourcesContent": ["export const missingDomainAndProxy = `\nMissing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl.\n\n1) With middleware\n   e.g. export default clerkMiddleware({domain:'YOUR_DOMAIN',isSatellite:true});\n2) With environment variables e.g.\n   NEXT_PUBLIC_CLERK_DOMAIN='YOUR_DOMAIN'\n   NEXT_PUBLIC_CLERK_IS_SATELLITE='true'\n   `;\n\nexport const missingSignInUrlInDev = `\nInvalid signInUrl. A satellite application requires a signInUrl for development instances.\nCheck if signInUrl is missing from your configuration or if it is not an absolute URL\n\n1) With middleware\n   e.g. export default clerkMiddleware({signInUrl:'SOME_URL', isSatellite:true});\n2) With environment variables e.g.\n   NEXT_PUBLIC_CLERK_SIGN_IN_URL='SOME_URL'\n   NEXT_PUBLIC_CLERK_IS_SATELLITE='true'`;\n\nexport const getAuthAuthHeaderMissing = () => authAuthHeaderMissing('getAuth');\n\nexport const authAuthHeaderMissing = (helperName = 'auth', prefixSteps?: string[]) =>\n  `Clerk: ${helperName}() was called but Clerk can't detect usage of clerkMiddleware(). Please ensure the following:\n- ${prefixSteps ? [...prefixSteps, ''].join('\\n- ') : ' '}clerkMiddleware() is used in your Next.js Middleware.\n- Your Middleware matcher is configured to match this route or page.\n- If you are using the src directory, make sure the Middleware file is inside of it.\n\nFor more details, see https://clerk.com/err/auth-middleware\n`;\n\nexport const authSignatureInvalid = `Clerk: Unable to verify request, this usually means the Clerk middleware did not run. Ensure Clerk's middleware is properly integrated and matches the current route. For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware. (code=auth_signature_invalid)`;\n\nexport const encryptionKeyInvalid = `Clerk: Unable to decrypt request data, this usually means the encryption key is invalid. Ensure the encryption key is properly set. For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys. (code=encryption_key_invalid)`;\n\nexport const encryptionKeyInvalidDev = `Clerk: Unable to decrypt request data.\\n\\nRefresh the page if your .env file was just updated. If the issue persists, ensure the encryption key is valid and properly set.\\n\\nFor more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys. (code=encryption_key_invalid)`;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAO,MAAM,wBAAwB,CAAA;;;;;;;;GAAA,CAAA;AAU9B,MAAM,wBAAwB,CAAA;;;;;;;;wCAAA,CAAA;AAU9B,MAAM,2BAA2B,IAAM,sBAAsB,SAAS;AAEtE,MAAM,wBAAwB,CAAC,aAAa,MAAA,EAAQ,cACzD,CAAA,OAAA,EAAU,UAAU,CAAA;EAAA,EAClB,cAAc,CAAC;WAAG;QAAa,EAAE;KAAA,CAAE,IAAA,CAAK,MAAM,IAAI,GAAG,CAAA;;;;;AAAA,CAAA;AAOlD,MAAM,uBAAuB,CAAA,yRAAA,CAAA;AAE7B,MAAM,uBAAuB,CAAA,oQAAA,CAAA;AAE7B,MAAM,0BAA0B,CAAA;;;;gIAAA,CAAA", "debugId": null}}, {"offset": {"line": 945, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/server/errorThrower.ts"], "sourcesContent": ["import { buildErrorThrower } from '@clerk/shared/error';\n\nexport const errorThrower = buildErrorThrower({ packageName: '@clerk/nextjs' });\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,yBAAyB;;;AAE3B,MAAM,mBAAe,kMAAA,EAAkB;IAAE,aAAa;AAAgB,CAAC", "debugId": null}}, {"offset": {"line": 962, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/server/utils.ts"], "sourcesContent": ["import type { AuthObject } from '@clerk/backend';\nimport type { AuthenticateRequestOptions, ClerkRequest, RequestState } from '@clerk/backend/internal';\nimport { constants } from '@clerk/backend/internal';\nimport { isDevelopmentFromSecretKey } from '@clerk/shared/keys';\nimport { logger } from '@clerk/shared/logger';\nimport { isHttpOrHttps } from '@clerk/shared/proxy';\nimport { handleValueOrFn, isProductionEnvironment } from '@clerk/shared/utils';\nimport { NextResponse } from 'next/server';\n\nimport { constants as nextConstants } from '../constants';\nimport { canUseKeyless } from '../utils/feature-flags';\nimport { AES, HmacSHA1, Utf8 } from '../vendor/crypto-es';\nimport { DOMAIN, ENCRYPTION_KEY, IS_SATELLITE, PROXY_URL, SECRET_KEY, SIGN_IN_URL } from './constants';\nimport {\n  authSignatureInvalid,\n  encryptionKeyInvalid,\n  encryptionKeyInvalidDev,\n  missingDomainAndProxy,\n  missingSignInUrlInDev,\n} from './errors';\nimport { errorThrower } from './errorThrower';\nimport { detectClerkMiddleware } from './headers-utils';\nimport type { RequestLike } from './types';\n\nconst OVERRIDE_HEADERS = 'x-middleware-override-headers';\nconst MIDDLEWARE_HEADER_PREFIX = 'x-middleware-request' as string;\n\nexport const setRequestHeadersOnNextResponse = (\n  res: NextResponse | Response,\n  req: Request,\n  newHeaders: Record<string, string>,\n) => {\n  if (!res.headers.get(OVERRIDE_HEADERS)) {\n    // Emulate a user setting overrides by explicitly adding the required nextjs headers\n    // https://github.com/vercel/next.js/pull/41380\n    // @ts-expect-error -- property keys does not exist on type Headers\n    res.headers.set(OVERRIDE_HEADERS, [...req.headers.keys()]);\n    req.headers.forEach((val, key) => {\n      res.headers.set(`${MIDDLEWARE_HEADER_PREFIX}-${key}`, val);\n    });\n  }\n\n  // Now that we have normalised res to include overrides, just append the new header\n  Object.entries(newHeaders).forEach(([key, val]) => {\n    res.headers.set(OVERRIDE_HEADERS, `${res.headers.get(OVERRIDE_HEADERS)},${key}`);\n    res.headers.set(`${MIDDLEWARE_HEADER_PREFIX}-${key}`, val);\n  });\n};\n\n// Auth result will be set as both a query param & header when applicable\nexport function decorateRequest(\n  req: ClerkRequest,\n  res: Response,\n  requestState: RequestState,\n  requestData: AuthenticateRequestOptions,\n  keylessMode: Pick<AuthenticateRequestOptions, 'publishableKey' | 'secretKey'>,\n  machineAuthObject: AuthObject | null,\n): Response {\n  const { reason, message, status, token } = requestState;\n  // pass-through case, convert to next()\n  if (!res) {\n    res = NextResponse.next();\n  }\n\n  // redirect() case, return early\n  if (res.headers.get(nextConstants.Headers.NextRedirect)) {\n    return res;\n  }\n\n  let rewriteURL;\n\n  // next() case, convert to a rewrite\n  if (res.headers.get(nextConstants.Headers.NextResume) === '1') {\n    res.headers.delete(nextConstants.Headers.NextResume);\n    rewriteURL = new URL(req.url);\n  }\n\n  // rewrite() case, set auth result only if origin remains the same\n  const rewriteURLHeader = res.headers.get(nextConstants.Headers.NextRewrite);\n\n  if (rewriteURLHeader) {\n    const reqURL = new URL(req.url);\n    rewriteURL = new URL(rewriteURLHeader);\n\n    // if the origin has changed, return early\n    if (rewriteURL.origin !== reqURL.origin) {\n      return res;\n    }\n  }\n\n  if (rewriteURL) {\n    const clerkRequestData = encryptClerkRequestData(requestData, keylessMode, machineAuthObject);\n\n    setRequestHeadersOnNextResponse(res, req, {\n      [constants.Headers.AuthStatus]: status,\n      [constants.Headers.AuthToken]: token || '',\n      [constants.Headers.AuthSignature]: token\n        ? createTokenSignature(token, requestData?.secretKey || SECRET_KEY || keylessMode.secretKey || '')\n        : '',\n      [constants.Headers.AuthMessage]: message || '',\n      [constants.Headers.AuthReason]: reason || '',\n      [constants.Headers.ClerkUrl]: req.clerkUrl.toString(),\n      ...(clerkRequestData ? { [constants.Headers.ClerkRequestData]: clerkRequestData } : {}),\n    });\n    res.headers.set(nextConstants.Headers.NextRewrite, rewriteURL.href);\n  }\n\n  return res;\n}\n\nexport const handleMultiDomainAndProxy = (clerkRequest: ClerkRequest, opts: AuthenticateRequestOptions) => {\n  const relativeOrAbsoluteProxyUrl = handleValueOrFn(opts?.proxyUrl, clerkRequest.clerkUrl, PROXY_URL);\n\n  let proxyUrl;\n  if (!!relativeOrAbsoluteProxyUrl && !isHttpOrHttps(relativeOrAbsoluteProxyUrl)) {\n    proxyUrl = new URL(relativeOrAbsoluteProxyUrl, clerkRequest.clerkUrl).toString();\n  } else {\n    proxyUrl = relativeOrAbsoluteProxyUrl;\n  }\n\n  const isSatellite = handleValueOrFn(opts.isSatellite, new URL(clerkRequest.url), IS_SATELLITE);\n  const domain = handleValueOrFn(opts.domain, new URL(clerkRequest.url), DOMAIN);\n  const signInUrl = opts?.signInUrl || SIGN_IN_URL;\n\n  if (isSatellite && !proxyUrl && !domain) {\n    throw new Error(missingDomainAndProxy);\n  }\n\n  if (isSatellite && !isHttpOrHttps(signInUrl) && isDevelopmentFromSecretKey(opts.secretKey || SECRET_KEY)) {\n    throw new Error(missingSignInUrlInDev);\n  }\n\n  return {\n    proxyUrl,\n    isSatellite,\n    domain,\n    signInUrl,\n  };\n};\n\nexport const redirectAdapter = (url: string | URL) => {\n  return NextResponse.redirect(url, { headers: { [constants.Headers.ClerkRedirectTo]: 'true' } });\n};\n\nexport function assertAuthStatus(req: RequestLike, error: string) {\n  if (!detectClerkMiddleware(req)) {\n    throw new Error(error);\n  }\n}\n\nexport function assertKey(key: string | undefined, onError: () => never): string {\n  if (!key) {\n    onError();\n  }\n\n  return key;\n}\n\n/**\n * Compute a cryptographic signature from a session token and provided secret key. Used to validate that the token has not been modified when transferring between middleware and the Next.js origin.\n */\nfunction createTokenSignature(token: string, key: string): string {\n  return HmacSHA1(token, key).toString();\n}\n\n/**\n * Assert that the provided token generates a matching signature.\n */\nexport function assertTokenSignature(token: string, key: string, signature?: string | null) {\n  if (!signature) {\n    throw new Error(authSignatureInvalid);\n  }\n\n  const expectedSignature = createTokenSignature(token, key);\n  if (expectedSignature !== signature) {\n    throw new Error(authSignatureInvalid);\n  }\n}\n\nconst KEYLESS_ENCRYPTION_KEY = 'clerk_keyless_dummy_key';\n\n/**\n * Encrypt request data propagated between server requests.\n * @internal\n **/\nexport function encryptClerkRequestData(\n  requestData: Partial<AuthenticateRequestOptions>,\n  keylessModeKeys: Pick<AuthenticateRequestOptions, 'publishableKey' | 'secretKey'>,\n  machineAuthObject: AuthObject | null,\n) {\n  const isEmpty = (obj: Record<string, any> | undefined) => {\n    if (!obj) {\n      return true;\n    }\n    return !Object.values(obj).some(v => v !== undefined);\n  };\n\n  if (isEmpty(requestData) && isEmpty(keylessModeKeys) && !machineAuthObject) {\n    return;\n  }\n\n  if (requestData.secretKey && !ENCRYPTION_KEY) {\n    // TODO SDK-1833: change this to an error in the next major version of `@clerk/nextjs`\n    logger.warnOnce(\n      'Clerk: Missing `CLERK_ENCRYPTION_KEY`. Required for propagating `secretKey` middleware option. See docs: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys',\n    );\n\n    return;\n  }\n\n  const maybeKeylessEncryptionKey = isProductionEnvironment()\n    ? ENCRYPTION_KEY || assertKey(SECRET_KEY, () => errorThrower.throwMissingSecretKeyError())\n    : ENCRYPTION_KEY || SECRET_KEY || KEYLESS_ENCRYPTION_KEY;\n\n  return AES.encrypt(\n    JSON.stringify({ ...keylessModeKeys, ...requestData, machineAuthObject: machineAuthObject ?? undefined }),\n    maybeKeylessEncryptionKey,\n  ).toString();\n}\n\n/**\n * Decrypt request data propagated between server requests.\n * @internal\n */\nexport function decryptClerkRequestData(\n  encryptedRequestData?: string | undefined | null,\n): Partial<AuthenticateRequestOptions> & { machineAuthObject?: AuthObject } {\n  if (!encryptedRequestData) {\n    return {};\n  }\n\n  const maybeKeylessEncryptionKey = isProductionEnvironment()\n    ? ENCRYPTION_KEY || SECRET_KEY\n    : ENCRYPTION_KEY || SECRET_KEY || KEYLESS_ENCRYPTION_KEY;\n\n  try {\n    return decryptData(encryptedRequestData, maybeKeylessEncryptionKey);\n  } catch {\n    /**\n     * There is a great chance when running in Keyless mode that the above fails,\n     * because the keys hot-swapped and the Next.js dev server has not yet fully rebuilt middleware and routes.\n     *\n     * Attempt one more time with the default dummy value.\n     */\n    if (canUseKeyless) {\n      try {\n        return decryptData(encryptedRequestData, KEYLESS_ENCRYPTION_KEY);\n      } catch {\n        throwInvalidEncryptionKey();\n      }\n    }\n    throwInvalidEncryptionKey();\n  }\n}\n\nfunction throwInvalidEncryptionKey(): never {\n  if (isProductionEnvironment()) {\n    throw new Error(encryptionKeyInvalid);\n  }\n  throw new Error(encryptionKeyInvalidDev);\n}\n\nfunction decryptData(data: string, key: string) {\n  const decryptedBytes = AES.decrypt(data, key);\n  const encoded = decryptedBytes.toString(Utf8);\n  return JSON.parse(encoded);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAEA,SAAS,iBAAiB;;AAC1B,SAAS,kCAAkC;;AAC3C,SAAS,cAAc;;AACvB,SAAS,qBAAqB;;;AAC9B,SAAS,iBAAiB,+BAA+B;AACzD,SAAS,oBAAoB;AAE7B,SAAS,aAAa,qBAAqB;AAC3C,SAAS,qBAAqB;AAC9B,SAAS,KAAK,UAAU,YAAY;AACpC,SAAS,QAAQ,gBAAgB,cAAc,WAAW,YAAY,mBAAmB;AACzF;AAOA,SAAS,oBAAoB;AAC7B,SAAS,6BAA6B;;;;;;;;;;;;;;;AAGtC,MAAM,mBAAmB;AACzB,MAAM,2BAA2B;AAE1B,MAAM,kCAAkC,CAC7C,KACA,KACA,eACG;IACH,IAAI,CAAC,IAAI,OAAA,CAAQ,GAAA,CAAI,gBAAgB,GAAG;QAItC,IAAI,OAAA,CAAQ,GAAA,CAAI,kBAAkB,CAAC;eAAG,IAAI,OAAA,CAAQ,IAAA,CAAK,CAAC;SAAC;QACzD,IAAI,OAAA,CAAQ,OAAA,CAAQ,CAAC,KAAK,QAAQ;YAChC,IAAI,OAAA,CAAQ,GAAA,CAAI,GAAG,wBAAwB,CAAA,CAAA,EAAI,GAAG,EAAA,EAAI,GAAG;QAC3D,CAAC;IACH;IAGA,OAAO,OAAA,CAAQ,UAAU,EAAE,OAAA,CAAQ,CAAC,CAAC,KAAK,GAAG,CAAA,KAAM;QACjD,IAAI,OAAA,CAAQ,GAAA,CAAI,kBAAkB,GAAG,IAAI,OAAA,CAAQ,GAAA,CAAI,gBAAgB,CAAC,CAAA,CAAA,EAAI,GAAG,EAAE;QAC/E,IAAI,OAAA,CAAQ,GAAA,CAAI,GAAG,wBAAwB,CAAA,CAAA,EAAI,GAAG,EAAA,EAAI,GAAG;IAC3D,CAAC;AACH;AAGO,SAAS,gBACd,GAAA,EACA,GAAA,EACA,YAAA,EACA,WAAA,EACA,WAAA,EACA,iBAAA,EACU;IACV,MAAM,EAAE,MAAA,EAAQ,OAAA,EAAS,MAAA,EAAQ,KAAA,CAAM,CAAA,GAAI;IAE3C,IAAI,CAAC,KAAK;QACR,MAAM,4JAAA,CAAa,IAAA,CAAK;IAC1B;IAGA,IAAI,IAAI,OAAA,CAAQ,GAAA,CAAI,wLAAA,CAAc,OAAA,CAAQ,YAAY,GAAG;QACvD,OAAO;IACT;IAEA,IAAI;IAGJ,IAAI,IAAI,OAAA,CAAQ,GAAA,CAAI,wLAAA,CAAc,OAAA,CAAQ,UAAU,MAAM,KAAK;QAC7D,IAAI,OAAA,CAAQ,MAAA,CAAO,wLAAA,CAAc,OAAA,CAAQ,UAAU;QACnD,aAAa,IAAI,IAAI,IAAI,GAAG;IAC9B;IAGA,MAAM,mBAAmB,IAAI,OAAA,CAAQ,GAAA,CAAI,wLAAA,CAAc,OAAA,CAAQ,WAAW;IAE1E,IAAI,kBAAkB;QACpB,MAAM,SAAS,IAAI,IAAI,IAAI,GAAG;QAC9B,aAAa,IAAI,IAAI,gBAAgB;QAGrC,IAAI,WAAW,MAAA,KAAW,OAAO,MAAA,EAAQ;YACvC,OAAO;QACT;IACF;IAEA,IAAI,YAAY;QACd,MAAM,mBAAmB,wBAAwB,aAAa,aAAa,iBAAiB;QAE5F,gCAAgC,KAAK,KAAK;YACxC,CAAC,2MAAA,CAAU,OAAA,CAAQ,UAAU,CAAA,EAAG;YAChC,CAAC,2MAAA,CAAU,OAAA,CAAQ,SAAS,CAAA,EAAG,SAAS;YACxC,CAAC,2MAAA,CAAU,OAAA,CAAQ,aAAa,CAAA,EAAG,QAC/B,qBAAqB,OAAA,CAAO,eAAA,OAAA,KAAA,IAAA,YAAa,SAAA,KAAa,mMAAA,IAAc,YAAY,SAAA,IAAa,EAAE,IAC/F;YACJ,CAAC,2MAAA,CAAU,OAAA,CAAQ,WAAW,CAAA,EAAG,WAAW;YAC5C,CAAC,2MAAA,CAAU,OAAA,CAAQ,UAAU,CAAA,EAAG,UAAU;YAC1C,CAAC,2MAAA,CAAU,OAAA,CAAQ,QAAQ,CAAA,EAAG,IAAI,QAAA,CAAS,QAAA,CAAS;YACpD,GAAI,mBAAmB;gBAAE,CAAC,2MAAA,CAAU,OAAA,CAAQ,gBAAgB,CAAA,EAAG;YAAiB,IAAI,CAAC,CAAA;QACvF,CAAC;QACD,IAAI,OAAA,CAAQ,GAAA,CAAI,wLAAA,CAAc,OAAA,CAAQ,WAAA,EAAa,WAAW,IAAI;IACpE;IAEA,OAAO;AACT;AAEO,MAAM,4BAA4B,CAAC,cAA4B,SAAqC;IACzG,MAAM,iCAA6B,gMAAA,EAAgB,QAAA,OAAA,KAAA,IAAA,KAAM,QAAA,EAAU,aAAa,QAAA,EAAU,kMAAS;IAEnG,IAAI;IACJ,IAAI,CAAC,CAAC,8BAA8B,KAAC,8LAAA,EAAc,0BAA0B,GAAG;QAC9E,WAAW,IAAI,IAAI,4BAA4B,aAAa,QAAQ,EAAE,QAAA,CAAS;IACjF,OAAO;QACL,WAAW;IACb;IAEA,MAAM,kBAAc,gMAAA,EAAgB,KAAK,WAAA,EAAa,IAAI,IAAI,aAAa,GAAG,GAAG,qMAAY;IAC7F,MAAM,aAAS,gMAAA,EAAgB,KAAK,MAAA,EAAQ,IAAI,IAAI,aAAa,GAAG,GAAG,+LAAM;IAC7E,MAAM,YAAA,CAAY,QAAA,OAAA,KAAA,IAAA,KAAM,SAAA,KAAa,oMAAA;IAErC,IAAI,eAAe,CAAC,YAAY,CAAC,QAAQ;QACvC,MAAM,IAAI,MAAM,2MAAqB;IACvC;IAEA,IAAI,eAAe,KAAC,8LAAA,EAAc,SAAS,SAAK,2MAAA,EAA2B,KAAK,SAAA,IAAa,mMAAU,GAAG;QACxG,MAAM,IAAI,MAAM,2MAAqB;IACvC;IAEA,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;AAEO,MAAM,kBAAkB,CAAC,QAAsB;IACpD,OAAO,4JAAA,CAAa,QAAA,CAAS,KAAK;QAAE,SAAS;YAAE,CAAC,2MAAA,CAAU,OAAA,CAAQ,eAAe,CAAA,EAAG;QAAO;IAAE,CAAC;AAChG;AAEO,SAAS,iBAAiB,GAAA,EAAkB,KAAA,EAAe;IAChE,IAAI,KAAC,qNAAA,EAAsB,GAAG,GAAG;QAC/B,MAAM,IAAI,MAAM,KAAK;IACvB;AACF;AAEO,SAAS,UAAU,GAAA,EAAyB,OAAA,EAA8B;IAC/E,IAAI,CAAC,KAAK;QACR,QAAQ;IACV;IAEA,OAAO;AACT;AAKA,SAAS,qBAAqB,KAAA,EAAe,GAAA,EAAqB;IAChE,WAAO,oMAAA,EAAS,OAAO,GAAG,EAAE,QAAA,CAAS;AACvC;AAKO,SAAS,qBAAqB,KAAA,EAAe,GAAA,EAAa,SAAA,EAA2B;IAC1F,IAAI,CAAC,WAAW;QACd,MAAM,IAAI,MAAM,0MAAoB;IACtC;IAEA,MAAM,oBAAoB,qBAAqB,OAAO,GAAG;IACzD,IAAI,sBAAsB,WAAW;QACnC,MAAM,IAAI,MAAM,0MAAoB;IACtC;AACF;AAEA,MAAM,yBAAyB;AAMxB,SAAS,wBACd,WAAA,EACA,eAAA,EACA,iBAAA,EACA;IACA,MAAM,UAAU,CAAC,QAAyC;QACxD,IAAI,CAAC,KAAK;YACR,OAAO;QACT;QACA,OAAO,CAAC,OAAO,MAAA,CAAO,GAAG,EAAE,IAAA,CAAK,CAAA,IAAK,MAAM,KAAA,CAAS;IACtD;IAEA,IAAI,QAAQ,WAAW,KAAK,QAAQ,eAAe,KAAK,CAAC,mBAAmB;QAC1E;IACF;IAEA,IAAI,YAAY,SAAA,IAAa,CAAC,uMAAA,EAAgB;QAE5C,uLAAA,CAAO,QAAA,CACL;QAGF;IACF;IAEA,MAAM,gCAA4B,wMAAA,CAAwB,KACtD,uMAAA,IAAkB,UAAU,mMAAA,EAAY,IAAM,wMAAA,CAAa,0BAAA,CAA2B,CAAC,IACvF,uMAAA,IAAkB,mMAAA,IAAc;IAEpC,OAAO,+LAAA,CAAI,OAAA,CACT,KAAK,SAAA,CAAU;QAAE,GAAG,eAAA;QAAiB,GAAG,WAAA;QAAa,mBAAmB,qBAAA,OAAA,oBAAqB,KAAA;IAAU,CAAC,GACxG,2BACA,QAAA,CAAS;AACb;AAMO,SAAS,wBACd,oBAAA,EAC0E;IAC1E,IAAI,CAAC,sBAAsB;QACzB,OAAO,CAAC;IACV;IAEA,MAAM,gCAA4B,wMAAA,CAAwB,KACtD,uMAAA,IAAkB,mMAAA,GAClB,uMAAA,IAAkB,mMAAA,IAAc;IAEpC,IAAI;QACF,OAAO,YAAY,sBAAsB,yBAAyB;IACpE,EAAA,OAAQ;QAON,IAAI,4MAAA,EAAe;YACjB,IAAI;gBACF,OAAO,YAAY,sBAAsB,sBAAsB;YACjE,EAAA,OAAQ;gBACN,0BAA0B;YAC5B;QACF;QACA,0BAA0B;IAC5B;AACF;AAEA,SAAS,4BAAmC;IAC1C,QAAI,wMAAA,CAAwB,IAAG;QAC7B,MAAM,IAAI,MAAM,0MAAoB;IACtC;IACA,MAAM,IAAI,MAAM,6MAAuB;AACzC;AAEA,SAAS,YAAY,IAAA,EAAc,GAAA,EAAa;IAC9C,MAAM,iBAAiB,+LAAA,CAAI,OAAA,CAAQ,MAAM,GAAG;IAC5C,MAAM,UAAU,eAAe,QAAA,CAAS,gMAAI;IAC5C,OAAO,KAAK,KAAA,CAAM,OAAO;AAC3B", "debugId": null}}, {"offset": {"line": 1180, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/server/data/getAuthDataFromRequest.ts"], "sourcesContent": ["import type { AuthObject, MachineAuthObject } from '@clerk/backend';\nimport type {\n  AuthenticateRequestOptions,\n  MachineTokenType,\n  SignedInAuthObject,\n  SignedOutAuthObject,\n} from '@clerk/backend/internal';\nimport {\n  AuthStatus,\n  constants,\n  getAuthObjectForAcceptedToken,\n  getAuthObjectFromJwt,\n  invalidTokenAuthObject,\n  isMachineTokenByPrefix,\n  isTokenTypeAccepted,\n  signedOutAuthObject,\n  TokenType,\n} from '@clerk/backend/internal';\nimport { decodeJwt } from '@clerk/backend/jwt';\nimport type { PendingSessionOptions } from '@clerk/types';\n\nimport type { LoggerNoCommit } from '../../utils/debugLogger';\nimport { API_URL, API_VERSION, PUBLISHABLE_KEY, SECRET_KEY } from '../constants';\nimport { getAuthKeyFromRequest, getHeader } from '../headers-utils';\nimport type { RequestLike } from '../types';\nimport { assertTokenSignature, decryptClerkRequestData } from '../utils';\n\nexport type GetAuthDataFromRequestOptions = {\n  secretKey?: string;\n  logger?: LoggerNoCommit;\n  acceptsToken?: AuthenticateRequestOptions['acceptsToken'];\n} & PendingSessionOptions;\n\n/**\n * Extracts auth headers from the request\n */\nconst getAuthHeaders = (req: RequestLike) => {\n  return {\n    authStatus: getAuthKeyFromRequest(req, 'AuthStatus'),\n    authToken: getAuthKeyFromRequest(req, 'AuthToken'),\n    authMessage: getAuthKeyFromRequest(req, 'AuthMessage'),\n    authReason: getAuthKeyFromRequest(req, 'AuthReason'),\n    authSignature: getAuthKeyFromRequest(req, 'AuthSignature'),\n  };\n};\n\n/**\n * Creates auth options object with fallbacks from encrypted request data\n */\nconst createAuthOptions = (req: RequestLike, opts: GetAuthDataFromRequestOptions, treatPendingAsSignedOut = true) => {\n  const encryptedRequestData = getHeader(req, constants.Headers.ClerkRequestData);\n  const decryptedRequestData = decryptClerkRequestData(encryptedRequestData);\n\n  return {\n    secretKey: opts?.secretKey || decryptedRequestData.secretKey || SECRET_KEY,\n    publishableKey: decryptedRequestData.publishableKey || PUBLISHABLE_KEY,\n    apiUrl: API_URL,\n    apiVersion: API_VERSION,\n    authStatus: getAuthKeyFromRequest(req, 'AuthStatus'),\n    authMessage: getAuthKeyFromRequest(req, 'AuthMessage'),\n    authReason: getAuthKeyFromRequest(req, 'AuthReason'),\n    treatPendingAsSignedOut,\n  };\n};\n\n/**\n * Given a request object, builds an auth object from the request data. Used in server-side environments to get access\n * to auth data for a given request.\n */\nexport const getSessionAuthDataFromRequest = (\n  req: RequestLike,\n  { treatPendingAsSignedOut = true, ...opts }: GetAuthDataFromRequestOptions = {},\n): SignedInAuthObject | SignedOutAuthObject => {\n  const { authStatus, authMessage, authReason, authToken, authSignature } = getAuthHeaders(req);\n\n  opts.logger?.debug('headers', { authStatus, authMessage, authReason });\n\n  const options = createAuthOptions(req, opts, treatPendingAsSignedOut);\n\n  // Only accept session tokens in this function.\n  // Machine tokens are not supported and will result in a signed-out state.\n  if (!isTokenTypeAccepted(TokenType.SessionToken, opts.acceptsToken || TokenType.SessionToken)) {\n    return signedOutAuthObject(options);\n  }\n\n  let authObject;\n  if (!authStatus || authStatus !== AuthStatus.SignedIn) {\n    authObject = signedOutAuthObject(options);\n  } else {\n    assertTokenSignature(authToken as string, options.secretKey, authSignature);\n\n    const jwt = decodeJwt(authToken as string);\n\n    opts.logger?.debug('jwt', jwt.raw);\n\n    return getAuthObjectFromJwt(jwt, options);\n  }\n\n  return authObject;\n};\n\n/**\n * Given a request object, builds an auth object from the request data. Used in server-side environments to get access\n * to auth data for a given request.\n *\n * This function handles both session tokens and machine tokens:\n * - Session tokens: Decoded from JWT and validated\n * - Machine tokens: Retrieved from encrypted request data (x-clerk-request-data header)\n */\nexport const getAuthDataFromRequest = (req: RequestLike, opts: GetAuthDataFromRequestOptions = {}): AuthObject => {\n  const { authStatus, authMessage, authReason } = getAuthHeaders(req);\n  opts.logger?.debug('headers', { authStatus, authMessage, authReason });\n\n  const encryptedRequestData = getHeader(req, constants.Headers.ClerkRequestData);\n  const decryptedRequestData = decryptClerkRequestData(encryptedRequestData);\n\n  const bearerToken = getHeader(req, constants.Headers.Authorization)?.replace('Bearer ', '');\n  const acceptsToken = opts.acceptsToken || TokenType.SessionToken;\n\n  const options = createAuthOptions(req, opts);\n\n  // Handle machine tokens first (from encrypted request data)\n  // Machine tokens are passed via x-clerk-request-data header from middleware\n  const machineAuthObject = handleMachineToken(\n    bearerToken,\n    decryptedRequestData.machineAuthObject,\n    acceptsToken,\n    options,\n  );\n  if (machineAuthObject) {\n    return machineAuthObject;\n  }\n\n  // If a random token is present and acceptsToken is an array that does NOT include session_token,\n  // return invalid token auth object.\n  if (bearerToken && Array.isArray(acceptsToken) && !acceptsToken.includes(TokenType.SessionToken)) {\n    return invalidTokenAuthObject();\n  }\n\n  // Fallback to session logic for all other cases\n  return getSessionAuthDataFromRequest(req, opts);\n};\n\nconst handleMachineToken = (\n  bearerToken: string | undefined,\n  rawAuthObject: AuthObject | undefined,\n  acceptsToken: NonNullable<AuthenticateRequestOptions['acceptsToken']>,\n  options: Record<string, any>,\n): MachineAuthObject<MachineTokenType> | null => {\n  const hasMachineToken = bearerToken && isMachineTokenByPrefix(bearerToken);\n\n  const acceptsOnlySessionToken =\n    acceptsToken === TokenType.SessionToken ||\n    (Array.isArray(acceptsToken) && acceptsToken.length === 1 && acceptsToken[0] === TokenType.SessionToken);\n\n  if (hasMachineToken && rawAuthObject && !acceptsOnlySessionToken) {\n    const authObject = getAuthObjectForAcceptedToken({\n      authObject: {\n        ...rawAuthObject,\n        debug: () => options,\n      },\n      acceptsToken,\n    });\n    return {\n      ...authObject,\n      getToken: () => (authObject.isAuthenticated ? Promise.resolve(bearerToken) : Promise.resolve(null)),\n      has: () => false,\n    } as MachineAuthObject<MachineTokenType>;\n  }\n\n  return null;\n};\n"], "names": [], "mappings": ";;;;;;;AAOA;AAWA,SAAS,iBAAiB;AAI1B,SAAS,SAAS,aAAa,iBAAiB,kBAAkB;AAClE,SAAS,uBAAuB,iBAAiB;AAEjD,SAAS,sBAAsB,+BAA+B;;;;;;;AAW9D,MAAM,iBAAiB,CAAC,QAAqB;IAC3C,OAAO;QACL,gBAAY,qNAAA,EAAsB,KAAK,YAAY;QACnD,eAAW,qNAAA,EAAsB,KAAK,WAAW;QACjD,iBAAa,qNAAA,EAAsB,KAAK,aAAa;QACrD,gBAAY,qNAAA,EAAsB,KAAK,YAAY;QACnD,mBAAe,qNAAA,EAAsB,KAAK,eAAe;IAC3D;AACF;AAKA,MAAM,oBAAoB,CAAC,KAAkB,MAAqC,0BAA0B,IAAA,KAAS;IACnH,MAAM,2BAAuB,yMAAA,EAAU,KAAK,2MAAA,CAAU,OAAA,CAAQ,gBAAgB;IAC9E,MAAM,2BAAuB,4MAAA,EAAwB,oBAAoB;IAEzE,OAAO;QACL,WAAA,CAAW,QAAA,OAAA,KAAA,IAAA,KAAM,SAAA,KAAa,qBAAqB,SAAA,IAAa,mMAAA;QAChE,gBAAgB,qBAAqB,cAAA,IAAkB,wMAAA;QACvD,QAAQ,gMAAA;QACR,YAAY,oMAAA;QACZ,gBAAY,qNAAA,EAAsB,KAAK,YAAY;QACnD,iBAAa,qNAAA,EAAsB,KAAK,aAAa;QACrD,gBAAY,qNAAA,EAAsB,KAAK,YAAY;QACnD;IACF;AACF;AAMO,MAAM,gCAAgC,CAC3C,KACA,EAAE,0BAA0B,IAAA,EAAM,GAAG,KAAK,CAAA,GAAmC,CAAC,CAAA,KACjC;IAxE/C,IAAA,IAAA;IAyEE,MAAM,EAAE,UAAA,EAAY,WAAA,EAAa,UAAA,EAAY,SAAA,EAAW,aAAA,CAAc,CAAA,GAAI,eAAe,GAAG;IAE5F,CAAA,KAAA,KAAK,MAAA,KAAL,OAAA,KAAA,IAAA,GAAa,KAAA,CAAM,WAAW;QAAE;QAAY;QAAa;IAAW;IAEpE,MAAM,UAAU,kBAAkB,KAAK,MAAM,uBAAuB;IAIpE,IAAI,KAAC,qNAAA,EAAoB,2MAAA,CAAU,YAAA,EAAc,KAAK,YAAA,IAAgB,2MAAA,CAAU,YAAY,GAAG;QAC7F,WAAO,qNAAA,EAAoB,OAAO;IACpC;IAEA,IAAI;IACJ,IAAI,CAAC,cAAc,eAAe,4MAAA,CAAW,QAAA,EAAU;QACrD,iBAAa,qNAAA,EAAoB,OAAO;IAC1C,OAAO;QACL,IAAA,yMAAA,EAAqB,WAAqB,QAAQ,SAAA,EAAW,aAAa;QAE1E,MAAM,UAAM,sLAAA,EAAU,SAAmB;QAEzC,CAAA,KAAA,KAAK,MAAA,KAAL,OAAA,KAAA,IAAA,GAAa,KAAA,CAAM,OAAO,IAAI,GAAA;QAE9B,WAAO,sNAAA,EAAqB,KAAK,OAAO;IAC1C;IAEA,OAAO;AACT;AAUO,MAAM,yBAAyB,CAAC,KAAkB,OAAsC,CAAC,CAAA,KAAkB;IA7GlH,IAAA,IAAA;IA8GE,MAAM,EAAE,UAAA,EAAY,WAAA,EAAa,UAAA,CAAW,CAAA,GAAI,eAAe,GAAG;IAClE,CAAA,KAAA,KAAK,MAAA,KAAL,OAAA,KAAA,IAAA,GAAa,KAAA,CAAM,WAAW;QAAE;QAAY;QAAa;IAAW;IAEpE,MAAM,2BAAuB,yMAAA,EAAU,KAAK,2MAAA,CAAU,OAAA,CAAQ,gBAAgB;IAC9E,MAAM,2BAAuB,4MAAA,EAAwB,oBAAoB;IAEzE,MAAM,cAAA,CAAc,KAAA,IAAA,yMAAA,EAAU,KAAK,2MAAA,CAAU,OAAA,CAAQ,aAAa,CAAA,KAA9C,OAAA,KAAA,IAAA,GAAiD,OAAA,CAAQ,WAAW;IACxF,MAAM,eAAe,KAAK,YAAA,IAAgB,2MAAA,CAAU,YAAA;IAEpD,MAAM,UAAU,kBAAkB,KAAK,IAAI;IAI3C,MAAM,oBAAoB,mBACxB,aACA,qBAAqB,iBAAA,EACrB,cACA;IAEF,IAAI,mBAAmB;QACrB,OAAO;IACT;IAIA,IAAI,eAAe,MAAM,OAAA,CAAQ,YAAY,KAAK,CAAC,aAAa,QAAA,CAAS,2MAAA,CAAU,YAAY,GAAG;QAChG,WAAO,wNAAA,CAAuB;IAChC;IAGA,OAAO,8BAA8B,KAAK,IAAI;AAChD;AAEA,MAAM,qBAAqB,CACzB,aACA,eACA,cACA,YAC+C;IAC/C,MAAM,kBAAkB,mBAAe,wNAAA,EAAuB,WAAW;IAEzE,MAAM,0BACJ,iBAAiB,2MAAA,CAAU,YAAA,IAC1B,MAAM,OAAA,CAAQ,YAAY,KAAK,aAAa,MAAA,KAAW,KAAK,YAAA,CAAa,CAAC,CAAA,KAAM,2MAAA,CAAU,YAAA;IAE7F,IAAI,mBAAmB,iBAAiB,CAAC,yBAAyB;QAChE,MAAM,iBAAa,+NAAA,EAA8B;YAC/C,YAAY;gBACV,GAAG,aAAA;gBACH,OAAO,IAAM;YACf;YACA;QACF,CAAC;QACD,OAAO;YACL,GAAG,UAAA;YACH,UAAU,IAAO,WAAW,eAAA,GAAkB,QAAQ,OAAA,CAAQ,WAAW,IAAI,QAAQ,OAAA,CAAQ,IAAI;YACjG,KAAK,IAAM;QACb;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1291, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/server/createGetAuth.ts"], "sourcesContent": ["import type { AuthObject } from '@clerk/backend';\nimport { constants, type SignedInAuthObject, type SignedOutAuthObject } from '@clerk/backend/internal';\nimport { isTruthy } from '@clerk/shared/underscore';\nimport type { PendingSessionOptions } from '@clerk/types';\n\nimport { withLogger } from '../utils/debugLogger';\nimport { isNextWithUnstableServerActions } from '../utils/sdk-versions';\nimport type { GetAuthDataFromRequestOptions } from './data/getAuthDataFromRequest';\nimport {\n  getAuthDataFromRequest as getAuthDataFromRequestOriginal,\n  getSessionAuthDataFromRequest as getSessionAuthDataFromRequestOriginal,\n} from './data/getAuthDataFromRequest';\nimport { getAuthAuthHeaderMissing } from './errors';\nimport { detectClerkMiddleware, getHeader } from './headers-utils';\nimport type { RequestLike } from './types';\nimport { assertAuthStatus } from './utils';\n\nexport type GetAuthOptions = {\n  acceptsToken?: GetAuthDataFromRequestOptions['acceptsToken'];\n} & PendingSessionOptions;\n\n/**\n * The async variant of our old `createGetAuth` allows for asynchronous code inside its callback.\n * Should be used with function like `auth()` that are already asynchronous.\n */\nexport const createAsyncGetAuth = ({\n  debugLoggerName,\n  noAuthStatusMessage,\n}: {\n  debugLoggerName: string;\n  noAuthStatusMessage: string;\n}) =>\n  withLogger(debugLoggerName, logger => {\n    return async (req: RequestLike, opts?: { secretKey?: string } & GetAuthOptions): Promise<AuthObject> => {\n      if (isTruthy(getHeader(req, constants.Headers.EnableDebug))) {\n        logger.enable();\n      }\n\n      if (!detectClerkMiddleware(req)) {\n        // Keep the same behaviour for versions that may have issues with bundling `node:fs`\n        if (isNextWithUnstableServerActions) {\n          assertAuthStatus(req, noAuthStatusMessage);\n        }\n\n        const missConfiguredMiddlewareLocation = await import('./fs/middleware-location.js')\n          .then(m => m.suggestMiddlewareLocation())\n          .catch(() => undefined);\n\n        if (missConfiguredMiddlewareLocation) {\n          throw new Error(missConfiguredMiddlewareLocation);\n        }\n\n        // still throw there is no suggested move location\n        assertAuthStatus(req, noAuthStatusMessage);\n      }\n\n      const getAuthDataFromRequest = (req: RequestLike, opts: GetAuthDataFromRequestOptions = {}) => {\n        return getAuthDataFromRequestOriginal(req, { ...opts, logger, acceptsToken: opts?.acceptsToken });\n      };\n\n      return getAuthDataFromRequest(req, { ...opts, logger, acceptsToken: opts?.acceptsToken });\n    };\n  });\n\n/**\n * Previous known as `createGetAuth`. We needed to create a sync and async variant in order to allow for improvements\n * that required dynamic imports (using `require` would not work).\n * It powers the synchronous top-level api `getAuth()`.\n */\nexport const createSyncGetAuth = ({\n  debugLoggerName,\n  noAuthStatusMessage,\n}: {\n  debugLoggerName: string;\n  noAuthStatusMessage: string;\n}) =>\n  withLogger(debugLoggerName, logger => {\n    return (\n      req: RequestLike,\n      opts?: { secretKey?: string } & GetAuthOptions,\n    ): SignedInAuthObject | SignedOutAuthObject => {\n      if (isTruthy(getHeader(req, constants.Headers.EnableDebug))) {\n        logger.enable();\n      }\n\n      assertAuthStatus(req, noAuthStatusMessage);\n\n      const getAuthDataFromRequest = (req: RequestLike, opts: GetAuthDataFromRequestOptions = {}) => {\n        return getSessionAuthDataFromRequestOriginal(req, { ...opts, logger, acceptsToken: opts?.acceptsToken });\n      };\n\n      return getAuthDataFromRequest(req, { ...opts, logger, acceptsToken: opts?.acceptsToken });\n    };\n  });\n\n/**\n * The `getAuth()` helper retrieves authentication state from the request object.\n *\n * > [!NOTE]\n * > If you are using App Router, use the [`auth()` helper](https://clerk.com/docs/references/nextjs/auth) instead.\n *\n * @param req - The Next.js request object.\n * @param [options] - An optional object that can be used to configure the behavior of the `getAuth()` function.\n * @param [options.secretKey] - A string that represents the Secret Key used to sign the session token. If not provided, the Secret Key is retrieved from the environment variable `CLERK_SECRET_KEY`.\n * @returns The `Auth` object. See the [Auth reference](https://clerk.com/docs/references/backend/types/auth-object) for more information.\n *\n * @example\n * ### Protect API routes\n *\n * The following example demonstrates how to protect an API route by checking if the `userId` is present in the `getAuth()` response.\n *\n * ```tsx {{ filename: 'app/api/example/route.ts' }}\n * import { getAuth } from '@clerk/nextjs/server'\n * import type { NextApiRequest, NextApiResponse } from 'next'\n *\n * export default async function handler(req: NextApiRequest, res: NextApiResponse) {\n *   const { userId } = getAuth(req)\n *\n *   if (!userId) {\n *     return res.status(401).json({ error: 'Not authenticated' })\n *   }\n *\n *   // Add logic that retrieves the data for the API route\n *\n *   return res.status(200).json({ userId: userId })\n * }\n * ```\n *\n * @example\n * ### Usage with `getToken()`\n *\n * `getAuth()` returns [`getToken()`](https://clerk.com/docs/references/backend/types/auth-object#get-token), which is a method that returns the current user's session token or a custom JWT template.\n *\n * ```tsx {{ filename: 'app/api/example/route.ts' }}\n * import { getAuth } from '@clerk/nextjs/server'\n * import type { NextApiRequest, NextApiResponse } from 'next'\n *\n * export default async function handler(req: NextApiRequest, res: NextApiResponse) {\n *   const { getToken } = getAuth(req)\n *\n *   const token = await getToken({ template: 'supabase' })\n *\n *   // Add logic that retrieves the data\n *   // from your database using the token\n *\n *   return res.status(200).json({})\n * }\n * ```\n *\n * @example\n * ### Usage with `clerkClient`\n *\n * `clerkClient` is used to access the [Backend SDK](https://clerk.com/docs/references/backend/overview), which exposes Clerk's Backend API resources. You can use `getAuth()` to pass authentication information that many of the Backend SDK methods require, like the user's ID.\n *\n * ```tsx {{ filename: 'app/api/example/route.ts' }}\n * import { clerkClient, getAuth } from '@clerk/nextjs/server'\n * import type { NextApiRequest, NextApiResponse } from 'next'\n *\n * export default async function handler(req: NextApiRequest, res: NextApiResponse) {\n *   const { userId } = getAuth(req)\n *\n *   const client = await clerkClient()\n *\n *   const user = userId ? await client.users.getUser(userId) : null\n *\n *   return res.status(200).json({})\n * }\n * ```\n */\nexport const getAuth = createSyncGetAuth({\n  debugLoggerName: 'getAuth()',\n  noAuthStatusMessage: getAuthAuthHeaderMissing(),\n});\n"], "names": ["req", "opts"], "mappings": ";;;;;;;;;AACA,SAAS,iBAAoE;;AAC7E,SAAS,gBAAgB;AAGzB,SAAS,kBAAkB;AAC3B,SAAS,uCAAuC;AAEhD;AAIA,SAAS,gCAAgC;AACzC,SAAS,uBAAuB,iBAAiB;AAEjD,SAAS,wBAAwB;;;;;;;;;;AAU1B,MAAM,qBAAqB,CAAC,EACjC,eAAA,EACA,mBAAA,EACF,OAIE,oMAAA,EAAW,iBAAiB,CAAA,WAAU;QACpC,OAAO,OAAO,KAAkB,SAAwE;YACtG,QAAI,yLAAA,MAAS,yMAAA,EAAU,KAAK,2MAAA,CAAU,OAAA,CAAQ,WAAW,CAAC,GAAG;gBAC3D,OAAO,MAAA,CAAO;YAChB;YAEA,IAAI,KAAC,qNAAA,EAAsB,GAAG,GAAG;gBAE/B,IAAI,6NAAA,EAAiC;oBACnC,IAAA,qMAAA,EAAiB,KAAK,mBAAmB;gBAC3C;gBAEA,MAAM,mCAAmC,MAAM,OAAO,6BAA6B,sHAChF,IAAA,CAAK,CAAA,IAAK,EAAE,yBAAA,CAA0B,CAAC,EACvC,KAAA,CAAM,IAAM,KAAA,CAAS;gBAExB,IAAI,kCAAkC;oBACpC,MAAM,IAAI,MAAM,gCAAgC;gBAClD;gBAGA,IAAA,qMAAA,EAAiB,KAAK,mBAAmB;YAC3C;YAEA,MAAM,yBAAyB,CAACA,MAAkBC,QAAsC,CAAC,CAAA,KAAM;gBAC7F,WAAO,oOAAA,EAA+BD,MAAK;oBAAE,GAAGC,KAAAA;oBAAM;oBAAQ,cAAcA,SAAA,OAAA,KAAA,IAAAA,MAAM,YAAA;gBAAa,CAAC;YAClG;YAEA,OAAO,uBAAuB,KAAK;gBAAE,GAAG,IAAA;gBAAM;gBAAQ,cAAc,QAAA,OAAA,KAAA,IAAA,KAAM,YAAA;YAAa,CAAC;QAC1F;IACF,CAAC;AAOI,MAAM,oBAAoB,CAAC,EAChC,eAAA,EACA,mBAAA,EACF,OAIE,oMAAA,EAAW,iBAAiB,CAAA,WAAU;QACpC,OAAO,CACL,KACA,SAC6C;YAC7C,QAAI,yLAAA,MAAS,yMAAA,EAAU,KAAK,2MAAA,CAAU,OAAA,CAAQ,WAAW,CAAC,GAAG;gBAC3D,OAAO,MAAA,CAAO;YAChB;YAEA,IAAA,qMAAA,EAAiB,KAAK,mBAAmB;YAEzC,MAAM,yBAAyB,CAACD,MAAkBC,QAAsC,CAAC,CAAA,KAAM;gBAC7F,WAAO,2OAAA,EAAsCD,MAAK;oBAAE,GAAGC,KAAAA;oBAAM;oBAAQ,cAAcA,SAAA,OAAA,KAAA,IAAAA,MAAM,YAAA;gBAAa,CAAC;YACzG;YAEA,OAAO,uBAAuB,KAAK;gBAAE,GAAG,IAAA;gBAAM;gBAAQ,cAAc,QAAA,OAAA,KAAA,IAAA,KAAM,YAAA;YAAa,CAAC;QAC1F;IACF,CAAC;AA4EI,MAAM,UAAU,kBAAkB;IACvC,iBAAiB;IACjB,yBAAqB,8MAAA,CAAyB;AAChD,CAAC", "debugId": null}}, {"offset": {"line": 1377, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/server/nextErrors.ts"], "sourcesContent": ["/**\n * Clerk's identifiers that are used alongside the ones from Next.js\n */\nconst CONTROL_FLOW_ERROR = {\n  REDIRECT_TO_URL: 'CLERK_PROTECT_REDIRECT_TO_URL',\n  REDIRECT_TO_SIGN_IN: 'CLERK_PROTECT_REDIRECT_TO_SIGN_IN',\n  REDIRECT_TO_SIGN_UP: 'CLERK_PROTECT_REDIRECT_TO_SIGN_UP',\n};\n\n/**\n * In-house implementation of `notFound()`\n * https://github.com/vercel/next.js/blob/canary/packages/next/src/client/components/not-found.ts\n */\nconst LEGACY_NOT_FOUND_ERROR_CODE = 'NEXT_NOT_FOUND';\n\ntype LegacyNotFoundError = Error & {\n  digest: typeof LEGACY_NOT_FOUND_ERROR_CODE;\n};\n\n/**\n * Checks for the error thrown from `notFound()` for versions <= next@15.0.4\n */\nfunction isLegacyNextjsNotFoundError(error: unknown): error is LegacyNotFoundError {\n  if (typeof error !== 'object' || error === null || !('digest' in error)) {\n    return false;\n  }\n\n  return error.digest === LEGACY_NOT_FOUND_ERROR_CODE;\n}\n\nconst HTTPAccessErrorStatusCodes = {\n  NOT_FOUND: 404,\n  FORBIDDEN: 403,\n  UNAUTHORIZED: 401,\n};\n\nconst ALLOWED_CODES = new Set(Object.values(HTTPAccessErrorStatusCodes));\n\nexport const HTTP_ERROR_FALLBACK_ERROR_CODE = 'NEXT_HTTP_ERROR_FALLBACK';\n\nexport type HTTPAccessFallbackError = Error & {\n  digest: `${typeof HTTP_ERROR_FALLBACK_ERROR_CODE};${string}`;\n};\n\nexport function isHTTPAccessFallbackError(error: unknown): error is HTTPAccessFallbackError {\n  if (typeof error !== 'object' || error === null || !('digest' in error) || typeof error.digest !== 'string') {\n    return false;\n  }\n  const [prefix, httpStatus] = error.digest.split(';');\n\n  return prefix === HTTP_ERROR_FALLBACK_ERROR_CODE && ALLOWED_CODES.has(Number(httpStatus));\n}\n\nexport function whichHTTPAccessFallbackError(error: unknown): number | undefined {\n  if (!isHTTPAccessFallbackError(error)) {\n    return undefined;\n  }\n\n  const [, httpStatus] = error.digest.split(';');\n  return Number(httpStatus);\n}\n\nfunction isNextjsNotFoundError(error: unknown): error is LegacyNotFoundError | HTTPAccessFallbackError {\n  return (\n    isLegacyNextjsNotFoundError(error) ||\n    // Checks for the error thrown from `notFound()` for canary versions of next@15\n    whichHTTPAccessFallbackError(error) === HTTPAccessErrorStatusCodes.NOT_FOUND\n  );\n}\n\n/**\n * In-house implementation of `redirect()` extended with a `clerk_digest` property\n * https://github.com/vercel/next.js/blob/canary/packages/next/src/client/components/redirect.ts\n */\n\nconst REDIRECT_ERROR_CODE = 'NEXT_REDIRECT';\n\ntype RedirectError<T = unknown> = Error & {\n  digest: `${typeof REDIRECT_ERROR_CODE};${'replace'};${string};${307};`;\n  clerk_digest: typeof CONTROL_FLOW_ERROR.REDIRECT_TO_URL | typeof CONTROL_FLOW_ERROR.REDIRECT_TO_SIGN_IN;\n} & T;\n\nfunction nextjsRedirectError(\n  url: string,\n  extra: Record<string, unknown>,\n  type: 'replace' = 'replace',\n  statusCode: 307 = 307,\n): never {\n  const error = new Error(REDIRECT_ERROR_CODE) as RedirectError;\n  error.digest = `${REDIRECT_ERROR_CODE};${type};${url};${statusCode};`;\n  error.clerk_digest = CONTROL_FLOW_ERROR.REDIRECT_TO_URL;\n  Object.assign(error, extra);\n  throw error;\n}\n\nfunction buildReturnBackUrl(url: string, returnBackUrl?: string | URL | null): string | URL {\n  return returnBackUrl === null ? '' : returnBackUrl || url;\n}\n\nfunction redirectToSignInError(url: string, returnBackUrl?: string | URL | null): never {\n  nextjsRedirectError(url, {\n    clerk_digest: CONTROL_FLOW_ERROR.REDIRECT_TO_SIGN_IN,\n    returnBackUrl: buildReturnBackUrl(url, returnBackUrl),\n  });\n}\n\nfunction redirectToSignUpError(url: string, returnBackUrl?: string | URL | null): never {\n  nextjsRedirectError(url, {\n    clerk_digest: CONTROL_FLOW_ERROR.REDIRECT_TO_SIGN_UP,\n    returnBackUrl: buildReturnBackUrl(url, returnBackUrl),\n  });\n}\n\n/**\n * Checks an error to determine if it's an error generated by the\n * `redirect(url)` helper.\n *\n * @param error the error that may reference a redirect error\n * @returns true if the error is a redirect error\n */\nfunction isNextjsRedirectError(error: unknown): error is RedirectError<{ redirectUrl: string | URL }> {\n  if (typeof error !== 'object' || error === null || !('digest' in error) || typeof error.digest !== 'string') {\n    return false;\n  }\n\n  const digest = error.digest.split(';');\n  const [errorCode, type] = digest;\n  const destination = digest.slice(2, -2).join(';');\n  const status = digest.at(-2);\n\n  const statusCode = Number(status);\n\n  return (\n    errorCode === REDIRECT_ERROR_CODE &&\n    (type === 'replace' || type === 'push') &&\n    typeof destination === 'string' &&\n    !isNaN(statusCode) &&\n    statusCode === 307\n  );\n}\n\nfunction isRedirectToSignInError(error: unknown): error is RedirectError<{ returnBackUrl: string | URL }> {\n  if (isNextjsRedirectError(error) && 'clerk_digest' in error) {\n    return error.clerk_digest === CONTROL_FLOW_ERROR.REDIRECT_TO_SIGN_IN;\n  }\n\n  return false;\n}\n\nfunction isRedirectToSignUpError(error: unknown): error is RedirectError<{ returnBackUrl: string | URL }> {\n  if (isNextjsRedirectError(error) && 'clerk_digest' in error) {\n    return error.clerk_digest === CONTROL_FLOW_ERROR.REDIRECT_TO_SIGN_UP;\n  }\n\n  return false;\n}\n\nfunction isNextjsUnauthorizedError(error: unknown): error is HTTPAccessFallbackError {\n  return whichHTTPAccessFallbackError(error) === HTTPAccessErrorStatusCodes.UNAUTHORIZED;\n}\n\n/**\n * In-house implementation of experimental `unauthorized()`\n * https://github.com/vercel/next.js/blob/canary/packages/next/src/client/components/unauthorized.ts\n */\nfunction unauthorized(): never {\n  const error = new Error(HTTP_ERROR_FALLBACK_ERROR_CODE) as HTTPAccessFallbackError;\n  error.digest = `${HTTP_ERROR_FALLBACK_ERROR_CODE};${HTTPAccessErrorStatusCodes.UNAUTHORIZED}`;\n  throw error;\n}\n\nexport {\n  isNextjsNotFoundError,\n  isLegacyNextjsNotFoundError,\n  redirectToSignInError,\n  redirectToSignUpError,\n  nextjsRedirectError,\n  isNextjsRedirectError,\n  isRedirectToSignInError,\n  isRedirectToSignUpError,\n  isNextjsUnauthorizedError,\n  unauthorized,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,MAAM,qBAAqB;IACzB,iBAAiB;IACjB,qBAAqB;IACrB,qBAAqB;AACvB;AAMA,MAAM,8BAA8B;AASpC,SAAS,4BAA4B,KAAA,EAA8C;IACjF,IAAI,OAAO,UAAU,YAAY,UAAU,QAAQ,CAAA,CAAE,YAAY,KAAA,GAAQ;QACvE,OAAO;IACT;IAEA,OAAO,MAAM,MAAA,KAAW;AAC1B;AAEA,MAAM,6BAA6B;IACjC,WAAW;IACX,WAAW;IACX,cAAc;AAChB;AAEA,MAAM,gBAAgB,IAAI,IAAI,OAAO,MAAA,CAAO,0BAA0B,CAAC;AAEhE,MAAM,iCAAiC;AAMvC,SAAS,0BAA0B,KAAA,EAAkD;IAC1F,IAAI,OAAO,UAAU,YAAY,UAAU,QAAQ,CAAA,CAAE,YAAY,KAAA,KAAU,OAAO,MAAM,MAAA,KAAW,UAAU;QAC3G,OAAO;IACT;IACA,MAAM,CAAC,QAAQ,UAAU,CAAA,GAAI,MAAM,MAAA,CAAO,KAAA,CAAM,GAAG;IAEnD,OAAO,WAAW,kCAAkC,cAAc,GAAA,CAAI,OAAO,UAAU,CAAC;AAC1F;AAEO,SAAS,6BAA6B,KAAA,EAAoC;IAC/E,IAAI,CAAC,0BAA0B,KAAK,GAAG;QACrC,OAAO,KAAA;IACT;IAEA,MAAM,CAAC,EAAE,UAAU,CAAA,GAAI,MAAM,MAAA,CAAO,KAAA,CAAM,GAAG;IAC7C,OAAO,OAAO,UAAU;AAC1B;AAEA,SAAS,sBAAsB,KAAA,EAAwE;IACrG,OACE,4BAA4B,KAAK,KAAA,+EAAA;IAEjC,6BAA6B,KAAK,MAAM,2BAA2B,SAAA;AAEvE;AAOA,MAAM,sBAAsB;AAO5B,SAAS,oBACP,GAAA,EACA,KAAA,EACA,OAAkB,SAAA,EAClB,aAAkB,GAAA,EACX;IACP,MAAM,QAAQ,IAAI,MAAM,mBAAmB;IAC3C,MAAM,MAAA,GAAS,GAAG,mBAAmB,CAAA,CAAA,EAAI,IAAI,CAAA,CAAA,EAAI,GAAG,CAAA,CAAA,EAAI,UAAU,CAAA,CAAA,CAAA;IAClE,MAAM,YAAA,GAAe,mBAAmB,eAAA;IACxC,OAAO,MAAA,CAAO,OAAO,KAAK;IAC1B,MAAM;AACR;AAEA,SAAS,mBAAmB,GAAA,EAAa,aAAA,EAAmD;IAC1F,OAAO,kBAAkB,OAAO,KAAK,iBAAiB;AACxD;AAEA,SAAS,sBAAsB,GAAA,EAAa,aAAA,EAA4C;IACtF,oBAAoB,KAAK;QACvB,cAAc,mBAAmB,mBAAA;QACjC,eAAe,mBAAmB,KAAK,aAAa;IACtD,CAAC;AACH;AAEA,SAAS,sBAAsB,GAAA,EAAa,aAAA,EAA4C;IACtF,oBAAoB,KAAK;QACvB,cAAc,mBAAmB,mBAAA;QACjC,eAAe,mBAAmB,KAAK,aAAa;IACtD,CAAC;AACH;AASA,SAAS,sBAAsB,KAAA,EAAuE;IACpG,IAAI,OAAO,UAAU,YAAY,UAAU,QAAQ,CAAA,CAAE,YAAY,KAAA,KAAU,OAAO,MAAM,MAAA,KAAW,UAAU;QAC3G,OAAO;IACT;IAEA,MAAM,SAAS,MAAM,MAAA,CAAO,KAAA,CAAM,GAAG;IACrC,MAAM,CAAC,WAAW,IAAI,CAAA,GAAI;IAC1B,MAAM,cAAc,OAAO,KAAA,CAAM,GAAG,CAAA,CAAE,EAAE,IAAA,CAAK,GAAG;IAChD,MAAM,SAAS,OAAO,EAAA,CAAG,CAAA,CAAE;IAE3B,MAAM,aAAa,OAAO,MAAM;IAEhC,OACE,cAAc,uBAAA,CACb,SAAS,aAAa,SAAS,MAAA,KAChC,OAAO,gBAAgB,YACvB,CAAC,MAAM,UAAU,KACjB,eAAe;AAEnB;AAEA,SAAS,wBAAwB,KAAA,EAAyE;IACxG,IAAI,sBAAsB,KAAK,KAAK,kBAAkB,OAAO;QAC3D,OAAO,MAAM,YAAA,KAAiB,mBAAmB,mBAAA;IACnD;IAEA,OAAO;AACT;AAEA,SAAS,wBAAwB,KAAA,EAAyE;IACxG,IAAI,sBAAsB,KAAK,KAAK,kBAAkB,OAAO;QAC3D,OAAO,MAAM,YAAA,KAAiB,mBAAmB,mBAAA;IACnD;IAEA,OAAO;AACT;AAEA,SAAS,0BAA0B,KAAA,EAAkD;IACnF,OAAO,6BAA6B,KAAK,MAAM,2BAA2B,YAAA;AAC5E;AAMA,SAAS,eAAsB;IAC7B,MAAM,QAAQ,IAAI,MAAM,8BAA8B;IACtD,MAAM,MAAA,GAAS,GAAG,8BAA8B,CAAA,CAAA,EAAI,2BAA2B,YAAY,EAAA;IAC3F,MAAM;AACR", "debugId": null}}, {"offset": {"line": 1503, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/server/nextFetcher.ts"], "sourcesContent": ["type Fetcher = typeof globalThis.fetch;\n\n/**\n * Based on nextjs internal implementation https://github.com/vercel/next.js/blob/6185444e0a944a82e7719ac37dad8becfed86acd/packages/next/src/server/lib/patch-fetch.ts#L23\n */\ntype NextFetcher = Fetcher & {\n  readonly __nextPatched: true;\n  readonly __nextGetStaticStore: () => { getStore: () => StaticGenerationAsyncStorage | undefined };\n};\n\n/**\n * Full type can be found https://github.com/vercel/next.js/blob/6185444e0a944a82e7719ac37dad8becfed86acd/packages/next/src/client/components/static-generation-async-storage.external.ts#L4\n */\ninterface StaticGenerationAsyncStorage {\n  /**\n   * Available for Next 14\n   */\n  readonly pagePath?: string;\n  /**\n   * Available for Next 15\n   */\n  readonly page?: string;\n}\n\nfunction isNextFetcher(fetch: Fetcher | NextFetcher): fetch is NextFetcher {\n  return '__nextPatched' in fetch && fetch.__nextPatched === true;\n}\n\nexport { isNextFetcher };\n"], "names": [], "mappings": ";;;;;AAwBA,SAAS,cAAc,KAAA,EAAoD;IACzE,OAAO,mBAAmB,SAAS,MAAM,aAAA,KAAkB;AAC7D", "debugId": null}}, {"offset": {"line": 1517, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/server/protect.ts"], "sourcesContent": ["import type { AuthObject } from '@clerk/backend';\nimport type {\n  AuthenticatedMachineObject,\n  AuthenticateRequestOptions,\n  InferAuthObjectFromToken,\n  InferAuthObjectFromTokenArray,\n  RedirectFun,\n  SignedInAuthObject,\n} from '@clerk/backend/internal';\nimport { constants, isTokenTypeAccepted, TokenType } from '@clerk/backend/internal';\nimport type {\n  CheckAuthorizationFromSessionClaims,\n  CheckAuthorizationParamsFromSessionClaims,\n  CheckAuthorizationParamsWithCustomPermissions,\n  CheckAuthorizationWithCustomPermissions,\n  OrganizationCustomPermissionKey,\n} from '@clerk/types';\n\nimport { constants as nextConstants } from '../constants';\nimport { isNextFetcher } from './nextFetcher';\n\ntype AuthProtectOptions = {\n  /**\n   * The token type to check.\n   */\n  token?: AuthenticateRequestOptions['acceptsToken'];\n  /**\n   * The URL to redirect the user to if they are not authorized.\n   */\n  unauthorizedUrl?: string;\n  /**\n   * The URL to redirect the user to if they are not authenticated.\n   */\n  unauthenticatedUrl?: string;\n};\n\n/**\n * Throws a Nextjs notFound error if user is not authenticated or authorized.\n */\nexport interface AuthProtect {\n  /**\n   * @example\n   * auth.protect({ permission: 'org:admin:example1' });\n   * auth.protect({ role: 'admin' });\n   */\n  <P extends OrganizationCustomPermissionKey>(\n    params?: CheckAuthorizationParamsFromSessionClaims<P>,\n    options?: AuthProtectOptions,\n  ): Promise<SignedInAuthObject>;\n\n  /**\n   * @example\n   * auth.protect(has => has({ permission: 'org:admin:example1' }));\n   */\n  (\n    params?: (has: CheckAuthorizationFromSessionClaims) => boolean,\n    options?: AuthProtectOptions,\n  ): Promise<SignedInAuthObject>;\n\n  /**\n   * @example\n   * auth.protect({ token: 'session_token' });\n   */\n  <T extends TokenType>(\n    options?: AuthProtectOptions & { token: T },\n  ): Promise<InferAuthObjectFromToken<T, SignedInAuthObject, AuthenticatedMachineObject>>;\n\n  /**\n   * @example\n   * auth.protect({ token: ['session_token', 'm2m_token'] });\n   */\n  <T extends TokenType[]>(\n    options?: AuthProtectOptions & { token: T },\n  ): Promise<InferAuthObjectFromTokenArray<T, SignedInAuthObject, AuthenticatedMachineObject>>;\n\n  /**\n   * @example\n   * auth.protect({ token: 'any' });\n   */\n  (options?: AuthProtectOptions & { token: 'any' }): Promise<SignedInAuthObject | AuthenticatedMachineObject>;\n\n  /**\n   * @example\n   * auth.protect();\n   */\n  (options?: AuthProtectOptions): Promise<SignedInAuthObject>;\n}\n\nexport function createProtect(opts: {\n  request: Request;\n  authObject: AuthObject;\n  /**\n   * middleware and pages throw a notFound error if signed out\n   * but the middleware needs to throw an error it can catch\n   * use this callback to customise the behavior\n   */\n  notFound: () => never;\n  /**\n   * see {@link notFound} above\n   */\n  redirect: (url: string) => void;\n  /**\n   * For m2m requests, throws a 401 response\n   */\n  unauthorized: () => void;\n  /**\n   * protect() in middleware redirects to signInUrl if signed out\n   * protect() in pages throws a notFound error if signed out\n   * use this callback to customise the behavior\n   */\n  redirectToSignIn: RedirectFun<unknown>;\n}): AuthProtect {\n  const { redirectToSignIn, authObject, redirect, notFound, request, unauthorized } = opts;\n\n  return (async (...args: any[]) => {\n    const paramsOrFunction = getAuthorizationParams(args[0]);\n    const unauthenticatedUrl = (args[0]?.unauthenticatedUrl || args[1]?.unauthenticatedUrl) as string | undefined;\n    const unauthorizedUrl = (args[0]?.unauthorizedUrl || args[1]?.unauthorizedUrl) as string | undefined;\n    const requestedToken = args[0]?.token || args[1]?.token || TokenType.SessionToken;\n\n    const handleUnauthenticated = () => {\n      if (unauthenticatedUrl) {\n        return redirect(unauthenticatedUrl);\n      }\n      if (isPageRequest(request)) {\n        // TODO: Handle runtime values. What happens if runtime values are set in middleware and in ClerkProvider as well?\n        return redirectToSignIn();\n      }\n      return notFound();\n    };\n\n    const handleUnauthorized = () => {\n      // For machine tokens, return a 401 response\n      if (authObject.tokenType !== TokenType.SessionToken) {\n        return unauthorized();\n      }\n\n      if (unauthorizedUrl) {\n        return redirect(unauthorizedUrl);\n      }\n      return notFound();\n    };\n\n    if (!isTokenTypeAccepted(authObject.tokenType, requestedToken)) {\n      return handleUnauthorized();\n    }\n\n    if (authObject.tokenType !== TokenType.SessionToken) {\n      // For machine tokens, we only check if they're authenticated\n      // They don't have session status or organization permissions\n      if (!authObject.isAuthenticated) {\n        return handleUnauthorized();\n      }\n      return authObject;\n    }\n\n    /**\n     * Redirects the user back to the tasks URL if their session status is pending\n     */\n    if (authObject.sessionStatus === 'pending') {\n      return handleUnauthenticated();\n    }\n\n    /**\n     * User is not authenticated\n     */\n    if (!authObject.userId) {\n      return handleUnauthenticated();\n    }\n\n    /**\n     * User is authenticated\n     */\n    if (!paramsOrFunction) {\n      return authObject;\n    }\n\n    /**\n     * if a function is passed and returns false then throw not found\n     */\n    if (typeof paramsOrFunction === 'function') {\n      if (paramsOrFunction(authObject.has)) {\n        return authObject;\n      }\n      return handleUnauthorized();\n    }\n\n    /**\n     * Checking if user is authorized when permission or role is passed\n     */\n    if (authObject.has(paramsOrFunction)) {\n      return authObject;\n    }\n\n    return handleUnauthorized();\n  }) as AuthProtect;\n}\n\nconst getAuthorizationParams = (arg: any) => {\n  if (!arg) {\n    return undefined;\n  }\n\n  // Skip authorization check if the arg contains any of these options\n  if (arg.unauthenticatedUrl || arg.unauthorizedUrl || arg.token) {\n    return undefined;\n  }\n\n  // Skip if it's just a token-only object\n  if (Object.keys(arg).length === 1 && 'token' in arg) {\n    return undefined;\n  }\n\n  // Return the authorization params/function\n  return arg as\n    | CheckAuthorizationParamsWithCustomPermissions\n    | ((has: CheckAuthorizationWithCustomPermissions) => boolean);\n};\n\nconst isServerActionRequest = (req: Request) => {\n  return (\n    !!req.headers.get(nextConstants.Headers.NextUrl) &&\n    (req.headers.get(constants.Headers.Accept)?.includes('text/x-component') ||\n      req.headers.get(constants.Headers.ContentType)?.includes('multipart/form-data') ||\n      !!req.headers.get(nextConstants.Headers.NextAction))\n  );\n};\n\nconst isPageRequest = (req: Request): boolean => {\n  return (\n    req.headers.get(constants.Headers.SecFetchDest) === 'document' ||\n    req.headers.get(constants.Headers.SecFetchDest) === 'iframe' ||\n    req.headers.get(constants.Headers.Accept)?.includes('text/html') ||\n    isAppRouterInternalNavigation(req) ||\n    isPagesRouterInternalNavigation(req)\n  );\n};\n\nconst isAppRouterInternalNavigation = (req: Request) =>\n  (!!req.headers.get(nextConstants.Headers.NextUrl) && !isServerActionRequest(req)) || isPagePathAvailable();\n\nconst isPagePathAvailable = () => {\n  const __fetch = globalThis.fetch;\n\n  if (!isNextFetcher(__fetch)) {\n    return false;\n  }\n\n  const { page, pagePath } = __fetch.__nextGetStaticStore().getStore() || {};\n\n  return Boolean(\n    // available on next@14\n    pagePath ||\n      // available on next@15\n      page,\n  );\n};\n\nconst isPagesRouterInternalNavigation = (req: Request) => !!req.headers.get(nextConstants.Headers.NextjsData);\n\n// /**\n//  * In case we want to handle router handlers and server actions differently in the future\n//  */\n// const isApiRouteRequest = (req: Request) => {\n//   return !isPageRequest(req) && !isServerActionRequest(req);\n// };\n"], "names": [], "mappings": ";;;;;AASA,SAAS,WAAW,qBAAqB,iBAAiB;AAS1D,SAAS,aAAa,qBAAqB;AAC3C,SAAS,qBAAqB;;;;;AAqEvB,SAAS,cAAc,IAAA,EAuBd;IACd,MAAM,EAAE,gBAAA,EAAkB,UAAA,EAAY,QAAA,EAAU,QAAA,EAAU,OAAA,EAAS,YAAA,CAAa,CAAA,GAAI;IAEpF,OAAQ,OAAA,GAAU,SAAgB;QAlHpC,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA;QAmHI,MAAM,mBAAmB,uBAAuB,IAAA,CAAK,CAAC,CAAC;QACvD,MAAM,qBAAA,CAAA,CAAsB,KAAA,IAAA,CAAK,CAAC,CAAA,KAAN,OAAA,KAAA,IAAA,GAAS,kBAAA,KAAA,CAAA,CAAsB,KAAA,IAAA,CAAK,CAAC,CAAA,KAAN,OAAA,KAAA,IAAA,GAAS,kBAAA;QACpE,MAAM,kBAAA,CAAA,CAAmB,KAAA,IAAA,CAAK,CAAC,CAAA,KAAN,OAAA,KAAA,IAAA,GAAS,eAAA,KAAA,CAAA,CAAmB,KAAA,IAAA,CAAK,CAAC,CAAA,KAAN,OAAA,KAAA,IAAA,GAAS,eAAA;QAC9D,MAAM,iBAAA,CAAA,CAAiB,KAAA,IAAA,CAAK,CAAC,CAAA,KAAN,OAAA,KAAA,IAAA,GAAS,KAAA,KAAA,CAAA,CAAS,KAAA,IAAA,CAAK,CAAC,CAAA,KAAN,OAAA,KAAA,IAAA,GAAS,KAAA,KAAS,2MAAA,CAAU,YAAA;QAErE,MAAM,wBAAwB,MAAM;YAClC,IAAI,oBAAoB;gBACtB,OAAO,SAAS,kBAAkB;YACpC;YACA,IAAI,cAAc,OAAO,GAAG;gBAE1B,OAAO,iBAAiB;YAC1B;YACA,OAAO,SAAS;QAClB;QAEA,MAAM,qBAAqB,MAAM;YAE/B,IAAI,WAAW,SAAA,KAAc,2MAAA,CAAU,YAAA,EAAc;gBACnD,OAAO,aAAa;YACtB;YAEA,IAAI,iBAAiB;gBACnB,OAAO,SAAS,eAAe;YACjC;YACA,OAAO,SAAS;QAClB;QAEA,IAAI,KAAC,qNAAA,EAAoB,WAAW,SAAA,EAAW,cAAc,GAAG;YAC9D,OAAO,mBAAmB;QAC5B;QAEA,IAAI,WAAW,SAAA,KAAc,2MAAA,CAAU,YAAA,EAAc;YAGnD,IAAI,CAAC,WAAW,eAAA,EAAiB;gBAC/B,OAAO,mBAAmB;YAC5B;YACA,OAAO;QACT;QAKA,IAAI,WAAW,aAAA,KAAkB,WAAW;YAC1C,OAAO,sBAAsB;QAC/B;QAKA,IAAI,CAAC,WAAW,MAAA,EAAQ;YACtB,OAAO,sBAAsB;QAC/B;QAKA,IAAI,CAAC,kBAAkB;YACrB,OAAO;QACT;QAKA,IAAI,OAAO,qBAAqB,YAAY;YAC1C,IAAI,iBAAiB,WAAW,GAAG,GAAG;gBACpC,OAAO;YACT;YACA,OAAO,mBAAmB;QAC5B;QAKA,IAAI,WAAW,GAAA,CAAI,gBAAgB,GAAG;YACpC,OAAO;QACT;QAEA,OAAO,mBAAmB;IAC5B;AACF;AAEA,MAAM,yBAAyB,CAAC,QAAa;IAC3C,IAAI,CAAC,KAAK;QACR,OAAO,KAAA;IACT;IAGA,IAAI,IAAI,kBAAA,IAAsB,IAAI,eAAA,IAAmB,IAAI,KAAA,EAAO;QAC9D,OAAO,KAAA;IACT;IAGA,IAAI,OAAO,IAAA,CAAK,GAAG,EAAE,MAAA,KAAW,KAAK,WAAW,KAAK;QACnD,OAAO,KAAA;IACT;IAGA,OAAO;AAGT;AAEA,MAAM,wBAAwB,CAAC,QAAiB;IA3NhD,IAAA,IAAA;IA4NE,OACE,CAAC,CAAC,IAAI,OAAA,CAAQ,GAAA,CAAI,wLAAA,CAAc,OAAA,CAAQ,OAAO,KAAA,CAAA,CAAA,CAC9C,KAAA,IAAI,OAAA,CAAQ,GAAA,CAAI,2MAAA,CAAU,OAAA,CAAQ,MAAM,CAAA,KAAxC,OAAA,KAAA,IAAA,GAA2C,QAAA,CAAS,mBAAA,KAAA,CAAA,CACnD,KAAA,IAAI,OAAA,CAAQ,GAAA,CAAI,2MAAA,CAAU,OAAA,CAAQ,WAAW,CAAA,KAA7C,OAAA,KAAA,IAAA,GAAgD,QAAA,CAAS,sBAAA,KACzD,CAAC,CAAC,IAAI,OAAA,CAAQ,GAAA,CAAI,wLAAA,CAAc,OAAA,CAAQ,UAAU,CAAA;AAExD;AAEA,MAAM,gBAAgB,CAAC,QAA0B;IApOjD,IAAA;IAqOE,OACE,IAAI,OAAA,CAAQ,GAAA,CAAI,2MAAA,CAAU,OAAA,CAAQ,YAAY,MAAM,cACpD,IAAI,OAAA,CAAQ,GAAA,CAAI,2MAAA,CAAU,OAAA,CAAQ,YAAY,MAAM,YAAA,CAAA,CACpD,KAAA,IAAI,OAAA,CAAQ,GAAA,CAAI,2MAAA,CAAU,OAAA,CAAQ,MAAM,CAAA,KAAxC,OAAA,KAAA,IAAA,GAA2C,QAAA,CAAS,YAAA,KACpD,8BAA8B,GAAG,KACjC,gCAAgC,GAAG;AAEvC;AAEA,MAAM,gCAAgC,CAAC,MACpC,CAAC,CAAC,IAAI,OAAA,CAAQ,GAAA,CAAI,wLAAA,CAAc,OAAA,CAAQ,OAAO,KAAK,CAAC,sBAAsB,GAAG,KAAM,oBAAoB;AAE3G,MAAM,sBAAsB,MAAM;IAChC,MAAM,UAAU,WAAW,KAAA;IAE3B,IAAI,KAAC,wMAAA,EAAc,OAAO,GAAG;QAC3B,OAAO;IACT;IAEA,MAAM,EAAE,IAAA,EAAM,QAAA,CAAS,CAAA,GAAI,QAAQ,oBAAA,CAAqB,EAAE,QAAA,CAAS,KAAK,CAAC;IAEzE,OAAO,QAAA,uBAAA;IAEL,YAAA,uBAAA;IAEE;AAEN;AAEA,MAAM,kCAAkC,CAAC,MAAiB,CAAC,CAAC,IAAI,OAAA,CAAQ,GAAA,CAAI,wLAAA,CAAc,OAAA,CAAQ,UAAU", "debugId": null}}, {"offset": {"line": 1623, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/app-router/server/utils.ts"], "sourcesContent": ["import { NextRequest } from 'next/server';\n\nexport const isPrerenderingBailout = (e: unknown) => {\n  if (!(e instanceof Error) || !('message' in e)) {\n    return false;\n  }\n\n  const { message } = e;\n\n  const lowerCaseInput = message.toLowerCase();\n  const dynamicServerUsage = lowerCaseInput.includes('dynamic server usage');\n  const bailOutPrerendering = lowerCaseInput.includes('this page needs to bail out of prerendering');\n\n  // note: new error message syntax introduced in next@14.1.1-canary.21\n  // but we still want to support older versions.\n  // https://github.com/vercel/next.js/pull/61332 (dynamic-rendering.ts:153)\n  const routeRegex = /Route .*? needs to bail out of prerendering at this point because it used .*?./;\n\n  return routeRegex.test(message) || dynamicServerUsage || bailOutPrerendering;\n};\n\nexport async function buildRequestLike(): Promise<NextRequest> {\n  try {\n    // Dynamically import next/headers, otherwise Next12 apps will break\n    // @ts-expect-error: Cannot find module 'next/headers' or its corresponding type declarations.ts(2307)\n    const { headers } = await import('next/headers');\n    const resolvedHeaders = await headers();\n    return new NextRequest('https://placeholder.com', { headers: resolvedHeaders });\n  } catch (e: any) {\n    // rethrow the error when react throws a prerendering bailout\n    // https://nextjs.org/docs/messages/ppr-caught-error\n    if (e && isPrerenderingBailout(e)) {\n      throw e;\n    }\n\n    throw new Error(\n      `Clerk: auth(), currentUser() and clerkClient(), are only supported in App Router (/app directory).\\nIf you're using /pages, try getAuth() instead.\\nOriginal error: ${e}`,\n    );\n  }\n}\n\n// Original source: https://github.com/vercel/next.js/blob/canary/packages/next/src/server/app-render/get-script-nonce-from-header.tsx\nexport function getScriptNonceFromHeader(cspHeaderValue: string): string | undefined {\n  const directives = cspHeaderValue\n    // Directives are split by ';'.\n    .split(';')\n    .map(directive => directive.trim());\n\n  // First try to find the directive for the 'script-src', otherwise try to\n  // fallback to the 'default-src'.\n  const directive =\n    directives.find(dir => dir.startsWith('script-src')) || directives.find(dir => dir.startsWith('default-src'));\n\n  // If no directive could be found, then we're done.\n  if (!directive) {\n    return;\n  }\n\n  // Extract the nonce from the directive\n  const nonce = directive\n    .split(' ')\n    // Remove the 'strict-src'/'default-src' string, this can't be the nonce.\n    .slice(1)\n    .map(source => source.trim())\n    // Find the first source with the 'nonce-' prefix.\n    .find(source => source.startsWith(\"'nonce-\") && source.length > 8 && source.endsWith(\"'\"))\n    // Grab the nonce by trimming the 'nonce-' prefix.\n    ?.slice(7, -1);\n\n  // If we couldn't find the nonce, then we're done.\n  if (!nonce) {\n    return;\n  }\n\n  // Don't accept the nonce value if it contains HTML escape characters.\n  // Technically, the spec requires a base64'd value, but this is just an\n  // extra layer.\n  if (/[&><\\u2028\\u2029]/g.test(nonce)) {\n    throw new Error(\n      'Nonce value from Content-Security-Policy contained invalid HTML escape characters, which is disallowed for security reasons. Make sure that your nonce value does not contain the following characters: `<`, `>`, `&`',\n    );\n  }\n\n  return nonce;\n}\n"], "names": ["directive"], "mappings": ";;;;;;;;AAAA,SAAS,mBAAmB;;;AAErB,MAAM,wBAAwB,CAAC,MAAe;IACnD,IAAI,CAAA,CAAE,aAAa,KAAA,KAAU,CAAA,CAAE,aAAa,CAAA,GAAI;QAC9C,OAAO;IACT;IAEA,MAAM,EAAE,OAAA,CAAQ,CAAA,GAAI;IAEpB,MAAM,iBAAiB,QAAQ,WAAA,CAAY;IAC3C,MAAM,qBAAqB,eAAe,QAAA,CAAS,sBAAsB;IACzE,MAAM,sBAAsB,eAAe,QAAA,CAAS,6CAA6C;IAKjG,MAAM,aAAa;IAEnB,OAAO,WAAW,IAAA,CAAK,OAAO,KAAK,sBAAsB;AAC3D;AAEA,eAAsB,mBAAyC;IAC7D,IAAI;QAGF,MAAM,EAAE,OAAA,CAAQ,CAAA,GAAI,MAAM,OAAO,cAAc;QAC/C,MAAM,kBAAkB,MAAM,QAAQ;QACtC,OAAO,IAAI,2JAAA,CAAY,2BAA2B;YAAE,SAAS;QAAgB,CAAC;IAChF,EAAA,OAAS,GAAQ;QAGf,IAAI,KAAK,sBAAsB,CAAC,GAAG;YACjC,MAAM;QACR;QAEA,MAAM,IAAI,MACR,CAAA;;gBAAA,EAAuK,CAAC,EAAA;IAE5K;AACF;AAGO,SAAS,yBAAyB,cAAA,EAA4C;IA1CrF,IAAA;IA2CE,MAAM,aAAa,eAEhB,KAAA,CAAM,GAAG,EACT,GAAA,CAAI,CAAAA,aAAaA,WAAU,IAAA,CAAK,CAAC;IAIpC,MAAM,YACJ,WAAW,IAAA,CAAK,CAAA,MAAO,IAAI,UAAA,CAAW,YAAY,CAAC,KAAK,WAAW,IAAA,CAAK,CAAA,MAAO,IAAI,UAAA,CAAW,aAAa,CAAC;IAG9G,IAAI,CAAC,WAAW;QACd;IACF;IAGA,MAAM,QAAA,CAAQ,KAAA,UACX,KAAA,CAAM,GAAG,EAET,KAAA,CAAM,CAAC,EACP,GAAA,CAAI,CAAA,SAAU,OAAO,IAAA,CAAK,CAAC,EAE3B,IAAA,CAAK,CAAA,SAAU,OAAO,UAAA,CAAW,SAAS,KAAK,OAAO,MAAA,GAAS,KAAK,OAAO,QAAA,CAAS,GAAG,CAAC,CAAA,KAN7E,OAAA,KAAA,IAAA,GAQV,KAAA,CAAM,GAAG,CAAA;IAGb,IAAI,CAAC,OAAO;QACV;IACF;IAKA,IAAI,qBAAqB,IAAA,CAAK,KAAK,GAAG;QACpC,MAAM,IAAI,MACR;IAEJ;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1683, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/app-router/server/auth.ts"], "sourcesContent": ["import type { AuthObject, InvalidTokenAuthObject, MachineAuthObject, SessionAuthObject } from '@clerk/backend';\nimport type {\n  AuthenticateRequestOptions,\n  InferAuthObjectFromToken,\n  InferAuthObjectFromTokenArray,\n  RedirectFun,\n  SessionTokenType,\n} from '@clerk/backend/internal';\nimport { constants, createClerkRequest, createRedirect, TokenType } from '@clerk/backend/internal';\nimport type { PendingSessionOptions } from '@clerk/types';\nimport { notFound, redirect } from 'next/navigation';\n\nimport { PUBLISHABLE_KEY, SIGN_IN_URL, SIGN_UP_URL } from '../../server/constants';\nimport { createAsyncGetAuth } from '../../server/createGetAuth';\nimport { authAuthHeaderMissing } from '../../server/errors';\nimport { getAuthKeyFromRequest, getHeader } from '../../server/headers-utils';\nimport { unauthorized } from '../../server/nextErrors';\nimport type { AuthProtect } from '../../server/protect';\nimport { createProtect } from '../../server/protect';\nimport { decryptClerkRequestData } from '../../server/utils';\nimport { isNextWithUnstableServerActions } from '../../utils/sdk-versions';\nimport { buildRequestLike } from './utils';\n\n/**\n * `Auth` object of the currently active user and the `redirectToSignIn()` method.\n */\ntype SessionAuthWithRedirect<TRedirect> = SessionAuthObject & {\n  /**\n   * The `auth()` helper returns the `redirectToSignIn()` method, which you can use to redirect the user to the sign-in page.\n   *\n   * @param [returnBackUrl] {string | URL} - The URL to redirect the user back to after they sign in.\n   *\n   * > [!NOTE]\n   * > `auth()` on the server-side can only access redirect URLs defined via [environment variables](https://clerk.com/docs/deployments/clerk-environment-variables#sign-in-and-sign-up-redirects) or [`clerkMiddleware` dynamic keys](https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys).\n   */\n  redirectToSignIn: RedirectFun<TRedirect>;\n\n  /**\n   * The `auth()` helper returns the `redirectToSignUp()` method, which you can use to redirect the user to the sign-up page.\n   *\n   * @param [returnBackUrl] {string | URL} - The URL to redirect the user back to after they sign up.\n   *\n   * > [!NOTE]\n   * > `auth()` on the server-side can only access redirect URLs defined via [environment variables](https://clerk.com/docs/deployments/clerk-environment-variables#sign-in-and-sign-up-redirects) or [`clerkMiddleware` dynamic keys](https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys).\n   */\n  redirectToSignUp: RedirectFun<TRedirect>;\n};\n\nexport type AuthOptions = PendingSessionOptions & { acceptsToken?: AuthenticateRequestOptions['acceptsToken'] };\n\nexport interface AuthFn<TRedirect = ReturnType<typeof redirect>> {\n  /**\n   * @example\n   * const authObject = await auth({ acceptsToken: ['session_token', 'api_key'] })\n   */\n  <T extends TokenType[]>(\n    options: AuthOptions & { acceptsToken: T },\n  ): Promise<\n    | InferAuthObjectFromTokenArray<\n        T,\n        SessionAuthWithRedirect<TRedirect>,\n        MachineAuthObject<Exclude<T[number], SessionTokenType>>\n      >\n    | InvalidTokenAuthObject\n  >;\n\n  /**\n   * @example\n   * const authObject = await auth({ acceptsToken: 'session_token' })\n   */\n  <T extends TokenType>(\n    options: AuthOptions & { acceptsToken: T },\n  ): Promise<\n    InferAuthObjectFromToken<T, SessionAuthWithRedirect<TRedirect>, MachineAuthObject<Exclude<T, SessionTokenType>>>\n  >;\n\n  /**\n   * @example\n   * const authObject = await auth({ acceptsToken: 'any' })\n   */\n  (options: AuthOptions & { acceptsToken: 'any' }): Promise<AuthObject>;\n\n  /**\n   * @example\n   * const authObject = await auth()\n   */\n  (options?: PendingSessionOptions): Promise<SessionAuthWithRedirect<TRedirect>>;\n\n  /**\n   * `auth` includes a single property, the `protect()` method, which you can use in two ways:\n   * - to check if a user is authenticated (signed in)\n   * - to check if a user is authorized (has the correct roles or permissions) to access something, such as a component or a route handler\n   *\n   * The following table describes how auth.protect() behaves based on user authentication or authorization status:\n   *\n   * | Authenticated | Authorized | `auth.protect()` will |\n   * | - | - | - |\n   * | Yes | Yes | Return the [`Auth`](https://clerk.com/docs/references/backend/types/auth-object) object. |\n   * | Yes | No | Return a `404` error. |\n   * | No | No | Redirect the user to the sign-in page\\*. |\n   *\n   * > [!IMPORTANT]\n   * > \\*For non-document requests, such as API requests, `auth.protect()` returns a `404` error to users who aren't authenticated.\n   *\n   * `auth.protect()` can be used to check if a user is authenticated or authorized to access certain parts of your application or even entire routes. See detailed examples in the [dedicated guide](https://clerk.com/docs/organizations/verify-user-permissions).\n   */\n  protect: AuthProtect;\n}\n\n/**\n * The `auth()` helper returns the [`Auth`](https://clerk.com/docs/references/backend/types/auth-object) object of the currently active user, as well as the [`redirectToSignIn()`](https://clerk.com/docs/references/nextjs/auth#redirect-to-sign-in) method.\n *\n * - Only available for App Router.\n * - Only works on the server-side, such as in Server Components, Route Handlers, and Server Actions.\n * - Requires [`clerkMiddleware()`](https://clerk.com/docs/references/nextjs/clerk-middleware) to be configured.\n */\nexport const auth: AuthFn = (async (options?: AuthOptions) => {\n  // eslint-disable-next-line @typescript-eslint/no-require-imports\n  require('server-only');\n\n  const request = await buildRequestLike();\n\n  const stepsBasedOnSrcDirectory = async () => {\n    if (isNextWithUnstableServerActions) {\n      return [];\n    }\n\n    try {\n      const isSrcAppDir = await import('../../server/fs/middleware-location.js').then(m => m.hasSrcAppDir());\n      return [`Your Middleware exists at ./${isSrcAppDir ? 'src/' : ''}middleware.(ts|js)`];\n    } catch {\n      return [];\n    }\n  };\n  const authObject = await createAsyncGetAuth({\n    debugLoggerName: 'auth()',\n    noAuthStatusMessage: authAuthHeaderMissing('auth', await stepsBasedOnSrcDirectory()),\n  })(request, {\n    treatPendingAsSignedOut: options?.treatPendingAsSignedOut,\n    acceptsToken: options?.acceptsToken ?? TokenType.SessionToken,\n  });\n\n  const clerkUrl = getAuthKeyFromRequest(request, 'ClerkUrl');\n\n  const createRedirectForRequest = (...args: Parameters<RedirectFun<never>>) => {\n    const { returnBackUrl } = args[0] || {};\n    const clerkRequest = createClerkRequest(request);\n    const devBrowserToken =\n      clerkRequest.clerkUrl.searchParams.get(constants.QueryParameters.DevBrowser) ||\n      clerkRequest.cookies.get(constants.Cookies.DevBrowser);\n\n    const encryptedRequestData = getHeader(request, constants.Headers.ClerkRequestData);\n    const decryptedRequestData = decryptClerkRequestData(encryptedRequestData);\n    return [\n      createRedirect({\n        redirectAdapter: redirect,\n        devBrowserToken: devBrowserToken,\n        baseUrl: clerkRequest.clerkUrl.toString(),\n        publishableKey: decryptedRequestData.publishableKey || PUBLISHABLE_KEY,\n        signInUrl: decryptedRequestData.signInUrl || SIGN_IN_URL,\n        signUpUrl: decryptedRequestData.signUpUrl || SIGN_UP_URL,\n        sessionStatus: authObject.tokenType === TokenType.SessionToken ? authObject.sessionStatus : null,\n      }),\n      returnBackUrl === null ? '' : returnBackUrl || clerkUrl?.toString(),\n    ] as const;\n  };\n\n  const redirectToSignIn: RedirectFun<never> = (opts = {}) => {\n    const [r, returnBackUrl] = createRedirectForRequest(opts);\n    return r.redirectToSignIn({\n      returnBackUrl,\n    });\n  };\n\n  const redirectToSignUp: RedirectFun<never> = (opts = {}) => {\n    const [r, returnBackUrl] = createRedirectForRequest(opts);\n    return r.redirectToSignUp({\n      returnBackUrl,\n    });\n  };\n\n  if (authObject.tokenType === TokenType.SessionToken) {\n    return Object.assign(authObject, { redirectToSignIn, redirectToSignUp });\n  }\n\n  return authObject;\n}) as AuthFn;\n\nauth.protect = async (...args: any[]) => {\n  // eslint-disable-next-line @typescript-eslint/no-require-imports\n  require('server-only');\n\n  const request = await buildRequestLike();\n  const requestedToken = args?.[0]?.token || args?.[1]?.token || TokenType.SessionToken;\n  const authObject = await auth({ acceptsToken: requestedToken });\n\n  const protect = createProtect({\n    request,\n    authObject,\n    redirectToSignIn: authObject.redirectToSignIn,\n    notFound,\n    redirect,\n    unauthorized,\n  });\n\n  return protect(...args);\n};\n"], "names": [], "mappings": ";;;;;AAQA,SAAS,WAAW,oBAAoB,gBAAgB,iBAAiB;;AAEzE,SAAS,UAAU,gBAAgB;AAEnC,SAAS,iBAAiB,aAAa,mBAAmB;AAC1D,SAAS,0BAA0B;AACnC,SAAS,6BAA6B;AACtC,SAAS,uBAAuB,iBAAiB;AACjD,SAAS,oBAAoB;AAE7B,SAAS,qBAAqB;AAC9B,SAAS,+BAA+B;AACxC,SAAS,uCAAuC;AAChD,SAAS,wBAAwB;;;;;;;;;;;;;AA+F1B,MAAM,OAAgB,OAAO,YAA0B;IApH9D,IAAA;;IAwHE,MAAM,UAAU,UAAM,sNAAA,CAAiB;IAEvC,MAAM,2BAA2B,YAAY;QAC3C,IAAI,6NAAA,EAAiC;YACnC,OAAO,CAAC,CAAA;QACV;QAEA,IAAI;YACF,MAAM,cAAc,MAAM,OAAO,wCAAwC,2GAAE,IAAA,CAAK,CAAA,IAAK,EAAE,YAAA,CAAa,CAAC;YACrG,OAAO;gBAAC,CAAA,4BAAA,EAA+B,cAAc,SAAS,EAAE,CAAA,kBAAA,CAAoB;aAAA;QACtF,EAAA,OAAQ;YACN,OAAO,CAAC,CAAA;QACV;IACF;IACA,MAAM,aAAa,UAAM,+MAAA,EAAmB;QAC1C,iBAAiB;QACjB,yBAAqB,2MAAA,EAAsB,QAAQ,MAAM,yBAAyB,CAAC;IACrF,CAAC,EAAE,SAAS;QACV,yBAAyB,WAAA,OAAA,KAAA,IAAA,QAAS,uBAAA;QAClC,cAAA,CAAc,KAAA,WAAA,OAAA,KAAA,IAAA,QAAS,YAAA,KAAT,OAAA,KAAyB,2MAAA,CAAU,YAAA;IACnD,CAAC;IAED,MAAM,eAAW,qNAAA,EAAsB,SAAS,UAAU;IAE1D,MAAM,2BAA2B,CAAA,GAAI,SAAyC;QAC5E,MAAM,EAAE,aAAA,CAAc,CAAA,GAAI,IAAA,CAAK,CAAC,CAAA,IAAK,CAAC;QACtC,MAAM,mBAAe,oNAAA,EAAmB,OAAO;QAC/C,MAAM,kBACJ,aAAa,QAAA,CAAS,YAAA,CAAa,GAAA,CAAI,2MAAA,CAAU,eAAA,CAAgB,UAAU,KAC3E,aAAa,OAAA,CAAQ,GAAA,CAAI,2MAAA,CAAU,OAAA,CAAQ,UAAU;QAEvD,MAAM,2BAAuB,yMAAA,EAAU,SAAS,2MAAA,CAAU,OAAA,CAAQ,gBAAgB;QAClF,MAAM,2BAAuB,4MAAA,EAAwB,oBAAoB;QACzE,OAAO;gBACL,gNAAA,EAAe;gBACb,iBAAiB,+MAAA;gBACjB;gBACA,SAAS,aAAa,QAAA,CAAS,QAAA,CAAS;gBACxC,gBAAgB,qBAAqB,cAAA,IAAkB,wMAAA;gBACvD,WAAW,qBAAqB,SAAA,IAAa,oMAAA;gBAC7C,WAAW,qBAAqB,SAAA,IAAa,oMAAA;gBAC7C,eAAe,WAAW,SAAA,KAAc,2MAAA,CAAU,YAAA,GAAe,WAAW,aAAA,GAAgB;YAC9F,CAAC;YACD,kBAAkB,OAAO,KAAK,iBAAA,CAAiB,YAAA,OAAA,KAAA,IAAA,SAAU,QAAA,EAAA;SAC3D;IACF;IAEA,MAAM,mBAAuC,CAAC,OAAO,CAAC,CAAA,KAAM;QAC1D,MAAM,CAAC,GAAG,aAAa,CAAA,GAAI,yBAAyB,IAAI;QACxD,OAAO,EAAE,gBAAA,CAAiB;YACxB;QACF,CAAC;IACH;IAEA,MAAM,mBAAuC,CAAC,OAAO,CAAC,CAAA,KAAM;QAC1D,MAAM,CAAC,GAAG,aAAa,CAAA,GAAI,yBAAyB,IAAI;QACxD,OAAO,EAAE,gBAAA,CAAiB;YACxB;QACF,CAAC;IACH;IAEA,IAAI,WAAW,SAAA,KAAc,2MAAA,CAAU,YAAA,EAAc;QACnD,OAAO,OAAO,MAAA,CAAO,YAAY;YAAE;YAAkB;QAAiB,CAAC;IACzE;IAEA,OAAO;AACT;AAEA,KAAK,OAAA,GAAU,OAAA,GAAU,SAAgB;IA5LzC,IAAA,IAAA;;IAgME,MAAM,UAAU,UAAM,sNAAA,CAAiB;IACvC,MAAM,iBAAA,CAAA,CAAiB,KAAA,QAAA,OAAA,KAAA,IAAA,IAAA,CAAO,EAAA,KAAP,OAAA,KAAA,IAAA,GAAW,KAAA,KAAA,CAAA,CAAS,KAAA,QAAA,OAAA,KAAA,IAAA,IAAA,CAAO,EAAA,KAAP,OAAA,KAAA,IAAA,GAAW,KAAA,KAAS,2MAAA,CAAU,YAAA;IACzE,MAAM,aAAa,MAAM,KAAK;QAAE,cAAc;IAAe,CAAC;IAE9D,MAAM,cAAU,oMAAA,EAAc;QAC5B;QACA;QACA,kBAAkB,WAAW,gBAAA;kBAC7B,+MAAA;kBACA,+MAAA;sBACA,sMAAA;IACF,CAAC;IAED,OAAO,QAAQ,GAAG,IAAI;AACxB", "debugId": null}}, {"offset": {"line": 1800, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/server/createClerkClient.ts"], "sourcesContent": ["import { createClerkClient } from '@clerk/backend';\n\nimport {\n  API_URL,\n  API_VERSION,\n  DOMAIN,\n  IS_SATELLITE,\n  MACHINE_SECRET_KEY,\n  PROXY_URL,\n  PUBLISHABLE_KEY,\n  SDK_METADATA,\n  SECRET_KEY,\n  TELEMETRY_DEBUG,\n  TELEMETRY_DISABLED,\n} from './constants';\n\nconst clerkClientDefaultOptions = {\n  secretKey: SECRET_KEY,\n  publishableKey: PUBLISHABLE_KEY,\n  apiUrl: API_URL,\n  apiVersion: API_VERSION,\n  userAgent: `${PACKAGE_NAME}@${PACKAGE_VERSION}`,\n  proxyUrl: PROXY_URL,\n  domain: DOMAIN,\n  isSatellite: IS_SATELLITE,\n  machineSecretKey: MACHINE_SECRET_KEY,\n  sdkMetadata: SDK_METADATA,\n  telemetry: {\n    disabled: TELEMETRY_DISABLED,\n    debug: TELEMETRY_DEBUG,\n  },\n};\n\nexport const createClerkClientWithOptions: typeof createClerkClient = options =>\n  createClerkClient({ ...clerkClientDefaultOptions, ...options });\n"], "names": [], "mappings": ";;;;AAAA,SAAS,yBAAyB;AAElC;;;;AAcA,MAAM,4BAA4B;IAChC,WAAW,mMAAA;IACX,gBAAgB,wMAAA;IAChB,QAAQ,gMAAA;IACR,YAAY,oMAAA;IACZ,WAAW,GAAG,eAAY,CAAA,CAAA,EAAI,QAAe,EAAA;IAC7C,UAAU,kMAAA;IACV,QAAQ,+LAAA;IACR,aAAa,qMAAA;IACb,kBAAkB,2MAAA;IAClB,aAAa,qMAAA;IACb,WAAW;QACT,UAAU,2MAAA;QACV,OAAO,wMAAA;IACT;AACF;AAEO,MAAM,+BAAyD,CAAA,cACpE,uLAAA,EAAkB;QAAE,GAAG,yBAAA;QAA2B,GAAG,OAAA;IAAQ,CAAC", "debugId": null}}, {"offset": {"line": 1835, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/server/middleware-storage.ts"], "sourcesContent": ["import { AsyncLocalStorage } from 'node:async_hooks';\n\nimport type { AuthenticateRequestOptions } from '@clerk/backend/internal';\n\nexport const clerkMiddlewareRequestDataStore = new Map<'requestData', AuthenticateRequestOptions>();\nexport const clerkMiddlewareRequestDataStorage = new AsyncLocalStorage<typeof clerkMiddlewareRequestDataStore>();\n"], "names": [], "mappings": ";;;;;;AAAA,SAAS,yBAAyB;;;AAI3B,MAAM,kCAAkC,aAAA,GAAA,IAAI,IAA+C;AAC3F,MAAM,oCAAoC,IAAI,oJAAA,CAA0D", "debugId": null}}, {"offset": {"line": 1852, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/server/clerkClient.ts"], "sourcesContent": ["import { constants } from '@clerk/backend/internal';\n\nimport { buildRequestLike, isPrerenderingBailout } from '../app-router/server/utils';\nimport { createClerkClientWithOptions } from './createClerkClient';\nimport { getHeader } from './headers-utils';\nimport { clerkMiddlewareRequestDataStorage } from './middleware-storage';\nimport { decryptClerkRequestData } from './utils';\n\n/**\n * Constructs a BAPI client that accesses request data within the runtime.\n * Necessary if middleware dynamic keys are used.\n */\nconst clerkClient = async () => {\n  let requestData;\n\n  try {\n    const request = await buildRequestLike();\n    const encryptedRequestData = getHeader(request, constants.Headers.ClerkRequestData);\n    requestData = decryptClerkRequestData(encryptedRequestData);\n  } catch (err) {\n    if (err && isPrerenderingBailout(err)) {\n      throw err;\n    }\n  }\n\n  // Fallbacks between options from middleware runtime and `NextRequest` from application server\n  const options = clerkMiddlewareRequestDataStorage.getStore()?.get('requestData') ?? requestData;\n  if (options?.secretKey || options?.publishableKey) {\n    return createClerkClientWithOptions(options);\n  }\n\n  return createClerkClientWithOptions({});\n};\n\nexport { clerkClient };\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,iBAAiB;AAE1B,SAAS,kBAAkB,6BAA6B;AACxD,SAAS,oCAAoC;AAC7C,SAAS,iBAAiB;AAC1B,SAAS,yCAAyC;AAClD,SAAS,+BAA+B;;;;;;;;AAMxC,MAAM,cAAc,YAAY;IAZhC,IAAA,IAAA;IAaE,IAAI;IAEJ,IAAI;QACF,MAAM,UAAU,UAAM,sNAAA,CAAiB;QACvC,MAAM,2BAAuB,yMAAA,EAAU,SAAS,2MAAA,CAAU,OAAA,CAAQ,gBAAgB;QAClF,kBAAc,4MAAA,EAAwB,oBAAoB;IAC5D,EAAA,OAAS,KAAK;QACZ,IAAI,WAAO,2NAAA,EAAsB,GAAG,GAAG;YACrC,MAAM;QACR;IACF;IAGA,MAAM,UAAA,CAAU,KAAA,CAAA,KAAA,sOAAA,CAAkC,QAAA,CAAS,CAAA,KAA3C,OAAA,KAAA,IAAA,GAA8C,GAAA,CAAI,cAAA,KAAlD,OAAA,KAAoE;IACpF,IAAA,CAAI,WAAA,OAAA,KAAA,IAAA,QAAS,SAAA,KAAA,CAAa,WAAA,OAAA,KAAA,IAAA,QAAS,cAAA,GAAgB;QACjD,WAAO,6NAAA,EAA6B,OAAO;IAC7C;IAEA,WAAO,6NAAA,EAA6B,CAAC,CAAC;AACxC", "debugId": null}}]}