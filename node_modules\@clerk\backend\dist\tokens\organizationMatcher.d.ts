import type { OrganizationSyncOptions, OrganizationSyncTarget } from './types';
export declare class OrganizationMatcher {
    private readonly organizationPattern;
    private readonly personalAccountPattern;
    constructor(options?: OrganizationSyncOptions);
    private createMatcher;
    findTarget(url: URL): OrganizationSyncTarget | null;
    private findOrganizationTarget;
    private findPersonalAccountTarget;
}
//# sourceMappingURL=organizationMatcher.d.ts.map