export declare const TokenType: {
    readonly SessionToken: "session_token";
    readonly ApiKey: "api_key";
    readonly M2MToken: "m2m_token";
    readonly OAuthToken: "oauth_token";
};
/**
 * @inline
 */
export type TokenType = (typeof TokenType)[keyof typeof TokenType];
/**
 * @inline
 */
export type SessionTokenType = typeof TokenType.SessionToken;
/**
 * @inline
 */
export type MachineTokenType = Exclude<TokenType, SessionTokenType>;
//# sourceMappingURL=tokenTypes.d.ts.map