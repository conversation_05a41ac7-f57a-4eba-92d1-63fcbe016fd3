'use client'

import { UserButton } from '@clerk/nextjs'

interface NavigationProps {
  activeTab: 'messages' | 'search' | 'friends'
  setActiveTab: (tab: 'messages' | 'search' | 'friends') => void
  pendingRequestsCount: number
}

export default function Navigation({ activeTab, setActiveTab, pendingRequestsCount }: NavigationProps) {
  return (
    <nav className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center space-x-8">
            <h1 className="text-xl font-bold text-gray-900">Messages App</h1>
            
            <div className="flex space-x-4">
              <button
                onClick={() => setActiveTab('messages')}
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  activeTab === 'messages'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Messages
              </button>
              
              <button
                onClick={() => setActiveTab('search')}
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  activeTab === 'search'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Search Users
              </button>
              
              <button
                onClick={() => setActiveTab('friends')}
                className={`px-3 py-2 rounded-md text-sm font-medium relative ${
                  activeTab === 'friends'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Friend Requests
                {pendingRequestsCount > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {pendingRequestsCount}
                  </span>
                )}
              </button>
            </div>
          </div>
          
          <div className="flex items-center">
            <UserButton afterSignOutUrl="/" />
          </div>
        </div>
      </div>
    </nav>
  )
}
