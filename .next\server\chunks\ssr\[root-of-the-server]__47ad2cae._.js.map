{"version": 3, "sources": [], "sections": [{"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": [], "sourcesContent": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/runtime/node/safe-node-apis.js"], "sourcesContent": ["/**\n * This file is used for conditional imports to mitigate bundling issues with Next.js server actions on version prior to 14.1.0.\n */\n// eslint-disable-next-line @typescript-eslint/no-require-imports\nconst { existsSync, writeFileSync, readFileSync, appendFileSync, mkdirSync, rmSync } = require('node:fs');\n// eslint-disable-next-line @typescript-eslint/no-require-imports\nconst path = require('node:path');\nconst fs = {\n  existsSync,\n  writeFileSync,\n  readFileSync,\n  appendFileSync,\n  mkdirSync,\n  rmSync,\n};\n\nconst cwd = () => process.cwd();\n\nmodule.exports = { fs, path, cwd };\n"], "names": [], "mappings": ";;;;;;AAAA,IAAA,yBAAA,IAAA,+LAAA,EAAA;IAAA,sCAAA,OAAA,EAAA,MAAA;QAIA,MAAM,EAAE,UAAA,EAAY,aAAA,EAAe,YAAA,EAAc,cAAA,EAAgB,SAAA,EAAW,MAAA,CAAO,CAAA,GAAI,QAAQ,SAAS;QAExG,MAAM,OAAO,QAAQ,WAAW;QAChC,MAAM,KAAK;YACT;YACA;YACA;YACA;YACA;YACA;QACF;QAEA,MAAM,MAAM,IAAM,QAAQ,GAAA,CAAI;QAE9B,OAAO,OAAA,GAAU;YAAE;YAAI;YAAM;QAAI;IAAA;AAAA", "debugId": null}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/server/fs/utils.ts"], "sourcesContent": ["/**\n * Attention: Only import this module when the node runtime is used.\n * We are using conditional imports to mitigate bundling issues with Next.js server actions on version prior to 14.1.0.\n */\nimport nodeRuntime from '#safe-node-apis';\n\n// Generic assertion function that acts as a proper type guard\nfunction assertNotNullable<T>(value: T, moduleName: string): asserts value is NonNullable<T> {\n  if (!value) {\n    throw new Error(`Clerk: ${moduleName} is missing. This is an internal error. Please contact Clerk's support.`);\n  }\n}\n\nconst nodeFsOrThrow = (): NonNullable<typeof nodeRuntime.fs> => {\n  assertNotNullable(nodeRuntime.fs, 'fs');\n  return nodeRuntime.fs;\n};\n\nconst nodePathOrThrow = (): NonNullable<typeof nodeRuntime.path> => {\n  assertNotNullable(nodeRuntime.path, 'path');\n  return nodeRuntime.path;\n};\n\nconst nodeCwdOrThrow = (): NonNullable<typeof nodeRuntime.cwd> => {\n  assertNotNullable(nodeRuntime.cwd, 'cwd');\n  return nodeRuntime.cwd;\n};\n\nexport { nodeCwdOrThrow, nodeFsOrThrow, nodePathOrThrow };\n"], "names": [], "mappings": ";;;;;;;;AAIA,OAAO,iBAAiB;;;AAGxB,SAAS,kBAAqB,KAAA,EAAU,UAAA,EAAqD;IAC3F,IAAI,CAAC,OAAO;QACV,MAAM,IAAI,MAAM,CAAA,OAAA,EAAU,UAAU,CAAA,uEAAA,CAAyE;IAC/G;AACF;AAEA,MAAM,gBAAgB,MAA0C;IAC9D,kBAAkB,kNAAA,CAAY,EAAA,EAAI,IAAI;IACtC,OAAO,kNAAA,CAAY,EAAA;AACrB;AAEA,MAAM,kBAAkB,MAA4C;IAClE,kBAAkB,kNAAA,CAAY,IAAA,EAAM,MAAM;IAC1C,OAAO,kNAAA,CAAY,IAAA;AACrB;AAEA,MAAM,iBAAiB,MAA2C;IAChE,kBAAkB,kNAAA,CAAY,GAAA,EAAK,KAAK;IACxC,OAAO,kNAAA,CAAY,GAAA;AACrB", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/node_modules/%40clerk/nextjs/src/server/keyless-telemetry.ts"], "sourcesContent": ["import type { TelemetryEventRaw } from '@clerk/types';\nimport { dirname, join } from 'path';\n\nimport { canUseKeyless } from '../utils/feature-flags';\nimport { createClerkClientWithOptions } from './createClerkClient';\nimport { nodeFsOrThrow } from './fs/utils';\n\nconst EVENT_KEYLESS_ENV_DRIFT_DETECTED = 'KEYLESS_ENV_DRIFT_DETECTED';\nconst EVENT_SAMPLING_RATE = 1; // 100% sampling rate\nconst TELEMETRY_FLAG_FILE = '.clerk/.tmp/telemetry.json';\n\ntype EventKeylessEnvDriftPayload = {\n  publicKeyMatch: boolean;\n  secretKeyMatch: boolean;\n  envVarsMissing: boolean;\n  keylessFileHasKeys: boolean;\n  keylessPublishableKey: string;\n  envPublishableKey: string;\n};\n\n/**\n * Gets the absolute path to the telemetry flag file.\n *\n * This file is used to track whether telemetry events have already been fired\n * to prevent duplicate event reporting during the application lifecycle.\n *\n * @returns The absolute path to the telemetry flag file in the project's .clerk/.tmp directory\n */\nfunction getTelemetryFlagFilePath(): string {\n  return join(process.cwd(), TELEMETRY_FLAG_FILE);\n}\n\n/**\n * Attempts to create a telemetry flag file to mark that a telemetry event has been fired.\n *\n * This function uses the 'wx' flag to create the file atomically - it will only succeed\n * if the file doesn't already exist. This ensures that telemetry events are only fired\n * once per application lifecycle, preventing duplicate event reporting.\n *\n * @returns Promise<boolean> - Returns true if the flag file was successfully created (meaning\n *   the event should be fired), false if the file already exists (meaning the event was\n *   already fired) or if there was an error creating the file\n */\nfunction tryMarkTelemetryEventAsFired(): boolean {\n  try {\n    if (canUseKeyless) {\n      const { mkdirSync, writeFileSync } = nodeFsOrThrow();\n      const flagFilePath = getTelemetryFlagFilePath();\n      const flagDirectory = dirname(flagFilePath);\n\n      // Ensure the directory exists before attempting to write the file\n      mkdirSync(flagDirectory, { recursive: true });\n\n      const flagData = {\n        firedAt: new Date().toISOString(),\n        event: EVENT_KEYLESS_ENV_DRIFT_DETECTED,\n      };\n      writeFileSync(flagFilePath, JSON.stringify(flagData, null, 2), { flag: 'wx' });\n      return true;\n    } else {\n      return false;\n    }\n  } catch (error: unknown) {\n    if ((error as { code?: string })?.code === 'EEXIST') {\n      return false;\n    }\n    console.warn('Failed to create telemetry flag file:', error);\n    return false;\n  }\n}\n\n/**\n * Detects and reports environment drift between keyless configuration and environment variables.\n *\n * This function compares the Clerk keys stored in the keyless configuration file (.clerk/clerk.json)\n * with the keys set in environment variables (NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY and CLERK_SECRET_KEY).\n * It only reports drift when there's an actual mismatch between existing keys, not when keys are simply missing.\n *\n * The function handles several scenarios and only reports drift in specific cases:\n * - **Normal keyless mode**: env vars missing but keyless file has keys → no drift (expected)\n * - **No configuration**: neither env vars nor keyless file have keys → no drift (nothing to compare)\n * - **Actual drift**: env vars exist and don't match keyless file keys → drift detected\n * - **Empty keyless file**: keyless file exists but has no keys → no drift (nothing to compare)\n *\n * Drift is only detected when:\n * 1. Both environment variables and keyless file contain keys\n * 2. The keys in environment variables don't match the keys in the keyless file\n *\n * Telemetry events are only fired once per application lifecycle using a flag file mechanism\n * to prevent duplicate reporting.\n *\n * @returns Promise<void> - Function completes silently, errors are logged but don't throw\n */\nexport async function detectKeylessEnvDrift(): Promise<void> {\n  if (!canUseKeyless) {\n    return;\n  }\n  // Only run on server side\n  if (typeof window !== 'undefined') {\n    return;\n  }\n\n  try {\n    // Dynamically import server-side dependencies to avoid client-side issues\n    const { safeParseClerkFile } = await import('./keyless-node.js');\n\n    // Read the keyless configuration file\n    const keylessFile = safeParseClerkFile();\n\n    if (!keylessFile) {\n      return;\n    }\n\n    // Get environment variables\n    const envPublishableKey = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY;\n    const envSecretKey = process.env.CLERK_SECRET_KEY;\n\n    // Check the state of environment variables and keyless file\n    const hasEnvVars = Boolean(envPublishableKey || envSecretKey);\n    const keylessFileHasKeys = Boolean(keylessFile?.publishableKey && keylessFile?.secretKey);\n    const envVarsMissing = !envPublishableKey && !envSecretKey;\n\n    // Early return conditions - no drift to detect in these scenarios:\n    if (!hasEnvVars && !keylessFileHasKeys) {\n      // Neither env vars nor keyless file have keys - nothing to compare\n      return;\n    }\n\n    if (envVarsMissing && keylessFileHasKeys) {\n      // Environment variables are missing but keyless file has keys - this is normal for keyless mode\n      return;\n    }\n\n    if (!keylessFileHasKeys) {\n      // Keyless file doesn't have keys, so no drift can be detected\n      return;\n    }\n\n    // Only proceed with drift detection if we have something meaningful to compare\n    if (!hasEnvVars) {\n      return;\n    }\n\n    // Compare keys only when both sides have values to compare\n    const publicKeyMatch = Boolean(\n      envPublishableKey && keylessFile.publishableKey && envPublishableKey === keylessFile.publishableKey,\n    );\n\n    const secretKeyMatch = Boolean(envSecretKey && keylessFile.secretKey && envSecretKey === keylessFile.secretKey);\n\n    // Determine if there's an actual drift:\n    // Drift occurs when we have env vars that don't match the keyless file keys\n    const hasActualDrift =\n      (envPublishableKey && keylessFile.publishableKey && !publicKeyMatch) ||\n      (envSecretKey && keylessFile.secretKey && !secretKeyMatch);\n\n    // Only fire telemetry if there's an actual drift (not just missing keys)\n    if (!hasActualDrift) {\n      return;\n    }\n\n    const payload: EventKeylessEnvDriftPayload = {\n      publicKeyMatch,\n      secretKeyMatch,\n      envVarsMissing,\n      keylessFileHasKeys,\n      keylessPublishableKey: keylessFile.publishableKey ?? '',\n      envPublishableKey: envPublishableKey ?? '',\n    };\n\n    // Create a clerk client to access telemetry\n    const clerkClient = createClerkClientWithOptions({\n      publishableKey: keylessFile.publishableKey,\n      secretKey: keylessFile.secretKey,\n      telemetry: {\n        samplingRate: 1,\n      },\n    });\n\n    const shouldFireEvent = tryMarkTelemetryEventAsFired();\n\n    if (shouldFireEvent) {\n      // Fire drift detected event only if we successfully created the flag\n      const driftDetectedEvent: TelemetryEventRaw<EventKeylessEnvDriftPayload> = {\n        event: EVENT_KEYLESS_ENV_DRIFT_DETECTED,\n        eventSamplingRate: EVENT_SAMPLING_RATE,\n        payload,\n      };\n\n      clerkClient.telemetry?.record(driftDetectedEvent);\n    }\n  } catch (error) {\n    // Silently handle errors to avoid breaking the application\n    console.warn('Failed to detect keyless environment drift:', error);\n  }\n}\n"], "names": [], "mappings": ";;;;AACA,SAAS,SAAS,YAAY;AAE9B,SAAS,qBAAqB;AAC9B,SAAS,oCAAoC;AAC7C,SAAS,qBAAqB;;;;;;AAE9B,MAAM,mCAAmC;AACzC,MAAM,sBAAsB;AAC5B,MAAM,sBAAsB;AAmB5B,SAAS,2BAAmC;IAC1C,WAAO,yGAAA,EAAK,QAAQ,GAAA,CAAI,GAAG,mBAAmB;AAChD;AAaA,SAAS,+BAAwC;IAC/C,IAAI;QACF,IAAI,0MAAA,EAAe;YACjB,MAAM,EAAE,SAAA,EAAW,aAAA,CAAc,CAAA,OAAI,sMAAA,CAAc;YACnD,MAAM,eAAe,yBAAyB;YAC9C,MAAM,oBAAgB,4GAAA,EAAQ,YAAY;YAG1C,UAAU,eAAe;gBAAE,WAAW;YAAK,CAAC;YAE5C,MAAM,WAAW;gBACf,SAAA,AAAS,aAAA,GAAA,IAAI,KAAK,EAAE,WAAA,CAAY;gBAChC,OAAO;YACT;YACA,cAAc,cAAc,KAAK,SAAA,CAAU,UAAU,MAAM,CAAC,GAAG;gBAAE,MAAM;YAAK,CAAC;YAC7E,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF,EAAA,OAAS,OAAgB;QACvB,IAAA,CAAK,SAAA,OAAA,KAAA,IAAA,MAA6B,IAAA,MAAS,UAAU;YACnD,OAAO;QACT;QACA,QAAQ,IAAA,CAAK,yCAAyC,KAAK;QAC3D,OAAO;IACT;AACF;AAwBA,eAAsB,wBAAuC;IA7F7D,IAAA,IAAA;IA8FE,IAAI,CAAC,0MAAA,EAAe;QAClB;IACF;IAEA,IAAI,OAAO,WAAW,aAAa;;IAInC,IAAI;QAEF,MAAM,EAAE,kBAAA,CAAmB,CAAA,GAAI,MAAM,OAAO,mBAAmB;QAG/D,MAAM,cAAc,mBAAmB;QAEvC,IAAI,CAAC,aAAa;YAChB;QACF;QAGA,MAAM,oBAAoB,QAAQ,IAAI;QACtC,MAAM,eAAe,QAAQ,GAAA,CAAI,gBAAA;QAGjC,MAAM,aAAa,QAAQ,qBAAqB,YAAY;QAC5D,MAAM,qBAAqB,QAAA,CAAQ,eAAA,OAAA,KAAA,IAAA,YAAa,cAAA,KAAA,CAAkB,eAAA,OAAA,KAAA,IAAA,YAAa,SAAA,CAAS;QACxF,MAAM,iBAAiB,CAAC,qBAAqB,CAAC;QAG9C,IAAI,CAAC,cAAc,CAAC,oBAAoB;YAEtC;QACF;QAEA,IAAI,kBAAkB,oBAAoB;;QAK1C,IAAI,CAAC,oBAAoB;YAEvB;QACF;QAGA,IAAI,CAAC,YAAY;YACf;QACF;QAGA,MAAM,iBAAiB,QACrB,qBAAqB,YAAY,cAAA,IAAkB,sBAAsB,YAAY,cAAA;QAGvF,MAAM,iBAAiB,QAAQ,gBAAgB,YAAY,SAAA,IAAa,iBAAiB,YAAY,SAAS;QAI9G,MAAM,iBACH,qBAAqB,YAAY,cAAA,IAAkB,CAAC,kBACpD,gBAAgB,YAAY,SAAA,IAAa,CAAC;QAG7C,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,MAAM,UAAuC;YAC3C;YACA;YACA;YACA;YACA,uBAAA,CAAuB,KAAA,YAAY,cAAA,KAAZ,OAAA,KAA8B;YACrD,mBAAmB,qBAAA,OAAA,oBAAqB;QAC1C;QAGA,MAAM,kBAAc,2NAAA,EAA6B;YAC/C,gBAAgB,YAAY,cAAA;YAC5B,WAAW,YAAY,SAAA;YACvB,WAAW;gBACT,cAAc;YAChB;QACF,CAAC;QAED,MAAM,kBAAkB,6BAA6B;QAErD,IAAI,iBAAiB;YAEnB,MAAM,qBAAqE;gBACzE,OAAO;gBACP,mBAAmB;gBACnB;YACF;YAEA,CAAA,KAAA,YAAY,SAAA,KAAZ,OAAA,KAAA,IAAA,GAAuB,MAAA,CAAO;QAChC;IACF,EAAA,OAAS,OAAO;QAEd,QAAQ,IAAA,CAAK,+CAA+C,KAAK;IACnE;AACF", "debugId": null}}]}