import { User, Message, FriendRequest, Conversation, AppState } from './types';

const STORAGE_KEYS = {
  USERS: 'messaging_app_users',
  MESSAGES: 'messaging_app_messages',
  FRIEND_REQUESTS: 'messaging_app_friend_requests',
  CONVERSATIONS: 'messaging_app_conversations',
};

// Initialize storage with some demo data
const initializeStorage = () => {
  if (typeof window === 'undefined') return;
  
  // Initialize users if not exists
  if (!localStorage.getItem(STORAGE_KEYS.USERS)) {
    const demoUsers: User[] = [
      {
        id: 'demo1',
        username: 'alice_wonder',
        firstName: 'Alice',
        lastName: 'Wonder',
        imageUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
        emailAddress: '<EMAIL>'
      },
      {
        id: 'demo2',
        username: 'bob_builder',
        firstName: 'Bob',
        lastName: 'Builder',
        imageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
        emailAddress: '<EMAIL>'
      },
      {
        id: 'demo3',
        username: 'charlie_brown',
        firstName: 'Charlie',
        lastName: 'Brown',
        imageUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
        emailAddress: '<EMAIL>'
      }
    ];
    localStorage.setItem(STORAGE_KEYS.USERS, JSON.stringify(demoUsers));
  }
  
  // Initialize other storage items
  if (!localStorage.getItem(STORAGE_KEYS.MESSAGES)) {
    localStorage.setItem(STORAGE_KEYS.MESSAGES, JSON.stringify([]));
  }
  if (!localStorage.getItem(STORAGE_KEYS.FRIEND_REQUESTS)) {
    localStorage.setItem(STORAGE_KEYS.FRIEND_REQUESTS, JSON.stringify([]));
  }
  if (!localStorage.getItem(STORAGE_KEYS.CONVERSATIONS)) {
    localStorage.setItem(STORAGE_KEYS.CONVERSATIONS, JSON.stringify([]));
  }
};

export const storageUtils = {
  // Users
  getUsers: (): User[] => {
    if (typeof window === 'undefined') return [];
    initializeStorage();
    const users = localStorage.getItem(STORAGE_KEYS.USERS);
    return users ? JSON.parse(users) : [];
  },

  addUser: (user: User): void => {
    if (typeof window === 'undefined') return;
    const users = storageUtils.getUsers();
    const existingUser = users.find(u => u.id === user.id);
    if (!existingUser) {
      users.push(user);
      localStorage.setItem(STORAGE_KEYS.USERS, JSON.stringify(users));
    }
  },

  // Messages
  getMessages: (): Message[] => {
    if (typeof window === 'undefined') return [];
    const messages = localStorage.getItem(STORAGE_KEYS.MESSAGES);
    return messages ? JSON.parse(messages) : [];
  },

  addMessage: (message: Message): void => {
    if (typeof window === 'undefined') return;
    const messages = storageUtils.getMessages();
    messages.push(message);
    localStorage.setItem(STORAGE_KEYS.MESSAGES, JSON.stringify(messages));
  },

  getConversationMessages: (userId1: string, userId2: string): Message[] => {
    const messages = storageUtils.getMessages();
    return messages.filter(
      msg => 
        (msg.senderId === userId1 && msg.receiverId === userId2) ||
        (msg.senderId === userId2 && msg.receiverId === userId1)
    ).sort((a, b) => a.timestamp - b.timestamp);
  },

  // Friend Requests
  getFriendRequests: (): FriendRequest[] => {
    if (typeof window === 'undefined') return [];
    const requests = localStorage.getItem(STORAGE_KEYS.FRIEND_REQUESTS);
    return requests ? JSON.parse(requests) : [];
  },

  addFriendRequest: (request: FriendRequest): void => {
    if (typeof window === 'undefined') return;
    const requests = storageUtils.getFriendRequests();
    requests.push(request);
    localStorage.setItem(STORAGE_KEYS.FRIEND_REQUESTS, JSON.stringify(requests));
  },

  updateFriendRequest: (requestId: string, status: 'accepted' | 'rejected'): void => {
    if (typeof window === 'undefined') return;
    const requests = storageUtils.getFriendRequests();
    const requestIndex = requests.findIndex(r => r.id === requestId);
    if (requestIndex !== -1) {
      requests[requestIndex].status = status;
      localStorage.setItem(STORAGE_KEYS.FRIEND_REQUESTS, JSON.stringify(requests));
    }
  },

  // Conversations
  getConversations: (): Conversation[] => {
    if (typeof window === 'undefined') return [];
    const conversations = localStorage.getItem(STORAGE_KEYS.CONVERSATIONS);
    return conversations ? JSON.parse(conversations) : [];
  },

  updateConversation: (conversation: Conversation): void => {
    if (typeof window === 'undefined') return;
    const conversations = storageUtils.getConversations();
    const existingIndex = conversations.findIndex(c => c.id === conversation.id);
    if (existingIndex !== -1) {
      conversations[existingIndex] = conversation;
    } else {
      conversations.push(conversation);
    }
    localStorage.setItem(STORAGE_KEYS.CONVERSATIONS, JSON.stringify(conversations));
  },

  // Search users
  searchUsers: (query: string): User[] => {
    const users = storageUtils.getUsers();
    return users.filter(user => 
      user.username.toLowerCase().includes(query.toLowerCase()) ||
      user.firstName?.toLowerCase().includes(query.toLowerCase()) ||
      user.lastName?.toLowerCase().includes(query.toLowerCase())
    );
  }
};

// Initialize storage on import
if (typeof window !== 'undefined') {
  initializeStorage();
}
