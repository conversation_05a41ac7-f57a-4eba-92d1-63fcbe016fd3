var R=require("../../chunks/ssr/[turbopack]_runtime.js")("server/app/test/page.js")
R.c("server/chunks/ssr/65b60_90b50bf2._.js")
R.c("server/chunks/ssr/[root-of-the-server]__e8a2741f._.js")
R.c("server/chunks/ssr/messages_app_3b30f80d._.js")
R.c("server/chunks/ssr/65b60_c7e4d189._.js")
R.c("server/chunks/ssr/65b60_767bc167._.js")
R.c("server/chunks/ssr/[root-of-the-server]__f06af05b._.js")
R.c("server/chunks/ssr/65b60_next_dist_client_components_abe38d49._.js")
R.c("server/chunks/ssr/65b60_next_dist_client_components_builtin_forbidden_db46d823.js")
R.c("server/chunks/ssr/65b60_next_dist_client_components_builtin_unauthorized_6e1405e2.js")
R.c("server/chunks/ssr/65b60_next_dist_client_components_builtin_global-error_2860d9dd.js")
R.c("server/chunks/ssr/messages_23128464._.js")
R.c("server/chunks/ssr/[root-of-the-server]__7673dc75._.js")
R.m("[project]/messages/.next-internal/server/app/test/page/actions.js { ACTIONS_MODULE0 => \"[project]/messages/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/messages/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/messages/node_modules/next/dist/esm/build/templates/app-page.js?page=/test/page { GLOBAL_ERROR_MODULE => \"[project]/messages/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/messages/app/favicon.ico.mjs { IMAGE => \\\"[project]/messages/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/messages/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/messages/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/messages/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/messages/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/messages/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/messages/app/test/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)")
module.exports=R.m("[project]/messages/node_modules/next/dist/esm/build/templates/app-page.js?page=/test/page { GLOBAL_ERROR_MODULE => \"[project]/messages/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/messages/app/favicon.ico.mjs { IMAGE => \\\"[project]/messages/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/messages/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/messages/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/messages/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/messages/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/messages/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/messages/app/test/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)").exports
