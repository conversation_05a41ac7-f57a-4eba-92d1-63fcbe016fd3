{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/messages/app/api/users/%5Bid%5D/route.ts"], "sourcesContent": ["import { clerkClient } from '@clerk/nextjs/server'\nimport { auth } from '@clerk/nextjs/server'\nimport { NextResponse } from 'next/server'\n\nexport async function GET(\n  request: Request,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const { userId } = await auth()\n    \n    if (!userId) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const user = await clerkClient.users.getUser(params.id)\n\n    const userData = {\n      id: user.id,\n      username: user.username || user.emailAddresses[0]?.emailAddress?.split('@')[0] || 'user',\n      firstName: user.firstName || '',\n      lastName: user.lastName || '',\n      imageUrl: user.imageUrl,\n      emailAddress: user.emailAddresses[0]?.emailAddress || ''\n    }\n\n    return NextResponse.json({ user: userData })\n  } catch (error) {\n    console.error('Error fetching user:', error)\n    return NextResponse.json({ error: 'User not found' }, { status: 404 })\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEO,eAAe,IACpB,OAAgB,EAChB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAA,yMAAI;QAE7B,IAAI,CAAC,QAAQ;YACX,OAAO,4JAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,OAAO,MAAM,sMAAW,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE;QAEtD,MAAM,WAAW;YACf,IAAI,KAAK,EAAE;YACX,UAAU,KAAK,QAAQ,IAAI,KAAK,cAAc,CAAC,EAAE,EAAE,cAAc,MAAM,IAAI,CAAC,EAAE,IAAI;YAClF,WAAW,KAAK,SAAS,IAAI;YAC7B,UAAU,KAAK,QAAQ,IAAI;YAC3B,UAAU,KAAK,QAAQ;YACvB,cAAc,KAAK,cAAc,CAAC,EAAE,EAAE,gBAAgB;QACxD;QAEA,OAAO,4JAAY,CAAC,IAAI,CAAC;YAAE,MAAM;QAAS;IAC5C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,4JAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAiB,GAAG;YAAE,QAAQ;QAAI;IACtE;AACF", "debugId": null}}]}